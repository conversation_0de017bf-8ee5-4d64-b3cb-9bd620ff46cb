# SMSHub App

SMSHub App is a Python Flask application designed to manage SMS communications via multiple cellular modems, integrate with the SMSHub API, and provide a dashboard for monitoring and control.

## Prerequisites

*   Python 3.8+
*   pip (Python package installer)
*   Git (for cloning, optional if you have the files)

## Setup

1.  **Clone the repository (if applicable) or ensure you have the project files.**

2.  **Navigate to the project root directory:**
    ```bash
    cd path/to/sms-app-1/smshub_app
    ```
    (Or simply `cd smshub_app` if you are already in `sms-app-1`)

3.  **Create a Python virtual environment (recommended):**
    ```bash
    python -m venv venv
    ```

4.  **Activate the virtual environment:**
    *   Windows:
        ```bash
        .\venv\Scripts\activate
        ```
    *   macOS/Linux:
        ```bash
        source venv/bin/activate
        ```

5.  **Install required packages:**
    A `requirements.txt` file is provided. Install the dependencies using:
    ```bash
    pip install -r ../requirements.txt
    ```
    (Ensure you are in the `smshub_app` directory when running this, or adjust the path to `requirements.txt` if you run it from the project root `sms-app-1`, in which case it would be `pip install -r requirements.txt`.)
    The key dependencies are `Flask` and `pyserial`.

## Configuration

1.  **Copy the example configuration (if one exists) or review `config.json`:**
    The application uses `config.json` located in the `smshub_app` directory for its settings.

2.  **Edit `config.json`:**
    *   Update `smshub_api_key` with your actual SMSHub API key.
    *   The application now attempts to auto-detect modems. However, you might want to review `default_modem_baudrate` and `default_modem_timeout` if you encounter issues with modem communication.
    *   Adjust `log_level`, `app_host`, and `app_port` as needed.

## Running the Application

1.  **Ensure your virtual environment is activated.**

2.  **Navigate to the parent directory of `smshub_app`** (i.e., `d:\coding\sms app 1` in your case, if you are running the command from there). The command needs to be run from a context where `smshub_app` is a discoverable package/module.

3.  **Run the Flask application using the following command from the project root (`d:\coding\sms app 1`):**
    ```bash
    python -m flask --app smshub_app.app.main:app run --host=0.0.0.0 --port=5001
    ```
    *   `--app smshub_app.app.main:app`: Specifies the location of the Flask app instance (`app`) within your project structure.
    *   `--host=0.0.0.0`: Makes the server accessible from other devices on your network (uses `127.0.0.1` for local access).
    *   `--port=5001`: Specifies the port the application will run on.

4.  **If you want to enable Flask's debug mode (recommended for development for auto-reloading and better error pages), use:**
    ```bash
    python -m flask --app smshub_app.app.main:app --debug run --host=0.0.0.0 --port=5001
    ```

## Accessing the Dashboard

Once the server is running, open your web browser and navigate to:
`http://localhost:5001/dashboard`

You should see the SMSHub dashboard. Initially, it might display example modem data if no actual modems are detected or configured correctly. The server console will show logs related to modem detection.