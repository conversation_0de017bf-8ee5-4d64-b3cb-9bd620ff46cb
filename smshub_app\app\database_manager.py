import sqlite3
import logging
import os
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

DATABASE_NAME = "sms_database.db"
DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), DATABASE_NAME)

def get_db_connection():
    """Establishes a connection to the SQLite database."""
    from flask import current_app # Local import to avoid circular dependency if db_manager is imported early
    
    path_to_use = DATABASE_PATH # Default global path
    if current_app and current_app.config.get('DATABASE_PATH'): # Check if key exists and has a value
        path_to_use = current_app.config['DATABASE_PATH']
        logger.debug(f"get_db_connection using DATABASE_PATH from app.config: {path_to_use}")
    else:
        logger.debug(f"get_db_connection using global DATABASE_PATH: {path_to_use}")

    conn = None
    try:
        conn = sqlite3.connect(path_to_use)
        conn.row_factory = sqlite3.Row
    except sqlite3.Error as e:
        logger.error(f"Error connecting to database {path_to_use}: {e}")
        raise 
    return conn

def init_db():
    """Initializes the database and creates tables if they don't exist."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Determine the path that get_db_connection will use.
        db_path_for_check = DATABASE_PATH # Default
        from flask import current_app
        if current_app and current_app.config.get('DATABASE_PATH'):
            db_path_for_check = current_app.config['DATABASE_PATH']

        # If using in-memory database for testing, drop tables first for a clean state
        if db_path_for_check == ':memory:':
            logger.info("In-memory database detected for init_db. Dropping existing tables for a clean test state.")
            cursor.execute("DROP TABLE IF EXISTS sms_messages")
            cursor.execute("DROP TABLE IF EXISTS activations")
            conn.commit() # Commit the drops before recreating

        # SMS Messages Table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS sms_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            modem_port TEXT NOT NULL,
            modem_message_index INTEGER,
            sender TEXT,
            timestamp_device TEXT, 
            timestamp_received_app TEXT NOT NULL,
            message_body TEXT,
            status_on_modem TEXT, 
            is_read_in_app INTEGER DEFAULT 0, 
            forwarding_status TEXT DEFAULT 'PENDING', 
            forwarding_attempts INTEGER DEFAULT 0,
            last_forwarding_error TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP, 
            UNIQUE(modem_port, sender, timestamp_device, message_body)
        )
        """)
        logger.info("Table 'sms_messages' checked/created.")
        
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sms_modem_port ON sms_messages (modem_port);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sms_timestamp_received_app ON sms_messages (timestamp_received_app);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sms_forwarding_status ON sms_messages (forwarding_status);")
        logger.info("Indexes for 'sms_messages' checked/created.")

        # Activations Table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS activations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            modem_port TEXT NOT NULL,
            phone_number TEXT NOT NULL,
            service TEXT NOT NULL,
            country TEXT,
            operator TEXT,
            smshub_reported_status INTEGER, -- 1, 3, 4, 5 from SMSHub
            status_in_our_system TEXT NOT NULL DEFAULT 'PENDING_ISSUE', -- e.g., PENDING_ISSUE, PENDING_SMS, SMS_RECEIVED, COMPLETED, CANCELLED, FAILED
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            expires_at TEXT -- ISO8601, e.g., for 20 min expiry
        )
        """)
        logger.info("Table 'activations' checked/created.")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activations_status_ours ON activations (status_in_our_system);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activations_expires_at ON activations (expires_at);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activations_modem_service ON activations (modem_port, service, status_in_our_system);")
        logger.info("Indexes for 'activations' checked/created.")

        conn.commit()
        logger.info(f"Database initialized successfully at {DATABASE_PATH}.")
    except sqlite3.Error as e:
        logger.error(f"Error initializing database: {e}")
    finally:
        if conn:
            conn.close()

def add_message(message_data: dict) -> Optional[int]:
    required_keys = ["modem_port", "sender", "timestamp_device", "timestamp_received_app", "message_body", "status_on_modem"]
    for key in required_keys:
        if key not in message_data:
            logger.error(f"Missing required key '{key}' in message_data for add_message.")
            return None
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            INSERT OR IGNORE INTO sms_messages (
                modem_port, modem_message_index, sender, timestamp_device, 
                timestamp_received_app, message_body, status_on_modem, 
                is_read_in_app, forwarding_status, forwarding_attempts, last_forwarding_error
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            message_data.get("modem_port"), message_data.get("modem_message_index"),
            message_data.get("sender"), message_data.get("timestamp_device"),
            message_data.get("timestamp_received_app"), message_data.get("message_body"),
            message_data.get("status_on_modem"), message_data.get("is_read_in_app", 0),
            message_data.get("forwarding_status", "PENDING"), message_data.get("forwarding_attempts", 0),
            message_data.get("last_forwarding_error")
        ))
        conn.commit()
        if cursor.lastrowid is not None and cursor.lastrowid > 0:
            logger.info(f"Message from {message_data.get('sender')} on port {message_data.get('modem_port')} added to DB with ID {cursor.lastrowid}.")
            return cursor.lastrowid
        elif cursor.rowcount == 0: 
            logger.info(f"Message from {message_data.get('sender')} on port {message_data.get('modem_port')} likely a duplicate, not added again.")
            return None 
        else: 
            logger.warning(f"Message from {message_data.get('sender')} on port {message_data.get('modem_port')} - INSERT OR IGNORE resulted in no lastrowid and non-zero rowcount.")
            return None
    except sqlite3.Error as e:
        logger.error(f"Error adding message to database: {e}")
        if conn: conn.rollback() 
        return None
    finally:
        if conn: conn.close()

def get_all_messages(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    conn = None
    messages: List[Dict[str, Any]] = []
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, modem_port, modem_message_index, sender, timestamp_device, 
                   timestamp_received_app, message_body, status_on_modem, is_read_in_app, 
                   forwarding_status, forwarding_attempts, last_forwarding_error, created_at
            FROM sms_messages ORDER BY timestamp_received_app DESC, id DESC LIMIT ? OFFSET ?
        """, (limit, offset))
        rows = cursor.fetchall()
        for row in rows: messages.append(dict(row)) 
        logger.info(f"Retrieved {len(messages)} messages from DB (limit {limit}, offset {offset}).")
    except sqlite3.Error as e:
        logger.error(f"Error retrieving messages from database: {e}")
        return [] 
    finally:
        if conn: conn.close()
    return messages

def get_pending_forward_messages(limit: int = 10) -> List[Dict[str, Any]]:
    conn = None
    messages: List[Dict[str, Any]] = []
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, modem_port, modem_message_index, sender, timestamp_device, 
                   timestamp_received_app, message_body, status_on_modem, is_read_in_app, 
                   forwarding_status, forwarding_attempts, last_forwarding_error, created_at
            FROM sms_messages WHERE forwarding_status = 'PENDING' OR forwarding_status = 'FAILED'
            ORDER BY created_at ASC LIMIT ?
        """, (limit,))
        rows = cursor.fetchall()
        for row in rows: messages.append(dict(row))
        logger.info(f"Retrieved {len(messages)} messages pending/failed forwarding from DB (limit {limit}).")
    except sqlite3.Error as e:
        logger.error(f"Error retrieving pending/failed forwarding messages from database: {e}")
        return []
    finally:
        if conn: conn.close()
    return messages

def update_forwarding_status(message_id: int, status: str, attempts: Optional[int] = None, error_message: Optional[str] = None) -> bool:
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        update_fields: List[str] = ["forwarding_status = ?"]
        params: List[Any] = [status]
        if attempts is not None:
            update_fields.append("forwarding_attempts = ?")
            params.append(attempts)
        if error_message is not None:
            update_fields.append("last_forwarding_error = ?")
            params.append(error_message)
        elif status in ["SUCCESS", "SKIPPED"]: 
            update_fields.append("last_forwarding_error = NULL")
        params.append(message_id)
        sql = f"UPDATE sms_messages SET {', '.join(update_fields)} WHERE id = ?"
        cursor.execute(sql, tuple(params))
        conn.commit()
        if cursor.rowcount > 0:
            logger.info(f"Forwarding status for message ID {message_id} updated to {status}.")
            return True
        else:
            logger.warning(f"No message found with ID {message_id} to update forwarding status.")
            return False
    except sqlite3.Error as e:
        logger.error(f"Error updating forwarding status for message ID {message_id}: {e}")
        if conn: conn.rollback()
        return False
    finally:
        if conn: conn.close()

# --- Activation Table Functions ---

def create_activation(modem_port: str, phone_number: str, service: str, country: Optional[str], operator: Optional[str], expiry_minutes: int = 20) -> Optional[int]:
    """Creates a new activation record and returns its ID."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        now = datetime.utcnow()
        created_at_iso = now.isoformat()
        expires_at_iso = (now + timedelta(minutes=expiry_minutes)).isoformat()
        
        cursor.execute("""
            INSERT INTO activations (modem_port, phone_number, service, country, operator, 
                                     status_in_our_system, created_at, updated_at, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (modem_port, phone_number, service, country, operator, 
              'PENDING_SMS', created_at_iso, created_at_iso, expires_at_iso))
        conn.commit()
        activation_id = cursor.lastrowid
        if activation_id:
            logger.info(f"Created activation ID {activation_id} for service {service} on modem {modem_port}, number {phone_number}.")
            return activation_id
        return None
    except sqlite3.Error as e:
        logger.error(f"Error creating activation: {e}")
        if conn: conn.rollback()
        return None
    finally:
        if conn: conn.close()

def get_activation_by_id(activation_id: int) -> Optional[Dict[str, Any]]:
    """Retrieves an activation by its ID."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM activations WHERE id = ?", (activation_id,))
        row = cursor.fetchone()
        if row:
            return dict(row)
        return None
    except sqlite3.Error as e:
        logger.error(f"Error getting activation ID {activation_id}: {e}")
        return None
    finally:
        if conn: conn.close()

def update_activation_details(activation_id: int, smshub_status: Optional[int] = None, our_status: Optional[str] = None) -> bool:
    """Updates an activation's status (SMSHub reported and/or our internal status)."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        update_fields: List[str] = []
        params: List[Any] = []
        
        if smshub_status is not None:
            update_fields.append("smshub_reported_status = ?")
            params.append(smshub_status)
        if our_status is not None:
            update_fields.append("status_in_our_system = ?")
            params.append(our_status)
        
        if not update_fields:
            logger.warning(f"No fields to update for activation ID {activation_id}.")
            return False
            
        update_fields.append("updated_at = ?")
        params.append(datetime.utcnow().isoformat())
        
        params.append(activation_id) # For the WHERE clause
        
        sql = f"UPDATE activations SET {', '.join(update_fields)} WHERE id = ?"
        
        cursor.execute(sql, tuple(params))
        conn.commit()
        
        if cursor.rowcount > 0:
            logger.info(f"Activation ID {activation_id} updated. SMSHub status: {smshub_status}, Our status: {our_status}.")
            return True
        else:
            logger.warning(f"No activation found with ID {activation_id} to update.")
            return False
    except sqlite3.Error as e:
        logger.error(f"Error updating activation ID {activation_id}: {e}")
        if conn: conn.rollback()
        return False
    finally:
        if conn: conn.close()

def find_active_activation_for_modem_service(modem_port: str, service: str) -> Optional[Dict[str, Any]]:
    """Checks if a modem is currently tied to an active (e.g., PENDING_SMS) activation for a specific service."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        # Active statuses might include PENDING_ISSUE, PENDING_SMS.
        # Consider what statuses mean a modem is "busy" for a new GET_NUMBER request.
        cursor.execute("""
            SELECT * FROM activations 
            WHERE modem_port = ? AND service = ? 
            AND status_in_our_system IN ('PENDING_ISSUE', 'PENDING_SMS') 
            AND (expires_at IS NULL OR expires_at > ?)
            ORDER BY created_at DESC LIMIT 1
        """, (modem_port, service, datetime.utcnow().isoformat()))
        row = cursor.fetchone()
        return dict(row) if row else None
    except sqlite3.Error as e:
        logger.error(f"Error finding active activation for modem {modem_port}, service {service}: {e}")
        return None
    finally:
        if conn: conn.close()

def get_open_activation_for_modem(modem_port: str) -> Optional[Dict[str, Any]]:
    """
    Finds an "open" activation for a given modem that is expecting an SMS.
    An activation is considered open if its status is 'PENDING_SMS' or 'SMS_FORWARDED_AWAITING_HUB'
    and it has not expired. Returns the most recent one if multiple.
    """
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        # These are the statuses where we expect SMSHub might still need SMS messages for an activation.
        open_statuses = ('PENDING_SMS', 'SMS_FORWARDED_AWAITING_HUB')
        cursor.execute(f"""
            SELECT * FROM activations
            WHERE modem_port = ?
            AND status_in_our_system IN ({','.join(['?']*len(open_statuses))})
            AND (expires_at IS NULL OR expires_at > ?)
            ORDER BY created_at DESC LIMIT 1
        """, (modem_port, *open_statuses, datetime.utcnow().isoformat()))
        row = cursor.fetchone()
        if row:
            logger.info(f"Found open activation ID {row['id']} (status: {row['status_in_our_system']}) for modem {modem_port}, service {row['service']}.")
            return dict(row)
        logger.info(f"No open activation (statuses: {open_statuses}) found for modem {modem_port}.")
        return None
    except sqlite3.Error as e:
        logger.error(f"Error finding open activation for modem {modem_port}: {e}")
        return None
    finally:
        if conn: conn.close()

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.info(f"Attempting to initialize database at: {DATABASE_PATH}")
    init_db()