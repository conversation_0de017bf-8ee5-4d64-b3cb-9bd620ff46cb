[{"title": "os_tcp.png (2035×1076)", "url": "https://blog.tanatos.org/os_tcp.png", "html": ""}, {"title": "puppet - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/puppet/", "html": "<PERSON>’ tech & personal blog\nTags\n#puppet\n2019\nWhy Ansible? Ansible vs Puppet\nJan 23\nRendered by <PERSON> | Subscribe"}, {"title": "ansible - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/ansible/", "html": "<PERSON>’ tech & personal blog\nTags\n#ansible\n2019\nWhy Ansible? Ansible vs Puppet\nJan 23\nRendered by <PERSON> | Subscribe"}, {"title": "", "url": "https://blog.tanatos.org/index.xml", "html": "This XML file does not appear to have any style information associated with it. The document tree is shown below.\n\n<rss xmlns:atom=\"http://www.w3.org/2005/Atom\" version=\"2.0\">\n<channel>\n<title><PERSON>' tech & personal blog</title>\n<link>https://blog.tanatos.org/</link>\n<description>Recent content on <PERSON>' tech & personal blog</description>\n<generator><PERSON> -- gohugo.io</generator>\n<language>en</language>\n<managingEditor><EMAIL> (<PERSON>)</managingEditor>\n<webMaster><EMAIL> (<PERSON>)</webMaster>\n<lastBuildDate>Mon, 22 Jan 2024 00:00:00 +1300</lastBuildDate>\n<atom:link href=\"https://blog.tanatos.org/index.xml\" rel=\"self\" type=\"application/rss+xml\"/>\n<item>\n<title>Safely try new Linux kernel in Grub</title>\n<link>https://blog.tanatos.org/posts/grub_try_new_kernel/</link>\n<pubDate>Mon, 22 Jan 2024 00:00:00 +1300</pubDate>\n<author><EMAIL> (<PERSON>)</author>\n<guid>https://blog.tanatos.org/posts/grub_try_new_kernel/</guid>\n<description><p>How to safely try new Linux kernel remotely without loosing access to the server.</p><p>Install new kernel.</p><p>List kernels in Grub compatible format.</p><pre tabindex=\"0\"><code>MENUID=$( grep -E &#34;^\\\\s*(submenu) &#34; /boot/grub/grub.cfg | grep -oP &#34;gnuli.*(?=&#39;)&#34; )grep &#39;(?&lt;=menuentry_id_option ).gnuli\\S+&#39; /boot/grub/grub.cfg -Po | \\ sed &#34;s@&#39;@@g&#34; |grep -vE &#39;recovery|simple|gnulinux-advanced&#39; | \\ sed &#34;s@^@GRUB_DEFAULT=\\&#34;${MENUID}&gt;@; s@\\$@\\&#34;@&#34;</code></pre><p>Sample output:</p><pre tabindex=\"0\"><code>GRUB_DEFAULT=&#34;gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&gt;gnulinux-6.2.16-usb2-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&#34;GRUB_DEFAULT=&#34;gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&gt;gnulinux-6.2.0-36-generic-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&#34;GRUB_DEFAULT=&#34;gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&gt;gnulinux-6.2.0-26-generic-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&#34;</code></pre><p>So assume we want to try the new kernel <strong>6.2.16-usb2</strong> while old stable kernel is <strong>6.2.0-36-generic</strong>.</p><ul><li>New Kernel ID: [line1]</li><li>Old Kernel ID: [line2]</li></ul><p>Edit <code>/etc/default/grub</code> and set <code>GRUB_DEFAULT</code> to the OLD kernel ID.</p><pre tabindex=\"0\"><code>GRUB_DEFAULT=&#34;gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&gt;gnulinux-6.2.0-36-generic-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&#34;</code></pre><p>Run <code>update-grub</code>.</p><p>Mark New kernel to be booted up during the next reboot:</p><pre tabindex=\"0\"><code>grub-reboot &#34;gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&gt;gnulinux-6.2.16-usb2-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee&#34;</code></pre><p>Reboot the server:</p><pre tabindex=\"0\"><code>syncreboot -f</code></pre><p>Wait ~1 minute (depends..), then:</p><h1 id=\"if-the-server-was-booted-ok\">If the server was booted OK:</h1><p>set <code>GRUB_DEFAULT=0</code> (default kernel of higher version)</p><p>run <code>update-grub</code> and <code>sync</code> and <code>reboot</code></p><p>make sure new kernel is booted up again (because of its higher version)</p><h1 id=\"if-the-server-was-not-booted-up\">If the server was not booted up:</h1><p>reboot, old kernel will boot (because of <code>GRUB_DEFAULT</code>)</p><p>remove new kernel with <code>apt remove</code> or <code>dpkg -r</code></p><p>set <code>GRUB_DEFAULT=0</code> and run <code>update-grub</code></p></description>\n</item>\n<item>\n<title>Acom 716</title>\n<link>https://blog.tanatos.org/posts/acom716/</link>\n<pubDate>Mon, 20 Nov 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/acom716/</guid>\n<description><p>Briew review of <strong>Acom716</strong> multi-SIM router &amp; proxies server</p><h2 id=\"where-to-buy\">Where to buy:</h2><ul><li><a href=\"https://ejointech.cn/products/16-port-4g-proxy-gateway\">Official site?</a></li><li><a href=\"https://aliexpress.com/item/1005004633686302.html\">AliExpress</a></li><li><a href=\"https://www.made-in-china.com/showroom/faf448fd0d906a15/product-detailgwYaGJHOVxcT/China-16-SIM-IP-Proxy-Router-for-Multi-IP-Solution-Socks5-HTTP-Proxy-Server.html\">made-in-china.com</a></li><li><a href=\"https://www.globalsources.com/IP-gateway/IP-proxy-modem-1183148354p.htm\">globalsources.com</a></li></ul><p>Also knows as <strong>&ldquo;MTR716&rdquo;</strong>.</p><p>Price: about <strong>$1300</strong>.</p><p><img src=\"https://blog.tanatos.org/acom716/acom716.png\" alt=\"Acom716\"></p><h2 id=\"idea-of-the-device\">Idea of the device:</h2><ul><li>It allows building 16 proxies (HTTP and SOCKS5) from each of embedded 4G modems. Each modem has to have a SIM card.</li><li>Proxies are available by LAN IP of the router.</li><li>It allows IP rotation of the proxies by operating the underlying modems ( <strong>Quectel EC25</strong> ).</li><li>All actions can be done by clicking buttons in the Web App or by HTTP API.</li></ul><h2 id=\"download\">Download</h2><ol><li><a href=\"https://blog.tanatos.org/acom716/16%20Port%20Proxy%20Gateway%20User%20Manual%20V1.0.pdf\">User Manual</a></li><li><a href=\"https://blog.tanatos.org/acom716/Proxy%20MultiWan%20Router%20HTTP%20API_en(1).pdf\">HTTP API</a></li></ol><h2 id=\"is-it-worth-buying\">Is it worth buying?</h2><p><strong>PRO&rsquo;s:</strong></p><ul><li>very easy to start 4g proxies business with</li><li>good for beginners at 4g proxies building</li></ul><p><strong>CON&rsquo;s:</strong></p><ul><li>Expensive.</li><li>You are bound to its API which is not easy to operate.</li><li>Modems can&rsquo;t be upgraded \\ replaced \\ expanded.</li><li>you can&rsquo;t construct quite complex ACL on the proxies or configure them in a sophisticated way.</li><li>if you are a Pro in building proxies, it is not for you.</li></ul></description>\n</item>\n<item>\n<title>USB modems don't show up</title>\n<link>https://blog.tanatos.org/posts/usb2__not-enough-usb/</link>\n<pubDate>Wed, 01 Nov 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/usb2__not-enough-usb/</guid>\n<description><p>How to solve <code>Not enough host resources</code> in Linux system log, when USB modems don&rsquo;t show up though they are visible in <code>lsusb</code> and only 3..10 of them are actually working.</p><p>Check with the command :</p><pre tabindex=\"0\"><code>sudo journalctl -b -k | grep -E &#39;Not enough host .* resources|set config&#39; | tail </code></pre><p>If you see this output :</p><pre tabindex=\"0\"><code>Oct 31 21:39:38 px kernel: usb 3-4.1.3: can&#39;t set config #1, error -12Oct 31 21:39:45 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 61Oct 31 21:39:45 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12Oct 31 21:39:48 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:39:48 px kernel: usb 3-4.1.3: can&#39;t set config #1, error -12Oct 31 21:39:59 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 58Oct 31 21:39:59 px kernel: usb 3-4.1.3: can&#39;t set config #1, error -12Oct 31 21:40:10 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:40:10 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12Oct 31 21:40:21 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:40:21 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12Oct 31 21:40:32 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:40:32 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12Oct 31 21:40:42 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:40:42 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12Oct 31 21:40:53 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:40:53 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12Oct 31 21:41:04 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59Oct 31 21:41:04 px kernel: usb 3-4.6.2: can&#39;t set config #1, error -12</code></pre><p>.. then solutions are as follows.</p><h2 id=\"simple-solutions\">Simple solutions</h2><p>Try one of, or all of them together:</p><ul><li>Disable USB 3 in BIOS completely.</li><li>use USB 2.0 cables</li><li>use USB 2.0 hub</li><li>use USB 2.0 slot on the PC side</li></ul><h2 id=\"solution-with-custom-kernel\">Solution with custom kernel</h2><p>Build a Linux kernel without USB 3 kernel driver. Disable <strong>CONFIG_USB_XHCI_HCD</strong> while building.</p><p>Then disable UEFI (Secure boot) in BIOS, because custom kernels are not signed with enrolled Ubuntu keys (or enroll yours..).</p><p>Or install a kernel already built by me:</p><p><a href=\"https://pathos.tanatos.org/repo/kernel.usb2.0/\">https://pathos.tanatos.org/repo/kernel.usb2.0/</a></p><p>Download for your Ubuntu version.</p><p>Then <a href=\"https://blog.tanatos.org/posts/grub_try_new_kernel/\">install</a> it</p></description>\n</item>\n<item>\n<title>Openvpn Connect - fix support of old profiles</title>\n<link>https://blog.tanatos.org/posts/openvpn_connect_fix/</link>\n<pubDate>Sun, 22 Oct 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/openvpn_connect_fix/</guid>\n<description><p>Around July 2023 , <strong>Openvpn Connect 3.4</strong> was released which has broken support of old OpenVPN profiles. It gone global.</p><p><a href=\"https://duckduckgo.com/?q=openvpn+unknown%2Funsupported+options&amp;t=h_&amp;ia=web\">More info: DuckDuckGo</a></p><h2 id=\"how-the-issue-appears\">How the issue appears</h2><p><strong>Openvpn Connect</strong> won&rsquo;t connect and throws an error: <code>unknown/unsupported option</code></p><h2 id=\"solutions\">Solutions:</h2><h3 id=\"solution-1\">Solution 1.</h3><p>Edit the profile (<strong>.ovpn</strong> file) &amp; delete 2 lines:</p><pre tabindex=\"0\"><code>route-delaypull</code></pre><p>Save the file.</p><p>Add the profile and connect</p><h3 id=\"solition-2\">Solition 2</h3><p>Use older Openvpn Connect (pre 3.4 ).</p><h3 id=\"solution-3\">Solution 3</h3><p>Use <a href=\"https://openvpn.net/community-downloads/\">Openvpn Community client</a></p></description>\n</item>\n<item>\n<title>Building proxies - phones VS dongles</title>\n<link>https://blog.tanatos.org/posts/phones_vs_modems/</link>\n<pubDate>Mon, 10 Jul 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/phones_vs_modems/</guid>\n<description><p>Let&rsquo;t compare Pros and Cons of phones and dongles (modems) when building 4G/5G proxies on them.</p><h1 id=\"dongles-modems\">Dongles (modems)</h1><h3 id=\"dongles-modems-pros\">Dongles (modems) PRO&rsquo;s:</h3><ul><li>easy to set up (insert a SIM card and go)</li><li>anonymity (no Google accounts)</li><li>cheap hardware</li><li>less cables</li></ul><h3 id=\"dongles-modems-cons\">Dongles (modems) CON&rsquo;s:</h3><ul><li>no 5G, unless you buy expensive modems.</li><li>big tech companies stopped developing new dongles around 2018, so present dongles are often cheap 4G LTE pieces with LTE cat.4</li><li>lower LTE category (in general) so worse perception / slower speed, they need antennas or a GSM booster</li><li>a PC is needed to manage the <a href=\"https://proxysmart.org\">4g proxy farm</a></li><li><a href=\"https://blog.tanatos.org/posts/usb_hub/\">a good USB hub</a> is needed to connect the modems with the PC (unless you use 4g/5g LAN routers..)</li><li>a trick with IMEI is needed when the Carrier sells a DATA plan for Phones/Tablets.</li><li>difficult to find for some areas (e.g. the USA) for some Carriers.</li></ul><h1 id=\"phones\">Phones</h1><h3 id=\"phones-pros\">Phones PRO&rsquo;s</h3><ul><li>high speed out of the box because of 5G or higher LTE category (LTE cat. 12..20)</li><li>no need to use a PC for management; phones can connect to a VPS on their own</li><li>no need to buy <a href=\"https://blog.tanatos.org/posts/usb_hub/\">a good USB hub</a>; wall chargers are enough</li><li>native OS TCP fingerprint</li><li>easier to find and buy than modems (critical in some areas)</li><li>fit in with Carrier Data plans (for Phones/Tablets)</li><li><a href=\"https://proxysmart.org\">4g proxy farm</a> survives short electricyty outages</li></ul><h3 id=\"phones-cons\">Phones CON&rsquo;s</h3><ul><li>more expensive than a modem</li><li>hastle with initial set up (fake Google accounts for 100 phones is a nightmare)</li><li>rooting might be needed to unlock some tasty features</li><li><a href=\"https://blog.tanatos.org/posts/os_tcp_fingerprint_2022/\">changing OS TCP fingerprint</a> can be tricky.</li><li>battery health issues</li></ul></description>\n</item>\n<item>\n<title>Huawei E3372-325 'BROVI' and Linux (Ubuntu) - Stick mode.</title>\n<link>https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux_stickmode/</link>\n<pubDate>Mon, 29 May 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux_stickmode/</guid>\n<description><p>How to get to work Huawei E3372-325 &lsquo;BROVI&rsquo; in Linux (ubuntu) - Stick mode.</p><p>In <a href=\"https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux/\">Part1</a> I explained how to get it work in HiLink mode. For some reasons sometimes you need it in STICK mode.</p><p>Notes:</p><ul><li>I could not get it to work in NCM mode (with controlling AT\\CDC port and DHCP on a network card) so PPP mode was used.</li></ul><p><strong>/usr/local/bin/brovi_switch.X</strong> (make it executable!)</p><pre tabindex=\"0\"><code>#!/bin/bashPATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin# vim: filetype=bash# 2023-01-28 Pavel Piatruk, piatruk.by# switches BROVI to STICK modeID=$$echo $IDUSB_ID=$(basename $DEVPATH){echo bInterfaceClass on ports as follows grep -H . /sys$DEVPATH/1*/bInterfaceClass IC=$( grep -h . /sys$DEVPATH/*:1.0/bInterfaceClass )echo &#34;got bInterfaceClass on 1st port $IC&#34;echo usb_modeswitch -b $BUSNUM -g $DEVNUM -v 3566 -p 2001case $IC in08) echo Storage MODE usb_modeswitch -b $BUSNUM -g $DEVNUM -v $ID_VENDOR_ID -p $ID_MODEL_ID -X ;;e0) echo &#34;Already RNDIS&#34; ;;ff) echo Serial Port ;;*) echo Unknown mode ;;esac} | logger -t BROVIexit 0</code></pre><p>and UDEV script <strong>/etc/udev/rules.d/40-huawei.rules</strong></p><pre tabindex=\"0\"><code>ACTION!=&#34;add&#34;, GOTO=&#34;modeswitch_rules_end&#34;SUBSYSTEM!=&#34;usb&#34;, GOTO=&#34;modeswitch_rules_end&#34;### All known install partitions are on interface 0ATTRS{bInterfaceNumber}!=&#34;00&#34;, GOTO=&#34;modeswitch_rules_end&#34;#GOTO=&#34;modeswitch_rules_begin&#34;LABEL=&#34;modeswitch_rules_begin&#34;ATTR{idVendor}==&#34;3566&#34;, ATTR{idProduct}==&#34;2001&#34;, RUN+=&#34;/usr/local/bin/brovi_switch.X %k %p&#34;LABEL=&#34;modeswitch_rules_end&#34;SUBSYSTEM==&#34;net&#34;, ACTION==&#34;add&#34;, ATTRS{idVendor}==&#34;3566&#34;, ATTRS{idProduct}==&#34;2001&#34;, NAME=&#34;wwan_brovi&#34;# ignore AT ports on interfaces 2,4 for ModemManager cuz they stuck for some reasonSUBSYSTEM==&#34;tty&#34;, ACTION==&#34;add&#34;, DEVPATH==&#34;*:1.[24]/*&#34;, ATTRS{idVendor}==&#34;3566&#34;, ATTRS{idProduct}==&#34;2001&#34;, ENV{ID_MM_PORT_IGNORE}=&#34;1&#34; </code></pre><p>After editing these 2 , replug the modem. Or when the modem is in, and is in CDROM mode, just run</p><pre tabindex=\"0\"><code>usb_modeswitch -v 3566 -p 2001 -X</code></pre><p>So it will open its ports &amp; 3 new tty devices will appear. If they don&rsquo;t - tell the OS to bind this device</p><pre tabindex=\"0\"><code>modprobe optionecho 3566 2001 ff &gt; /sys/bus/usb-serial/drivers/option1/new_id</code></pre><p>At this point AT ports are present. Check kernel logs.</p><p>Now you have 2 options to get online.</p><h2 id=\"option1--use-networkmanager--modemmanager\">Option1. Use NetworkManager + ModemManager</h2><p>Check output of <strong>mmcli -L</strong> , it shows smth like <em>/org/freedesktop/ModemManager1/Modem/5 [ZOWEE TECHNOLOGY (HEYUAN) CO., LTD.] E3372-325</em>.</p><p>Check verbose output from MM(ModemManager), it shows signal levels etc: <strong>mmcli -m 5</strong></p><p>And <strong>nmcli dev</strong> shows <strong>ttyUSB1</strong>.</p><p>Create NM(NetworkManager) connection:</p><pre tabindex=\"0\"><code>nmcli con add con-name BROVI_PPP ipv4.route-metric 3000 connection.type gsm ifname ttyUSB1nmcli con modify BROVI_PPP ipv6.method disablednmcli con modify BROVI_PPP gsm.apn internet</code></pre><p>it is started automatically when the modem appears.</p><h2 id=\"option2--use-wvdial\">Option2. use Wvdial.</h2><p>create a file <strong>/etc/wvdial.conf</strong></p><pre tabindex=\"0\"><code>[Dialer Defaults]Init1 = ATInit3 = AT+CGDCONT=1,&#34;IP&#34;,&#34;internet&#34;Init7 = AT+CGATT?Init8 = AT+CGACT?Phone = *99#Username = { }Password = { }Modem Type = Analog ModemISDN = 0New PPPD = yesStupid Mode = yesModem = /dev/ttyUSB1Auto DNS = noBaud = 9600</code></pre><p>and run <code>wvdial</code></p></description>\n</item>\n<item>\n<title>UK 4G\\LTE modems</title>\n<link>https://blog.tanatos.org/posts/uk_modems/</link>\n<pubDate>Tue, 04 Apr 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/uk_modems/</guid>\n<description><p>The following modems should be working in the UK e.g. with my <a href=\"https://proxysmart.org\"> 4g proxy farm software <strong>Proxysmart</strong></a> software.</p><p>Main <strong>LTE bands</strong> in the UK:</p><ul><li>FDD B3 1800Mhz</li><li>FDD B7 2100Mhz</li><li>FDD B20 800Mhz</li></ul><h2 id=\"tldr\">TL;DR;</h2><p>Most popular and easy to get modems for the UK:</p><ul><li>Huawei E3372-320</li><li>Quectel EC25, EP06</li><li>ZTE MF79</li><li>Sierra Wireless EM7455</li></ul><h2 id=\"dongles\">DONGLES</h2><ul><li>Alcatel IK40</li></ul><p><a href=\"https://www.amazon.co.uk/Alcatel-LinkKey-Mobile-Internet-Dongle-Black/dp/B073X3NWF7/\">https://www.amazon.co.uk/Alcatel-LinkKey-Mobile-Internet-Dongle-Black/dp/B073X3NWF7/</a></p><ul><li>Huawei E3372-320 (recommended)</li></ul><p>..</p><ul><li>Huawei E3372-325 (not recommended)</li></ul><p><a href=\"https://www.amazon.co.uk/Huawei-E3372-Cat4-Modem-E3372H-153-White/dp/B013UURTL4/\">https://www.amazon.co.uk/Huawei-E3372-Cat4-Modem-E3372H-153-White/dp/B013UURTL4/</a></p><ul><li>ZTE-MF833U1</li></ul><p><a href=\"https://www.amazon.co.uk/ZTE-MF833U1-Unlocked-Configuration-Warranty/dp/B08R69YGLV/\">https://www.amazon.co.uk/ZTE-MF833U1-Unlocked-Configuration-Warranty/dp/B08R69YGLV/</a></p><h2 id=\"mifi\">MIFI</h2><ul><li>Huawei E5783</li></ul><p><a href=\"https://www.amazon.co.uk/Huawei-E5783B-230-Super-Fast-Hotspot-Warranty/dp/B07KF93DFH/\">https://www.amazon.co.uk/Huawei-E5783B-230-Super-Fast-Hotspot-Warranty/dp/B07KF93DFH/</a></p><p><a href=\"https://www.amazon.co.uk/E5783-330-Soyealink-Hotspot-Worldwide-Wireless-Black/dp/B09MFSNSDN/\">https://www.amazon.co.uk/E5783-330-Soyealink-Hotspot-Worldwide-Wireless-Black/dp/B09MFSNSDN/</a></p><ul><li>Huawei E5577</li></ul><p><a href=\"https://www.amazon.co.uk/Super-Fast-Portable-Hotspot-Unlocked-Network/dp/B08L85339K/\">https://www.amazon.co.uk/Super-Fast-Portable-Hotspot-Unlocked-Network/dp/B08L85339K/</a></p><p><a href=\"https://www.amazon.co.uk/Huawei-E5577-320-300Mbps-Mobile-Broadband-Black/dp/B09BYWSQ1Y/\">https://www.amazon.co.uk/Huawei-E5577-320-300Mbps-Mobile-Broadband-Black/dp/B09BYWSQ1Y/</a></p><ul><li>Huawei E5576-320</li></ul><p><a href=\"https://www.amazon.co.uk/E5576-320-Networks-Configuration-required-Warranty/dp/B081SCZRHL/\">https://www.amazon.co.uk/E5576-320-Networks-Configuration-required-Warranty/dp/B081SCZRHL/</a></p><p><a href=\"https://www.amazon.co.uk/Huawei-E5576-320-Networks-Configuration-required/dp/B07Y3ZSR7D/\">https://www.amazon.co.uk/Huawei-E5576-320-Networks-Configuration-required/dp/B07Y3ZSR7D/</a></p><p><a href=\"https://www.amazon.co.uk/E5330-3G-Unlocked-Networks-Configuration-Required/dp/B07Z5LWMNZ/\">https://www.amazon.co.uk/E5330-3G-Unlocked-Networks-Configuration-Required/dp/B07Z5LWMNZ/</a></p><ul><li><p>ZTE MF79</p></li><li><p>ZTE MF920</p></li></ul><p><a href=\"https://www.amazon.co.uk/ZTE-MF920U-Travel-Hotspot-SMARTY/dp/B08CZYM51X/\">https://www.amazon.co.uk/ZTE-MF920U-Travel-Hotspot-SMARTY/dp/B08CZYM51X/</a></p><p><a href=\"https://www.amazon.co.uk/ZTE-MF920V-Hotspot-Unlocked-Networks/dp/B00YTI0ZRW/\">https://www.amazon.co.uk/ZTE-MF920V-Hotspot-Unlocked-Networks/dp/B00YTI0ZRW/</a></p><h2 id=\"lan-modems\">Lan modems</h2><ul><li>Huawei B311</li></ul><p><a href=\"https://www.amazon.co.uk/Unlocked-Networks-Genuine-Warranty-Network/dp/B08235ZNPV/\">https://www.amazon.co.uk/Unlocked-Networks-Genuine-Warranty-Network/dp/B08235ZNPV/</a></p><h2 id=\"lte-modules\">LTE modules</h2><ul><li>Quectel</li></ul><p><a href=\"https://www.amazon.co.uk/dp/B0B3C94GCM/\">https://www.amazon.co.uk/dp/B0B3C94GCM/</a></p><p>(EC25-E EC25-EU )</p><ul><li>Sierra Wireless EM7455</li></ul></description>\n</item>\n<item>\n<title>Huawei E3372-325 'BROVI' and Linux (Ubuntu)</title>\n<link>https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux/</link>\n<pubDate>Sat, 28 Jan 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux/</guid>\n<description><p>How to get to work Huawei E3372-325 &lsquo;BROVI&rsquo; in Linux (ubuntu)</p><p>In Jan 2023 there is still no good integration of this new modem to Linux <strong>usb-modeswitch</strong>. It doesn&rsquo;t include its VID:PID, which are <strong>3566:2001</strong>.</p><p>The weird thing about this modem, when it is plugged in a Linux machine, it goes through these steps on its own:</p><ul><li><ol><li>RNDIS+CDROM (netcard) , it stays here for 1..3 seconds, then switches on its own to</li></ol></li><li><ol start=\"2\"><li>CDROM , and stays here forever, and doesn&rsquo;t react on any SCSI sequence sent by usb-modeswitch .</li></ol></li></ul><p>It only accepts a sequence when in mode RNDIS+CDROM . And when it does so , it re-attaches its ports, so its netcard appears again. In latter case we should ignore it.</p><ul><li><ol><li>RNDIS+CDROM (netcard) , it stays here for 1..3 seconds,</li></ol></li><li><ol start=\"2\"><li>we manage to send a sequence</li></ol></li><li><ol start=\"3\"><li>during that sequence the RNDIS netcard appears again on the same USB port, but we ignore it (sic!)</li></ol></li><li><ol start=\"4\"><li>it stays in RNDIS forever</li></ol></li></ul><p>So we have to develop a script that</p><ul><li>addresses a modem by USB ID (not just by Vendor+Product ID!)</li><li>catches a modem with 3566:2001 in RNDIS mode</li><li>ignores other modes : CDROM, AT-PORT, NCM card</li><li>ignores already switched modem</li></ul><p>So 2 scripts are below</p><p><strong>/usr/local/bin/brovi_switch</strong> (make it executable!)</p><div class=\"highlight\"><div class=\"chroma\"><table class=\"lntable\"><tr><td class=\"lntd\"><pre tabindex=\"0\" class=\"chroma\"><code><span class=\"lnt\"> 1</span><span class=\"lnt\"> 2</span><span class=\"lnt\"> 3</span><span class=\"lnt\"> 4</span><span class=\"lnt\"> 5</span><span class=\"lnt\"> 6</span><span class=\"lnt\"> 7</span><span class=\"lnt\"> 8</span><span class=\"lnt\"> 9</span><span class=\"lnt\">10</span><span class=\"lnt\">11</span><span class=\"lnt\">12</span><span class=\"lnt\">13</span><span class=\"lnt\">14</span><span class=\"lnt\">15</span><span class=\"lnt\">16</span><span class=\"lnt\">17</span><span class=\"lnt\">18</span><span class=\"lnt\">19</span><span class=\"lnt\">20</span><span class=\"lnt\">21</span><span class=\"lnt\">22</span><span class=\"lnt\">23</span><span class=\"lnt\">24</span><span class=\"lnt\">25</span><span class=\"lnt\">26</span><span class=\"lnt\">27</span><span class=\"lnt\">28</span><span class=\"lnt\">29</span><span class=\"lnt\">30</span><span class=\"lnt\">31</span><span class=\"lnt\">32</span><span class=\"lnt\">33</span><span class=\"lnt\">34</span><span class=\"lnt\">35</span><span class=\"lnt\">36</span><span class=\"lnt\">37</span><span class=\"lnt\">38</span><span class=\"lnt\">39</span><span class=\"lnt\">40</span><span class=\"lnt\">41</span><span class=\"lnt\">42</span><span class=\"lnt\">43</span><span class=\"lnt\">44</span><span class=\"lnt\">45</span><span class=\"lnt\">46</span><span class=\"lnt\">47</span><span class=\"lnt\">48</span><span class=\"lnt\">49</span><span class=\"lnt\">50</span><span class=\"lnt\">51</span><span class=\"lnt\">52</span><span class=\"lnt\">53</span><span class=\"lnt\">54</span><span class=\"lnt\">55</span><span class=\"lnt\">56</span><span class=\"lnt\">57</span><span class=\"lnt\">58</span><span class=\"lnt\">59</span><span class=\"lnt\">60</span><span class=\"lnt\">61</span><span class=\"lnt\">62</span><span class=\"lnt\">63</span><span class=\"lnt\">64</span><span class=\"lnt\">65</span><span class=\"lnt\">66</span><span class=\"lnt\">67</span><span class=\"lnt\">68</span><span class=\"lnt\">69</span><span class=\"lnt\">70</span><span class=\"lnt\">71</span><span class=\"lnt\">72</span></code></pre></td><td class=\"lntd\"><pre tabindex=\"0\" class=\"chroma\"><code class=\"language-bash\" data-lang=\"bash\"><span class=\"line\"><span class=\"cl\"><span class=\"cp\">#!/bin/bash</span></span></span><span class=\"line\"><span class=\"cl\"><span class=\"cp\"></span><span class=\"nv\">PATH</span><span class=\"o\">=</span>/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin</span></span><span class=\"line\"><span class=\"cl\"><span class=\"c1\"># vim: filetype=bash</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"c1\"># 2023-01-28 Pavel Piatruk, piatruk.by</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"nv\">ID</span><span class=\"o\">=</span><span class=\"nv\">$$</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"nv\">USB_ID</span><span class=\"o\">=</span><span class=\"k\">$(</span>basename <span class=\"nv\">$DEVPATH</span><span class=\"k\">)</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"o\">{</span></span></span><span class=\"line\"><span class=\"cl\"><span class=\"c1\">#set|sort </span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"c1\">#ls -la /sys/$DEVPATH</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"nb\">echo</span> bInterfaceClass on ports as follows</span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\">grep -H . /sys<span class=\"nv\">$DEVPATH</span>/1*/bInterfaceClass</span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"nv\">IC</span><span class=\"o\">=</span><span class=\"k\">$(</span> grep -h . /sys<span class=\"nv\">$DEVPATH</span>/*:1.0/bInterfaceClass <span class=\"k\">)</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"nb\">echo</span> <span class=\"s2\">&#34;got bInterfaceClass on 1st port </span><span class=\"nv\">$IC</span><span class=\"s2\">&#34;</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> usb_modeswitch -b <span class=\"nv\">$BUSNUM</span> -g <span class=\"nv\">$DEVNUM</span> -v <span class=\"m\">3566</span> -p <span class=\"m\">2001</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"k\">case</span> <span class=\"nv\">$IC</span> in</span></span><span class=\"line\"><span class=\"cl\">08<span class=\"o\">)</span> </span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> Storage MODE</span></span><span class=\"line\"><span class=\"cl\"> <span class=\"p\">;;</span></span></span><span class=\"line\"><span class=\"cl\">e0<span class=\"o\">)</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> <span class=\"s2\">&#34;Already RNDIS&#34;</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nv\">LOCKFILE</span><span class=\"o\">=</span>/var/run/brovi.<span class=\"nv\">$USB_ID</span>.lock</span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">if</span> <span class=\"o\">[[</span> -e <span class=\"nv\">$LOCKFILE</span> <span class=\"o\">]]</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">then</span> </span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nv\">LOCKFILE_AGE</span><span class=\"o\">=</span><span class=\"k\">$((</span> <span class=\"k\">$(</span>date +%s <span class=\"k\">)</span> <span class=\"o\">-</span> <span class=\"k\">$(</span>stat <span class=\"nv\">$LOCKFILE</span> -c %Y<span class=\"k\">)</span> <span class=\"k\">))</span> </span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> <span class=\"nv\">LOCKFILE_AGE</span><span class=\"o\">=</span><span class=\"nv\">$LOCKFILE_AGE</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">fi</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">if</span> <span class=\"o\">[[</span> -n <span class=\"nv\">$LOCKFILE_AGE</span> <span class=\"o\">]]</span> <span class=\"o\">&amp;&amp;</span> <span class=\"o\">[[</span> <span class=\"nv\">$LOCKFILE_AGE</span> -lt <span class=\"m\">10</span> <span class=\"o\">]]</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">then</span> <span class=\"nb\">echo</span> was switched VERY recently, noop</span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">else</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">set</span> &gt; <span class=\"nv\">$LOCKFILE</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nv\">CMDS</span><span class=\"o\">=(</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"s2\">&#34;usb_modeswitch -b </span><span class=\"nv\">$BUSNUM</span><span class=\"s2\"> -g </span><span class=\"nv\">$DEVNUM</span><span class=\"s2\"> -v </span><span class=\"nv\">$ID_VENDOR_ID</span><span class=\"s2\"> -p </span><span class=\"nv\">$ID_MODEL_ID</span><span class=\"s2\"> -W -R -w 400 &#34;</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"s2\">&#34;usb_modeswitch -b </span><span class=\"nv\">$BUSNUM</span><span class=\"s2\"> -g </span><span class=\"nv\">$DEVNUM</span><span class=\"s2\"> -v </span><span class=\"nv\">$ID_VENDOR_ID</span><span class=\"s2\"> -p </span><span class=\"nv\">$ID_MODEL_ID</span><span class=\"s2\"> -W -R &#34;</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"o\">)</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nv\">i</span><span class=\"o\">=</span><span class=\"m\">0</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">for</span> CMD in <span class=\"s2\">&#34;</span><span class=\"si\">${</span><span class=\"nv\">CMDS</span><span class=\"p\">[@]</span><span class=\"si\">}</span><span class=\"s2\">&#34;</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">do</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nv\">i</span><span class=\"o\">=</span><span class=\"k\">$((</span><span class=\"nv\">$i</span><span class=\"o\">+</span><span class=\"m\">1</span><span class=\"k\">))</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> <span class=\"s2\">&#34;=====STEP</span><span class=\"nv\">$i</span><span class=\"s2\">, run: </span><span class=\"nv\">$CMD</span><span class=\"s2\">&#34;</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nv\">$CMD</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">done</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"k\">fi</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"p\">;;</span></span></span><span class=\"line\"><span class=\"cl\">ff<span class=\"o\">)</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> Serial Port</span></span><span class=\"line\"><span class=\"cl\"> <span class=\"p\">;;</span></span></span><span class=\"line\"><span class=\"cl\">*<span class=\"o\">)</span></span></span><span class=\"line\"><span class=\"cl\"> <span class=\"nb\">echo</span> Unknown mode</span></span><span class=\"line\"><span class=\"cl\"> <span class=\"p\">;;</span></span></span><span class=\"line\"><span class=\"cl\"><span class=\"k\">esac</span></span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"o\">}</span> <span class=\"p\">|</span> logger -t BROVI</span></span><span class=\"line\"><span class=\"cl\"></span></span><span class=\"line\"><span class=\"cl\"><span class=\"nb\">exit</span> <span class=\"m\">0</span></span></span></code></pre></td></tr></table></div></div><p>and UDEV script <strong>/etc/udev/rules.d/40-huawei.rules</strong></p><pre tabindex=\"0\"><code>ACTION!=&#34;add&#34;, GOTO=&#34;modeswitch_rules_end&#34;SUBSYSTEM!=&#34;usb&#34;, GOTO=&#34;modeswitch_rules_end&#34;# All known install partitions are on interface 0ATTRS{bInterfaceNumber}!=&#34;00&#34;, GOTO=&#34;modeswitch_rules_end&#34;GOTO=&#34;modeswitch_rules_begin&#34;LABEL=&#34;modeswitch_rules_begin&#34;# Huawei E3372-325ATTR{idVendor}==&#34;3566&#34;, ATTR{idProduct}==&#34;2001&#34;, RUN+=&#34;/usr/local/bin/brovi_switch %k %p&#34;LABEL=&#34;modeswitch_rules_end&#34;</code></pre><p><strong>EDIT 2023-01-30</strong> It seems Huawei released new firmware (below) that works fine with Linux. It switches to RNDIS -&gt; RNDIS -&gt; CDROM 12d1:1f01 -&gt; usb-modeswitch switches to RNDIS.</p><pre tabindex=\"0\"><code>&#39;SoftwareVersion&#39; =&gt; &#39;********(H057SP9C983)&#39;WebUIVersion&#39; =&gt; &#39;WEBUI ********(W13SP5C7702)&#39;,&#39;iniversion&#39; =&gt; &#39;E3372-325-CUST *******(C778)&#39;,&#39;HardwareVersion&#39; =&gt; &#39;CL5E3372M&#39;,</code></pre><p><strong>EDIT 2023-02-28</strong></p><p>There is an alternative guide <a href=\"https://www.draisberghof.de/usb_modeswitch/bb/viewtopic.php?f=3&amp;t=3043&amp;p=20026#p20054\">https://www.draisberghof.de/usb_modeswitch/bb/viewtopic.php?f=3&amp;t=3043&amp;p=20026#p20054</a> , it doesn&rsquo;t properly handle the case when <em>multiple</em> Brovi&rsquo;s are plugged in &ndash; it resets them <em>ALL</em> &ndash; the devices are referred by Vendor_ID and Product_ID, which are the same on identical modems. It`s fine when you have 1 BROVI on the PC.</p><p>While my solution addresses by Vendor_ID + Product_ID + Bus_ID + Deviced_ID. So the case with multiple BROVI on the PC is covered.</p><p><strong>EDIT 2023-05-29</strong></p><p>I got it working also in STICK mode <a href=\"https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux_stickmode/\">CLICK</a></p></description>\n</item>\n<item>\n<title>USB hubs for 4g proxies farm</title>\n<link>https://blog.tanatos.org/posts/usb_hub/</link>\n<pubDate>Sun, 22 Jan 2023 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/usb_hub/</guid>\n<description><p>How to buy best USB hub for a <a href=\"https://proxysmart.org\">building a 4g proxy farm</a>.</p><h3 id=\"main-things-to-check-before-buying\">Main things to check before buying</h3><ul><li>We don&rsquo;t need USB 3.0</li><li>Solid big charger, should look like a solid box 2&quot; x 2&quot; x 5&quot; , not like a cheap USB charger</li><li>at least 1 Amper per 1 USB port</li></ul><h3 id=\"good-usb-hubs\">Good USB hubs:</h3><ul><li><a href=\"https://www.amazon.com/ORICO-Aluminum-Charging-Adapter-Indicator/dp/B00NAMKDDY\">Orico A3H13P2 11 ports</a></li><li><a href=\"https://www.orico.shop/en/orico-12-ports-multi-functional-usb-30-hub-bc12.html\">Orico P12-U3 12 ports </a></li><li><a href=\"https://www.ebay.com/itm/************\">Orico P10-U3 10 ports </a></li><li><a href=\"https://www.amazon.com/dp/B07VND9MWL/\">Orico Industrial USB Hub, 30 Port </a></li><li><a href=\"https://www.amazon.com/dp/B07XGL49CV/\">Orico Industrial USB Hub, 20 Ports</a></li><li><a href=\"https://www.amazon.com/dp/B07XFNK37G/\">Orico Industrial USB Hub, 10 Ports</a></li><li><a href=\"https://www.aliexpress.com/item/1005002768054035.html\">Sipolar A-805p 20 ports</a></li><li><a href=\"https://www.amazon.com/Sipolar-USB-Hub-Ports-Hub-Industrial-Powered/dp/B01K4LYX7U/ref=cm_cr_arp_d_product_top?ie=UTF8\">Sipolar A-300 10 ports</a></li><li><a href=\"https://www.amazon.com/dp/B07C4WRKNK/ref=emc_b_5_t\">Sipolar A-103 10 ports </a></li><li><a href=\"https://www.amazon.com/dp/B072JW98SY/ref=emc_b_5_t\">Sipolar other 7 ports</a></li></ul></description>\n</item>\n<item>\n<title>QUIC support in Socks5 Proxies</title>\n<link>https://blog.tanatos.org/posts/quic/</link>\n<pubDate>Mon, 19 Dec 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/quic/</guid>\n<description><p>In 2023 you will loose the battle when your proxies don&rsquo;t support <strong>QUIC (HTTP/3.0)</strong>. With <a href=\"https://proxysmart.org\"> 4g proxy farm software <strong>Proxysmart</strong></a>, you will win. I recently added QUIC support to my proxies building software.</p><p>Indeed, when major websites like Instagram, FB detect that you run a modern Web browser on a modern OS on a modern hardware, and yet you can&rsquo;t send the data over <strong>QUIC (HTTP/3.0)</strong> , your requests are red-flagged and your account may be banned.</p><p>I already brought <a href=\"https://www.blackhatworld.com/seo/proxies-will-die-in-2023.1448749/\">this topic on BHW</a>, let me quote from there:</p><blockquote><p>Hi, there is a concern about raising usage of QUIC protocol (HTTP 3.0) with combination of using a proxy in browser. QUIC is UDP. All modern browsers do support QUIC. Yet none of them support sending QUIC via a proxy. Even if the proxy itself can send UDP (most Socks5 proxies can&rsquo;t but some can!).</p><p>And when a website finds out you have a modern browser and can&rsquo;t send QUIC, it&rsquo;s a big red flag to consider you are a bot!</p><p>3rd party solutions like Proxifier don&rsquo;t support sending QUIC either.</p><p>Possible solutions:</p><ul><li>wait till browsers can send QUIC via a proxy + proxy providers start adding UDP to their proxies</li><li>wait till tools like Proxifier can forward browser&rsquo;s UDP traffic via SOCKS5</li><li>switch to 4G\\5G VPN which are way more expensive.</li></ul></blockquote><p>People answered, cheer up, proxies companies will solve it, but my opinion is, browser&rsquo;s can&rsquo;t send HTTP3 via any proxy now. So eventhough proxy providers adapted, it is useless.</p><p>OK, if you&rsquo;ve got a proxy, how to check if it&rsquo;s <strong>UDP capable</strong> ? If it is, it can also do <strong>QUIC</strong>. I built a website, <a href=\"https://quic.tanatos.org:444/\">QUIC HTTP/3.0 checker</a> to check it. Refresh it multiple times (1st requst is always HTTP/2!).</p><p>Also you can send a raw UDP packet, using <a href=\"https://github.com/semigodking/socks5chk/blob/master/udpchk.py\">udpchk.py by semigodking</a> , if it sends, then the proxy is UDP capable.</p><p>Further info:</p><ul><li><a href=\"https://www.youtube.com/watch?v=07n9BUkyn8o\">https://www.youtube.com/watch?v=07n9BUkyn8o</a></li><li><a href=\"https://www.blackhatworld.com/seo/proxies-will-die-in-2023.1448749/\">https://www.blackhatworld.com/seo/proxies-will-die-in-2023.1448749/</a></li></ul></description>\n</item>\n<item>\n<title>Quectel EC200T on OpenWRT</title>\n<link>https://blog.tanatos.org/posts/quectel_ec200t_openwrt/</link>\n<pubDate>Fri, 16 Dec 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/quectel_ec200t_openwrt/</guid>\n<description><p>The issue with EC200T that it doesn&rsquo;t have QMI\\MBIM. It has only ECM and AT-port , so we have to start WAN connection on AT-port and use it via the WAN port.</p><p>Install some packages</p><pre><code>opkg updateopkg install kmod-usb-net-cdc-ether kmod-usb-serial-option usbutils socat </code></pre><ul><li>kmod-usb-net-cdc-ether for WWAN netcard device</li><li>kmod-usb-serial-option for /dev/ttyUSB* devices</li><li>usbutils for <code>lsusb</code></li><li>socat for sending AT commands.</li></ul><p>Plug in the module (EC200T) in USB, wait ~30sec, run <code>lsusb</code>, interesting line is</p><pre><code>Bus 002 Device 005: ID 2c7c:6026 Quectel Wireless Solutions Co., Ltd. </code></pre><p>Vendor and Product ID are <strong>2c7c:6026</strong> , so bind USB-OPTION driver to the device.</p><pre><code>echo 2c7c 6026 ff &gt; /sys/bus/usb-serial/drivers/option1/new_id</code></pre><p>Check if ttyUSB character devices are added:</p><pre><code>ls /dev/ttyUSB*</code></pre><p>Output:</p><pre><code>crw-rw---- 1 root dialout 188, 0 Dec 16 10:46 /dev/ttyUSB0crw-rw---- 1 root dialout 188, 1 Dec 16 10:46 /dev/ttyUSB1crw-rw---- 1 root dialout 188, 2 Dec 16 10:46 /dev/ttyUSB2</code></pre><p>Check if USB WWAN net card is added as <code>usb0</code></p><pre><code>ip li show</code></pre><p>Output contains <code>usb0</code>.</p><p>For EC200T, AT port sits on <code>/dev/ttyUSB1</code>. Let&rsquo;s talk there.</p><pre><code>AT_PORT=/dev/ttyUSB1SOCAT_RUN=&quot;socat - $AT_PORT,crnl&quot;echo ATE0 | $SOCAT_RUNecho ATI | $SOCAT_RUN</code></pre><p>Output:</p><pre><code>QuectelEC200TRevision: EC200TEUHAR05A01M16</code></pre><p>OK now let&rsquo;s create a WAN interface with DHCP client.</p><p>Edit <code>/etc/config/network</code> , add</p><pre tabindex=\"0\"><code>config interface &#39;QUECTEL_EC200T&#39; option proto &#39;dhcp&#39; option ifname &#39;usb0&#39; option metric &#39;200&#39;</code></pre><p>Then restart the network <code>/etc/init.d/network restart</code> , Openwrt will start sending DHCP requests on <code>usb0</code> but it will not respond (yet).</p><p>Add a file <code>/etc/init.d/quectel_EC200</code> and paste the content and make it 755</p><pre tabindex=\"0\"><code>#!/bin/sh /etc/rc.commonSTART=98start(){set -xecho StartAT_PORT=/dev/ttyUSB1DEV=usb0SOCAT_RUN=&#34;socat - $AT_PORT,crnl&#34;if test -c $AT_PORTthen echo port readyelseecho 2c7c 6026 ff &gt; /sys/bus/usb-serial/drivers/option1/new_idecho sleep till port is readysleep 30for i in $(seq 10);do test -c $AT_PORT &amp;&amp; break echo . sleep 10donefitest -c $AT_PORT || { echo &#34;$AT_PORT cant be found, exit&#34;; return 22; }echo ATE0 | $SOCAT_RUNecho ATI | $SOCAT_RUNecho &#39;AT+QCFG=&#34;usbnet&#34;&#39; | $SOCAT_RUNecho at+cgdcont? | $SOCAT_RUNecho at+cgatt? | $SOCAT_RUNecho at+cgact? | $SOCAT_RUNecho at+cgact=1,1 | $SOCAT_RUNecho AT+CGCONTRDP=1 | $SOCAT_RUNecho AT+QNETDEVCTL=1,1,1 | $SOCAT_RUN ; sleep 1echo AT+QNETDEVCTL=1,1,1 | $SOCAT_RUN ; sleep 1echo AT+QNETDEVCTL=1,1,1 | $SOCAT_RUN ; sleep 1echo AT+QNETDEVCTL? | $SOCAT_RUN}stop(){ echo Stop}</code></pre><p>Run it, it will connect the module to Internet</p><pre><code>/etc/init.d/quectel_EC200 start</code></pre><p>and DHCP client in previous step will obtain an IP, so expect a new default gateway, with metric <code>200</code>. Then check routing table</p><pre><code>ip ro</code></pre><p>Output:</p><pre tabindex=\"0\"><code>...default via ************ dev usb0 src *********** metric 200...</code></pre><p>Then enable its autostart, edit <code>/etc/hotplug.d/usb/10-quectel_ec200.sh</code> and paste the content and make it 755</p><pre tabindex=\"0\"><code>#!/bin/sh[ &#34;$ACTION&#34; = add -a &#34;$DEVTYPE&#34; = usb_device ] || exit 0vid=$(cat /sys$DEVPATH/idVendor)pid=$(cat /sys$DEVPATH/idProduct)if [[ $vid == 2c7c ]] &amp;&amp; [[ $pid == 6026 ]]thentrueelseexitfisleep 30 /etc/init.d/quectel_EC200 restart 2&gt;&amp;1 | logger -t quectel_EC200</code></pre><p>Reboot the router, Quectel EC200T will be online.</p><h3 id=\"summary-of-the-flow\">Summary of the flow:</h3><ul><li>plug in the module</li><li>hotplug will init the module and connect it online</li><li>Openwrt will detect its interface and start DHCP on it</li></ul><h3 id=\"further-reading\">Further reading:</h3><ul><li><a href=\"https://openwrt.org/docs/guide-user/base-system/hotplug\">https://openwrt.org/docs/guide-user/base-system/hotplug</a></li><li><a href=\"https://openwrt.org/docs/guide-user/network/wan/wwan/ltedongle\">https://openwrt.org/docs/guide-user/network/wan/wwan/ltedongle</a></li><li><a href=\"https://openwrt.org/docs/guide-user/network/wan/wwan/ethernetoverusb_ncm\">https://openwrt.org/docs/guide-user/network/wan/wwan/ethernetoverusb_ncm</a></li></ul></description>\n</item>\n<item>\n<title>How to Change OS TCP/IP Fingerprint</title>\n<link>https://blog.tanatos.org/posts/os_tcp_fingerprint_2022/</link>\n<pubDate>Mon, 03 Oct 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/os_tcp_fingerprint_2022/</guid>\n<description><p>Some useful info &amp; tips on OS TCP/IP Fingerprint.</p><h2 id=\"passive-os-tcpip-fingerprinting-how-it-works\">Passive OS TCP/IP Fingerprinting, how it works</h2><p>Client opens a connection, sends TCP SYN, and somebody inbetween analyzes it. This technique is named <strong>p0f</strong>. Only TCP SYN is unique for each OS, so OS name &amp; version &amp; even uptime can be extracted from it. In particular:</p><ul><li>ip ttl (p0f3: ittl)</li><li>tcp mss (p0f3: mss)</li><li>tcp options sequence (p0f3: olayout)</li><li>tcp window size (p0f3: wsize)</li><li>tcp window scaling (p0f3: scale)</li><li>tcp quirks</li></ul><p>More read: <a href=\"https://github.com/laoshaw/p0f3\">https://github.com/laoshaw/p0f3</a> =&gt; scroll down to &ldquo;== TCP signatures ==&rdquo;</p><p>All subsequent packets from client to the server are not unique: TCP ACK,RST,FIN etc.</p><p><em>Uptime</em> is detected by a TS (timestamp) option in tcp options sequence, which contains 2 values, and TSval <em>correlates</em> with uptime.</p><h2 id=\"active-os-tcpip-fingerprinting-how-it-works\">Active OS TCP/IP Fingerprinting, how it works</h2><p>Attacker (well, it is not an attack, but anyways) sends a TCP SYN to the object, connection established, TCP SYN+ACK is sent back &amp; analyzed by the attacker. The same factors do matter as for TCP SYN, but signatures do differ.</p><h2 id=\"software-used-for-p0f-cli\">Software used for p0f (CLI)</h2><ul><li><a href=\"https://github.com/MOL0ToK/sp0ky\">https://github.com/MOL0ToK/sp0ky</a></li><li><a href=\"https://github.com/cesarghali/OS-Fingerprinting\">https://github.com/cesarghali/OS-Fingerprinting</a></li><li><a href=\"https://github.com/laoshaw/p0f3\">https://github.com/laoshaw/p0f3</a></li><li><a href=\"https://github.com/NikolaiT/zardaxt\">https://github.com/NikolaiT/zardaxt</a></li><li>tcpdump / tshark (for checking raw signatures)</li></ul><h2 id=\"web-services-for-p0f\">WEB services for p0f</h2><p>Those use p0f3 or very similar:</p><ul><li><code>http://witch.valdikss.org.ru/</code></li><li><a href=\"https://browserleaks.com/ip\">https://browserleaks.com/ip</a></li><li><a href=\"https://whoer.net\">https://whoer.net</a> - click &ldquo;Extended version&rdquo;</li></ul><p>Uses its own set of signatures:</p><ul><li><code>https://tcpip.incolumitas.com/classify?by_ip=1</code></li></ul><h2 id=\"databases-of-signatures\">Databases of signatures</h2><p><a href=\"https://raw.githubusercontent.com/laoshaw/p0f3/master/p0f.fp\">https://raw.githubusercontent.com/laoshaw/p0f3/master/p0f.fp</a> p0f3 , TCP:request + TCP:response</p><p><a href=\"https://raw.githubusercontent.com/xnih/satori/master/fingerprints/tcp.xml\">https://raw.githubusercontent.com/xnih/satori/master/fingerprints/tcp.xml</a> p0f1 , TCP:request</p><p><a href=\"https://raw.githubusercontent.com/NikolaiT/zardaxt/master/database/combinedJune2022.json\">https://raw.githubusercontent.com/NikolaiT/zardaxt/master/database/combinedJune2022.json</a> kind of p0f1 format , used in Incolumitas checker.</p><p><strong>Problem:</strong></p><ul><li><p>p0f3 database (not mentioning the p0f which is even more old!) is very outdated and don&rsquo;t reflect modern situation with modern devices. For example, if you want to spoof TCP/IP fingerprint to pretent iOS, then even if you grab proper set of TCP options from real iOS, you still be recognized as <code>MAC OSX generic</code>. There is only 1 entry for iOS in p0f3, and when you start emulating it, yes, you will be seeing <code>iPad or iPhone</code> but it has a poor Wscale, so your connection will be starting too slowly.</p></li><li><p>the same is for Android. There are just 2 records in p0f3 for Android. While real Androids are visible as <code>Linux generic</code> or similar.</p></li><li><p>For Windows. p0f3 doesn&rsquo;t have records for modern Windows versions, they are detected as <code>Windows 7 or 8</code>.</p></li></ul><p><strong>Solution</strong></p><p>You have 2 solutions</p><ol><li><p>Grab real TCP signatures from real devices and use them for TCP/IP OS spoofing. <strong>Pro</strong>: it will be real &amp; genuine. <strong>Con</strong>: those public detectors will detect you as a wrong OS.</p></li><li><p>Use OS TCP signatures from p0f3. <strong>Pro</strong>: those public detectors will detect you as a target OS. <strong>Con</strong>: some private detectors still may detect you as a spoofer :-) And expect some speed losses because of non-optimal Wscale.</p></li></ol><h2 id=\"how-to-spoof-os-tcpip-fingerprint-theory\">How to spoof OS TCP/IP Fingerprint? Theory.</h2><ul><li>Linux Iptables firewall diverts outgoing TCP SYN packets to a local NFQUEUE</li><li>A software opens a Netfilter Queue, which accepts TCP packets.</li><li>unpacks them, adjusts mandatory TCP options, re-packs &amp; sends</li></ul><p>The software is scapy, a python module.</p><h2 id=\"how-to-spoof-os-tcpip-fingerprint-practically\">How to spoof OS TCP/IP Fingerprint? Practically.</h2><p>There is a good easy to use software called <a href=\"https://blog.tanatos.org/posts/osfooler-ng/\">OSfooler-ng</a> + its many forks. It is abandoned now. Also it uses ancient p0fv1 database. Also it simply can&rsquo;t process TS options which are VERY important for simulating MAC/iOS. In fact it is only useful for simulating Windows.</p><p>There is a python module <a href=\"https://pypi.org/project/scapy-p0f/\">scapy-p0f</a>, which uses p0f3 database, but still can&rsquo;t process TS options. Yet it doesn&rsquo;t have a ready to use program.</p><p>So the solution would be to take OSfooler-ng, modify it, so it works with scapy-p0f. I have done it, please DM me!</p><h2 id=\"result\">Result</h2><p><a href=\"https://blog.tanatos.org/os_tcp.png\">original screenshot</a></p><p><img src=\"https://blog.tanatos.org/os_tcp.png\" alt=\"\"></p><hr><h2 id=\"macosx\">MacOSX</h2><h4 id=\"osgenremacosx--details_p0f1\">OSGENRE=macosx ; DETAILS_P0F=1</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:5.39,&ldquo;Linux&rdquo;:4.4,&ldquo;Mac OS&rdquo;:11.3,&ldquo;Windows&rdquo;:2.4,&ldquo;iOS&rdquo;:11.05}</p><ul><li>valdik</li></ul><p>Detected OS = Mac OS X 10.x;MTU = 1398;Network link = ???</p><h4 id=\"osgenremacosx--details_p0f2\">OSGENRE=macosx ; DETAILS_P0F=2</h4><ul><li>incolumitas</li></ul><p>{ &ldquo;Android&rdquo;: 5.39, &ldquo;Linux&rdquo;: 4.39, &ldquo;Mac OS&rdquo;: 11.3, &ldquo;Windows&rdquo;: 2.42, &ldquo;iOS&rdquo;: 11.05 }</p><ul><li>valdik</li></ul><p>Detected OS = Mac OS X 10.x ; MTU = 1398; Network link = ???</p><h4 id=\"osgenremacosx-details_p0f3\">OSGENRE=macosx DETAILS_P0F=3</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:5.39,&ldquo;Linux&rdquo;:4.42,&ldquo;Mac OS&rdquo;:11.35,&ldquo;Windows&rdquo;:2.44,&ldquo;iOS&rdquo;:11.06}</p><ul><li>valdik</li></ul><p>Detected OS = Mac OS X 10.9 or newer (sometimes iPhone;MTU = 1398;Network link = ???</p><h2 id=\"ios\">IOS</h2><h4 id=\"osgenreios--details_p0f1\">OSGENRE=ios ; DETAILS_P0F=1</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:5.43,&ldquo;Linux&rdquo;:4.48,&ldquo;Mac OS&rdquo;:11.32,&ldquo;Windows&rdquo;:2.7,&ldquo;iOS&rdquo;:11.07}</p><ul><li>valdik</li></ul><p>Detected OS = Mac OS X iPhone or iPad;MTU = 1398;Network link = ???</p><h4 id=\"osgenreios--details_p0f2\">OSGENRE=ios ; DETAILS_P0F=2</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:5.41,&ldquo;Linux&rdquo;:4.44,&ldquo;Mac OS&rdquo;:11.92,&ldquo;Windows&rdquo;:2.4,&ldquo;iOS&rdquo;:11.93}</p><ul><li>valdik</li></ul><p>Detected OS = Mac OS X [generic];MTU = 1398;Network link = ???</p><h2 id=\"windows\">Windows</h2><h4 id=\"osgenrewindows-details_p0f1\">OSGENRE=windows DETAILS_P0F=1</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:5.01,&ldquo;Linux&rdquo;:4.24,&ldquo;Mac OS&rdquo;:2.89,&ldquo;Windows&rdquo;:13.66,&ldquo;iOS&rdquo;:2.78}</p><ul><li>valdik</li></ul><p>Detected OS = Windows 7 or 8;MTU = 1398;Network link = ???</p><h4 id=\"osgenrewindows-details_p0f2\">OSGENRE=windows DETAILS_P0F=2</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:3.86,&ldquo;Linux&rdquo;:3.99,&ldquo;Mac OS&rdquo;:2.71,&ldquo;Windows&rdquo;:6.68,&ldquo;iOS&rdquo;:2.55}</p><ul><li>valdik</li></ul><p>Detected OS = Windows 7 or 8;MTU = 1398;Network link = ???</p><h4 id=\"osgenrewindows-details_p0f3\">OSGENRE=windows DETAILS_P0F=3</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:4.19,&ldquo;Linux&rdquo;:4.25,&ldquo;Mac OS&rdquo;:2.91,&ldquo;Windows&rdquo;:12.57,&ldquo;iOS&rdquo;:2.81}</p><ul><li>valdik</li></ul><p>Detected OS = Windows 7 or 8;MTU = 1398;Network link = ???</p><h2 id=\"android\">Android</h2><h4 id=\"osgenreandroid-details_p0f1\">OSGENRE=android DETAILS_P0F=1</h4><ul><li>incolumitas</li></ul><p>{&ldquo;Android&rdquo;:10.23,&ldquo;Linux&rdquo;:10.82,&ldquo;Mac OS&rdquo;:2.52,&ldquo;Windows&rdquo;:3.6,&ldquo;iOS&rdquo;:2.47}</p><ul><li>valdik</li></ul><p>Detected OS = Linux (Android);MTU = 1398;Network link = ???</p></description>\n</item>\n<item>\n<title>How to build proxies from 4G/5G LAN routers</title>\n<link>https://blog.tanatos.org/posts/build_proxies_from_4g_lan_routers/</link>\n<pubDate>Fri, 02 Sep 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/build_proxies_from_4g_lan_routers/</guid>\n<description><p>Recently I added support of 4G or 5G LAN routers like <strong>Huawei Bxxx</strong>, to my <a href=\"https://proxysmart.org\"> 4G proxy farm software <strong>Proxysmart</strong></a> software:</p><ul><li>Huawei B311</li><li>Huawei B525</li><li>Huawei B628</li></ul><p>etc</p><p>They are not USB modems, so have to be put in a separate LAN + an extra Ethernet switch is used. The scheme is below.</p><p><img src=\"https://blog.tanatos.org/lanmodems.png\" alt=\"\"></p></description>\n</item>\n<item>\n<title>How to build VPN from PROXY</title>\n<link>https://blog.tanatos.org/posts/vpn2socks/</link>\n<pubDate>Fri, 12 Aug 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/vpn2socks/</guid>\n<description><p>Recently I built a solution for <strong>making VPN from Proxies</strong>. Any proxies are supported (<strong>HTTP, SOCKS5</strong>) and a resulting Openvpn user traffic is routed via the mapped proxy.</p><p>Every Openvpn user has its own backend proxy.</p><p>Of course only TCP is supported because majority of proxies don&rsquo;t support UDP. And it is enough for browsing \\ scraping.</p><p>The main tricky think was routing DNS packets via a proxy. So I used DoH (DNS-over-HTTPS).</p><p><strong>How it works:</strong></p><ul><li>For each vpn client it starts TCP redirector which establishes all TCP connections through a proxy</li><li>For each vpn client it starts DNS caching server &amp; DNS resolver. DNS is fwd&rsquo;ed to local DNS caching server, then to local DNS resolver, then (via proxy) to a public DNS DoH resolver like Google</li></ul><p><strong>Used tools:</strong></p><ul><li><a href=\"https://github.com/ginuerzh/gost\">https://github.com/ginuerzh/gost</a> for DNS cache</li><li><a href=\"https://github.com/aarond10/https_dns_proxy\">https://github.com/aarond10/https_dns_proxy</a> for DNS resolver via a backend proxy, using DoH</li><li>Openvpn, Linux, bash :-)</li></ul><p>It is briefly described here: <a href=\"https://askubuntu.com/a/1423292/906035\">https://askubuntu.com/a/1423292/906035</a></p></description>\n</item>\n<item>\n<title>Ideal Proxy Software</title>\n<link>https://blog.tanatos.org/posts/ideal_proxy/</link>\n<pubDate>Sat, 11 Jun 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/ideal_proxy/</guid>\n<description><p>A little comparison of open source proxy software: <strong>Haproxy, 3proxy, Squid, Nginx, Gost (2), V2ray</strong></p><pre tabindex=\"0\"><code> Haproxy 3proxy Squid Nginx Gost2 V2raycan send\\receive PROXY PROTOCOL y n y y n ncan listen on HTTP y y y y y y can listen on HTTPS y n y y n ncan listen on SOCKS5 n y n n y ycan send to parent HTTP proxy y y y y y ycan send to parent SOCKS5 proxy y y n n y ycan listen as transparent proxy y y y y n ncan send to transparent proxy y n y y n nhas ACL (domains, src\\dst IP) y y y y n yhas password-auth y y y y y yhas bandwidth speedlimit n y y y n nhas bandwidth quota n y y n n yhas bandwidth counter ~y y ~y ~y n ~y</code></pre><p>~ means &ldquo;almost&rdquo;</p></description>\n</item>\n<item>\n<title>USA 4G\\LTE modems</title>\n<link>https://blog.tanatos.org/posts/usa_modems/</link>\n<pubDate>Sat, 21 May 2022 00:00:00 +1300</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/usa_modems/</guid>\n<description><p>The following modems should be working in the USA e.g. with my <a href=\"https://proxysmart.org\"> 4g proxy farm software <strong>Proxysmart</strong></a> software.</p><p>Main <strong>LTE bands</strong> in the USA:</p><ul><li>FDD B2 1900 Mhz</li><li>FDD B4 1700 Mhz</li></ul><h2 id=\"tldr\">TL;DR;</h2><p>Most popular and easy to get modems for the USA:</p><ul><li>Alcatel IK41US, IK41UC</li><li>Quectel EC25-AFX, EC25-AF</li><li>Sierra Wireless EM7455</li></ul><h1 id=\"usb-dongles\">USB dongles</h1><h2 id=\"alcatel\">Alcatel</h2><ul><li><p>Alcatel IK41UC<a href=\"https://www.ebay.com/itm/************\">https://www.ebay.com/itm/************</a><a href=\"https://www.ebay.com/itm/************\">https://www.ebay.com/itm/************</a></p><p>B2/4/5/7/12/13/17/71</p></li><li><p>Alcatel IK41US</p><p><a href=\"https://www.ebay.com/itm/************\">https://www.ebay.com/itm/************</a></p></li><li><p>Alcatel MW41NF<a href=\"https://www.amazon.com/Alcatel-LINKZONE-Unlocked-MW41NF-2AOFUS2-T-Mobile/dp/B08D4PB55J/\">https://www.amazon.com/Alcatel-LINKZONE-Unlocked-MW41NF-2AOFUS2-T-Mobile/dp/B08D4PB55J/</a></p><p>LTE: B1/2/3/4/5/8/12/13/17/20/28</p></li><li><p>Alcatel MW41TM<a href=\"https://www.ebay.com/itm/184607733572\">https://www.ebay.com/itm/184607733572</a><a href=\"https://www.amazon.com/Alcatel-LINKZONE-MW41TM-Download-Anywhere/dp/B07791Y58K/\">https://www.amazon.com/Alcatel-LINKZONE-MW41TM-Download-Anywhere/dp/B07791Y58K/</a><a href=\"https://aliexpress.com/item/1005004421203808.html\">https://aliexpress.com/item/1005004421203808.html</a></p><p>FDD LTE: B2/4/12</p></li><li><p>Alcatel MW43TM</p><p><a href=\"https://www.ebay.com/itm/385371469463\">https://www.ebay.com/itm/385371469463</a></p><p>FDD LTE: 2/4/5/12/25/26/66/71</p></li><li><p>Alcatel LINKZONE 2 (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p><a href=\"https://us.alcatelmobile.com/alcatel-linkzone-2/\">https://us.alcatelmobile.com/alcatel-linkzone-2/</a></p><p>LTE FDD: 2/4/5/12/25/26/66/71</p></li></ul><h2 id=\"zte\">ZTE</h2><ul><li><p>ZTE MF971, <a href=\"https://aliexpress.com/item/1728282949.html\">https://aliexpress.com/item/1728282949.html</a> , <a href=\"https://www.amazon.com/Router-Hotspot-ZTE-Unlocked-Caribbean/dp/B0775ZQCYD/\">https://www.amazon.com/Router-Hotspot-ZTE-Unlocked-Caribbean/dp/B0775ZQCYD/</a></p><p>B1/2/3/4/5/7/8/17&amp;12/20/28</p></li><li><p>ZTE Velocity MF985 (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p><a href=\"https://www.ebay.com/itm/265698369028\">https://www.ebay.com/itm/265698369028</a> , <a href=\"https://aliexpress.com/item/1005003911031389.html\">https://aliexpress.com/item/1005003911031389.html</a></p><p>Lte Bands 2, 4, 5, 12, 29, 30, 66</p></li><li><p>ZTE MF279T (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p><a href=\"https://www.amazon.com/ZTE-Wireless-Internet-Device-Unlocked/dp/B07PLSTM1K/\">https://www.amazon.com/ZTE-Wireless-Internet-Device-Unlocked/dp/B07PLSTM1K/</a></p><p>Lte Bands 2, 4, 5, 12, 29, 30</p></li><li><p>ZTE Velocity MF923 <a href=\"https://www.amazon.com/ZTE-Velocity-Mobile-Hotspot-Unlocked/dp/B0747PF6P6/\">https://www.amazon.com/ZTE-Velocity-Mobile-Hotspot-Unlocked/dp/B0747PF6P6/</a></p><p>LTE bands 2/4/5/17/29</p></li><li><p>ZTE MF833V <a href=\"https://www.amazon.com/ZTE-MF833V-Customized-Version-Covering/dp/B07XXBQPZL/\">https://www.amazon.com/ZTE-MF833V-Customized-Version-Covering/dp/B07XXBQPZL/</a></p><p>LTE-FDD B1/2/3/4/5/7/8/12/17/20</p></li></ul><h2 id=\"huawei\">Huawei</h2><ul><li><p>E3372H-510 <a href=\"https://www.ebay.com/itm/394116161904\">https://www.ebay.com/itm/394116161904</a></p><p>LTE 1,2,4,5,7,28</p></li><li><p>E8372h-511 <a href=\"https://aliexpress.com/item/32826491455.html\">https://aliexpress.com/item/32826491455.html</a></p><p>4G LTE: Band 1, 2, 4, 5,17</p></li><li><p>E8372h-510 <a href=\"https://aliexpress.com/item/4001209567895.html\">https://aliexpress.com/item/4001209567895.html</a></p><p>Band 1, 2, 4, 5, 7, 28</p></li><li><p>E8372h-517 <a href=\"https://aliexpress.com/item/4000374160998.html\">https://aliexpress.com/item/4000374160998.html</a> <a href=\"https://aliexpress.com/item/32727646816.html\">https://aliexpress.com/item/32727646816.html</a></p><p>Band 1, 2, 4, 5, 7, 12, 17</p></li><li><p>E3276s-500<a href=\"https://aliexpress.com/item/1966339368.html\">https://aliexpress.com/item/1966339368.html</a></p><p>B 2/4/5/7</p></li><li><p>Huawei MS2372h-517 , MS2372h-518 (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p><a href=\"https://www.ebay.com/itm/325139289134\">https://www.ebay.com/itm/325139289134</a><a href=\"https://www.ebay.com/itm/275602522962\">https://www.ebay.com/itm/275602522962</a><a href=\"https://www.ebay.com/itm/295546421021\">https://www.ebay.com/itm/295546421021</a></p><p>LTE FDD B1/B2/B4/B5/B7/B12/B28</p></li><li><p>E8278s-603 <a href=\"https://aliexpress.com/item/32904377087.html\">https://aliexpress.com/item/32904377087.html</a> (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p>B 1/2/4/5/8/40</p></li><li><p>E5788 <a href=\"https://aliexpress.com/item/4000583132418.html\">https://aliexpress.com/item/4000583132418.html</a></p><p>4G LTE: 1/2/3/4/5/7/8/19/20/28/38/40/41/42 , Cat16</p></li><li><p>Huawei 603HW Pocket WiFi (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p><a href=\"https://aliexpress.com/item/4000250805947.html\">https://aliexpress.com/item/4000250805947.html</a></p><p>FDD-LTE B1 (2100 МГц), B2(1900 МГц), B4(1700/2100 МГц), B8(900 МГц), B11 z), B12(700 МГц), b17 (700bMHz), B18/26 ( z), B25( ), B26( )</p></li></ul><h3 id=\"other\">Other</h3><ul><li><p>Franklin T9 and Franklin T10</p></li><li><p>Jetpack AC791L</p></li></ul><h1 id=\"lan-routers\">LAN routers</h1><p><a href=\"https://blog.tanatos.org/posts/build_proxies_from_4g_lan_routers/\">Scheme of the setup</a></p><p>They don&rsquo;t have USB support, only LAN\\WIFI.</p><h3 id=\"huawei-1\">Huawei</h3><ul><li><p>Huawei B612s-51d (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )</p><p><a href=\"https://www.amazon.com/Huawei-B612s-51d-Unlocked-Caribbean-Renewed/dp/B08GKZGSXC/\">https://www.amazon.com/Huawei-B612s-51d-Unlocked-Caribbean-Renewed/dp/B08GKZGSXC/</a></p><p><a href=\"https://www.amazon.com/Huawei-B612s-51d-Router-Unlocked-Caribbean/dp/B07RC8JSMM\">https://www.amazon.com/Huawei-B612s-51d-Router-Unlocked-Caribbean/dp/B07RC8JSMM</a></p><p>LTE Bands: B2/B4/B5/B7/B41 LTE Cat.6</p></li><li><p>Huawei B311-521<a href=\"https://www.amazon.com/Huawei-B311-521-Unlocked-Venezuela-Caribbean/dp/B094W8H943/\">https://www.amazon.com/Huawei-B311-521-Unlocked-Venezuela-Caribbean/dp/B094W8H943/</a></p><p>LTE Bands: 2/4/5/7/28/66</p></li><li><p>Huawei B890 (not supported in <a href=\"https://proxysmart.org\">Proxysmart</a> )<a href=\"https://www.amazon.com/Huawei-Wireless-Gateway-Mobile-Unlocked/dp/B01B5DQGV2/\">https://www.amazon.com/Huawei-Wireless-Gateway-Mobile-Unlocked/dp/B01B5DQGV2/</a></p><p>LTE FDD Band 2/4/5/7/12/13/17</p><p>more read: <a href=\"https://www.4gltemall.com/4g-wireless-router/huawei-4g-lte-wifi-router.html\">https://www.4gltemall.com/4g-wireless-router/huawei-4g-lte-wifi-router.html</a></p></li></ul><h3 id=\"zte-1\">ZTE</h3><ul><li><p>ZTE MC7010CA</p></li><li><p>ZTE MC8010</p></li></ul><h1 id=\"lte-modules\">LTE modules</h1><h3 id=\"quectel\">Quectel</h3><ul><li>Module (chip) and USB box + antennas: separately</li></ul><p>Module (chip) itself: <a href=\"https://www.aliexpress.com/item/1005002833181873.html\">https://www.aliexpress.com/item/1005002833181873.html</a> or<a href=\"https://www.ebay.com/itm/384449850486\">https://www.ebay.com/itm/384449850486</a></p><p>USB box + antennas: <a href=\"https://www.aliexpress.com/item/32970948654.html\">https://www.aliexpress.com/item/32970948654.html</a> or<a href=\"https://www.ebay.com/itm/123427557392\">https://www.ebay.com/itm/123427557392</a></p><ul><li><p>Or, everything in 1 piece (chip + USB box):</p><p><a href=\"https://aliexpress.com/item/1005003984632017.html\">https://aliexpress.com/item/1005003984632017.html</a></p><p>Still antennas are needed.</p></li></ul><p>Chip versions:</p><ul><li><p>EC25-AF (Verizon, AT&amp;T, T-mobile, Rogers)</p><p>B 2,4,5,12,13,14,66,71</p></li><li><p>EC25-AFX (Verizon, AT&amp;T)</p><p>B 2,4,5,12,13,14,66,71</p></li></ul><h3 id=\"sierra-wireless\">Sierra Wireless</h3><ul><li><p>Sierra Wireless EM7455</p><p>Also M.2-USB adapter and antennas are needed;</p></li><li><p>Sierra Wireless EM7565</p></li></ul><h1 id=\"how-to-find-suitable-modems\">How to find suitable modems</h1><p>On AliExpress or Ebay, look up for &ldquo;Huawei LTE band 2&rdquo; &ldquo;ZTE 4g band 4&rdquo; or similar queries.</p></description>\n</item>\n<item>\n<title>Added new modems models to Proxysmart</title>\n<link>https://blog.tanatos.org/posts/newmodems/</link>\n<pubDate>Mon, 25 Apr 2022 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/newmodems/</guid>\n<description><p>Recently I have added new modems models to my <a href=\"https://proxysmart.org\"> 4g proxy farm software <strong>Proxysmart</strong></a>, in particular <strong>Quectel</strong> family and <strong>ZTE MF79</strong> and <strong>ZTE MF971</strong>. For each, it is possible to read SMS, and reset IP.</p><h3 id=\"zte-mf79--zte-mf971\">ZTE MF79, ZTE MF971</h3><p>Those are new ZTE models that the cell operators start selling to the customers. Their WEB API is pretty the same as of previous ZTE models, except one thing. It requires authentication and hassling with cookies and tokens encryption. So it took some time to reverse its API.</p><h3 id=\"quectel-series\">Quectel series</h3><p>Those are very promising, because they from the beginning work in NCM mode, yet they have awesome open API documentation, you have full control of almost every aspect of them. Yet it is possible to change their IMEI, which is useful for bypassing cell operators restrictions of tethering.</p></description>\n</item>\n<item>\n<title>Proxysmart: my alternative to Proxidize</title>\n<link>https://blog.tanatos.org/posts/proxidize_alternative/</link>\n<pubDate>Thu, 10 Mar 2022 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/proxidize_alternative/</guid>\n<description><p><strong>Proxidize</strong> <a href=\"https://www.blackhatworld.com/seo/proxidize-alternatives.1394096/\">significally raised prices</a> on their service and I have a drop-in replacement.</p><p>I have been developing <a href=\"https://proxysmart.org\"> 4g proxy farm software <strong>Proxysmart</strong></a> which is a set of software for building your own 4g\\LTE proxy farm. Proxidize is a big competitor, and they have raised prices,i.e. up to $20 per modem per month. It is something a common &ldquo;4g proxy&rdquo; farmer can&rsquo;t afford. So recently multiple ex-proxidize customers reached out to me. Theycancelled the conracts with Proxidize and all the Proxidize hardware was left: in particular 4g modems.</p><p>So I offered them my service and built new 4g proxy farm for them, on top of old hardware, with significally lower price, yet it was one time payment , not recurring, like Proxidize charged them.</p></description>\n</item>\n<item>\n<title>Fighting against Passive OS Fingerprinting</title>\n<link>https://blog.tanatos.org/posts/osfooler-ng/</link>\n<pubDate>Tue, 03 Nov 2020 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/osfooler-ng/</guid>\n<description><p>The article describes steps to install <strong>OSfooler-ng</strong> to prevent your host OS be recognized by Passive OS Fingerpringting (p0f).</p><p>I used a fork of OSfooler-ng because original project seems to be abandoned. Moreover the fork can be installed on modern Linux distros and doesn&rsquo;t require old packages.</p><p>Check the fork&rsquo;s page: <a href=\"https://github.com/moonbaseDelta/OSfooler-ng\">https://github.com/moonbaseDelta/OSfooler-ng</a></p><h2 id=\"centos-7\">Centos 7</h2><pre tabindex=\"0\"><code>yum install epel-releaseyum install python git python-pip wget gcc make python-devel libnetfilter_queue-devel libnfnetlink-devel </code></pre><h2 id=\"centos-8\">Centos 8</h2><pre tabindex=\"0\"><code>sed -i s/enabled=0/enabled=1/ /etc/yum.repos.d/CentOS-PowerTools.repo yum install epel-releaseyum install python2 git python2-pip wget gcc make python2-devel libnetfilter_queue-devel libnfnetlink-devel</code></pre><h2 id=\"ubuntu-1804\">Ubuntu 18.04</h2><pre tabindex=\"0\"><code>apt install libnetfilter-queue-dev libnfnetlink-dev python python-setuptools python-pip</code></pre><h2 id=\"install-osfooler-ng\">Install OSfooler-ng</h2><p>This step and following steps are OS-independant.</p><pre tabindex=\"0\"><code>pip2 install NetfilterQueuegit clone https://github.com/moonbaseDelta/OSfooler-ng ; cd OSfooler-ngpython2 setup.py installosfooler-ng -u osfooler-ng -o Windows -d &#34;SP3&#34; -i tun0 </code></pre><h2 id=\"check\">Check</h2><pre tabindex=\"0\"><code>curl http://witch.valdikss.org.ru/ -Ss | sed -n &#39;/^&lt;pre/,/NTL/p&#39;</code></pre><p>Should return <code>Detected OS = Windows NT kernel [generic]</code></p><p>Or visit <a href=\"https://whatleaks.com\">https://whatleaks.com</a> or <a href=\"https://doileak.com\">https://doileak.com</a> or <a href=\"https://whoer.net\">https://whoer.net</a></p><h2 id=\"notes\">Notes</h2><ul><li>some home routers do change TCP options, so consider using a VPN to the cloud for tests</li><li>Virtualbox also may change TCP options, so GuestVM-&gt;Host-&gt;VPN may not work because TCP opts are changed between GuestVM-&gt;Host.</li><li>for tests you can use <strong>P0F</strong> (<a href=\"https://linux.die.net/man/1/p0f\">https://linux.die.net/man/1/p0f</a>) on remote side.</li></ul></description>\n</item>\n<item>\n<title>Psiphon: setting up Linux client (with free servers)</title>\n<link>https://blog.tanatos.org/posts/psiphon-free/</link>\n<pubDate>Sat, 05 Sep 2020 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/psiphon-free/</guid>\n<description><p>Psiphon is obfuscated proxy, and its client &amp; server are open-source.</p><p>The plan is:</p><ul><li>setting up Linux Psiphon Client that connects to free Psiphon network.</li></ul><pre tabindex=\"0\"><code>rm -rf ~/psiphon.client.freemkdir -p ~/psiphon.client.freecd ~/psiphon.client.freecurl https://github.com/Psiphon-Labs/psiphon-tunnel-core-binaries/raw/master/linux/psiphon-tunnel-core-x86_64 -L -o psiphon-tunnel-core-x86_64chmod 755 psiphon-tunnel-core-x86_64vim client.free.conf</code></pre><p>paste text below</p><pre tabindex=\"0\"><code>{&#34;LocalHttpProxyPort&#34;:8081,&#34;LocalSocksProxyPort&#34;:1081,&#34;PropagationChannelId&#34;:&#34;FFFFFFFFFFFFFFFF&#34;,&#34;RemoteServerListDownloadFilename&#34;:&#34;remote_server_list&#34;,&#34;RemoteServerListSignaturePublicKey&#34;:&#34;MIICIDANBgkqhkiG9w0BAQEFAAOCAg0AMIICCAKCAgEAt7Ls+/39r+T6zNW7GiVpJfzq/xvL9SBH5rIFnk0RXYEYavax3WS6HOD35eTAqn8AniOwiH+DOkvgSKF2caqk/y1dfq47Pdymtwzp9ikpB1C5OfAysXzBiwVJlCdajBKvBZDerV1cMvRzCKvKwRmvDmHgphQQ7WfXIGbRbmmk6opMBh3roE42KcotLFtqp0RRwLtcBRNtCdsrVsjiI1Lqz/lH+T61sGjSjQ3CHMuZYSQJZo/KrvzgQXpkaCTdbObxHqb6/+i1qaVOfEsvjoiyzTxJADvSytVtcTjijhPEV6XskJVHE1Zgl+7rATr/pDQkw6DPCNBS1+Y6fy7GstZALQXwEDN/qhQI9kWkHijT8ns+i1vGg00Mk/6J75arLhqcodWsdeG/M/moWgqQAnlZAGVtJI1OgeF5fsPpXu4kctOfuZlGjVZXQNW34aOzm8r8S0eVZitPlbhcPiR4gT/aSMz/wd8lZlzZYsje/Jr8u/YtlwjjreZrGRmG8KMOzukV3lLmMppXFMvl4bxv6YFEmIuTsOhbLTwFgh7KYNjodLj/LsqRVfwz31PgWQFTEPICV7GCvgVlPRxnofqKSjgTWI4mxDhBpVcATvaoBl1L/6WLbFvBsoAUBItWwctO2xalKxF5szhGm8lccoc5MZr8kfE0uxMgsxz4er68iCID+rsCAQM=&#34;,&#34;RemoteServerListUrl&#34;:&#34;https://s3.amazonaws.com//psiphon/web/mjr4-p23r-puwl/server_list_compressed&#34;,&#34;SponsorId&#34;:&#34;FFFFFFFFFFFFFFFF&#34;,&#34;UseIndistinguishableTLS&#34;:true}</code></pre><pre tabindex=\"0\"><code>echo &#39;./psiphon-tunnel-core-x86_64 -config client.free.conf&#39; &gt; run.clientchmod 755 run.client./run.client</code></pre><p>It will start Http proxy on local port 8081 and Socks proxy on port 1081. Test those proxies in another console.</p><pre tabindex=\"0\"><code>curl -x localhost:8081 gmail.com -v </code></pre><p>That gave me 150KB/s speed.</p><h2 id=\"solve-the-situation-when-you-are-already-under-dpi\">Solve the situation when you are already under DPI.</h2><p>As you can see in free client config, it downloads servers list from <code>RemoteServerListUrl</code>. Of course you can download it beforehand &amp; point Psiphon client to some arbitrary Psiphon server .Get server tokens list:</p><pre tabindex=\"0\"><code>cd ~/tmp/wget https://s3.amazonaws.com//psiphon/web/mjr4-p23r-puwl/server_list_compressed printf &#34;\\x1f\\x8b\\x08\\x00\\x00\\x00\\x00\\x00&#34; |cat - server_list_compressed |gzip -dc | json_xs | grep &#39;&#34;data&#34;&#39; | awk -F\\&#34; &#39;{print $4}&#39; | sed &#34;s@\\\\\\n@\\n\\n\\n\\n@g&#34; &gt; server_tokens.txtless server_tokens.txt</code></pre><p>Each line is then a token. Use client config like this ( <code>client.free_servers.1.conf</code> ) :</p><p>( replace <code>__TOKEN__</code> to some token from the file <code>server_tokens.txt</code> )</p><pre tabindex=\"0\"><code>{ &#34;LocalHttpProxyPort&#34; : 8081, &#34;LocalSocksProxyPort&#34; : 1081, &#34;PropagationChannelId&#34; : &#34;FFFFFFFFFFFFFFFF&#34;, &#34;SponsorId&#34; : &#34;FFFFFFFFFFFFFFFF&#34;, &#34;TargetServerEntry&#34; : &#34;__TOKEN__&#34;}</code></pre><pre tabindex=\"0\"><code>./psiphon-tunnel-core-x86_64 -config client.free_servers.1.conf</code></pre></description>\n</item>\n<item>\n<title>Psiphon: setting up own server & Linux client</title>\n<link>https://blog.tanatos.org/posts/psiphon-own/</link>\n<pubDate>Sat, 05 Sep 2020 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/psiphon-own/</guid>\n<description><p>Psiphon is obfuscated proxy, and its client &amp; server are open-source.</p><p>The plan is:</p><ul><li>setting up own Psiphon server</li><li>setting up Psiphon Client on Linux that connects to your own Psiphon server</li></ul><h2 id=\"psiphon-server\">Psiphon Server</h2><p>Notes:</p><ul><li>consider changing OSSH port from 9991 to something cool e.g. 22, 443 etc&hellip;</li><li>when you change server port or server IP, regenerate the token &amp; update the clients.</li><li>multiple protocols, on different ports, they all will be tried by a client. List of available protocols :</li></ul><blockquote><p>&ldquo;SSH&rdquo;, &ldquo;OSSH&rdquo;, &ldquo;UNFRONTED-MEEK-OSSH&rdquo;, &ldquo;UNFRONTED-MEEK-HTTPS-OSSH&rdquo;,&ldquo;UNFRONTED-MEEK-SESSION-TICKET-OSSH&rdquo;, &ldquo;FRONTED-MEEK-OSSH&rdquo;,&ldquo;FRONTED-MEEK-QUIC-OSSH&rdquo;, &ldquo;FRONTED-MEEK-HTTP-OSSH&rdquo;, &ldquo;QUIC-OSSH&rdquo;,&ldquo;TAPDANCE-OSSH&rdquo;, abd &ldquo;CONJURE-OSSH&rdquo;.</p></blockquote><pre tabindex=\"0\"><code>mkdir -p /opt/psiphond-server/cd /opt/psiphond-server/curl https://github.com/Psiphon-Labs/psiphon-tunnel-core-binaries/raw/master/psiphond/psiphond -o psiphond -Lchmod 755 psiphondMYIP=`curl -4 https://api.ipify.org -Ss`echo $MYIP./psiphond -ipaddress $MYIP -protocol OSSH:9991 -protocol QUIC-OSSH:9996 -protocol UNFRONTED-MEEK-OSSH:9999 generatecat server-entry.dat ; echo</code></pre><p>Output: ~2500 letters and digits. It is the token for clients ; save it.</p><p>Run Psiphon server manually &hellip;</p><pre tabindex=\"0\"><code>./psiphond run</code></pre><p>.. or stop &amp; create Systemd unit:</p><pre tabindex=\"0\"><code>vim /etc/systemd/system/psiphond.service </code></pre><pre tabindex=\"0\"><code>[Unit]After=network.target[Service]ExecStart=/opt/psiphond-server/psiphond runType=simpleWorkingDirectory=/opt/psiphond-server[Install]WantedBy=default.target</code></pre><p>Enable the unit:</p><pre tabindex=\"0\"><code>systemctl daemon-reloadsystemctl start psiphond.servicesystemctl enable psiphond.servicesystemctl status psiphond.service</code></pre><h2 id=\"psiphon-client\">Psiphon Client</h2><pre tabindex=\"0\"><code>rm -rf ~/psiphon.client.to.own.servermkdir -p ~/psiphon.client.to.own.servercd ~/psiphon.client.to.own.servercurl https://github.com/Psiphon-Labs/psiphon-tunnel-core-binaries/raw/master/linux/psiphon-tunnel-core-x86_64 -o psiphon-tunnel-core-x86_64 -Lchmod 755 psiphon-tunnel-core-x86_64vim client.own.conf</code></pre><p>paste text below but replace <code>__TOKEN__</code> to the Token from step above.</p><pre tabindex=\"0\"><code>{ &#34;LocalHttpProxyPort&#34; : 8081, &#34;LocalSocksProxyPort&#34; : 1081, &#34;PropagationChannelId&#34; : &#34;FFFFFFFFFFFFFFFF&#34;, &#34;SponsorId&#34; : &#34;FFFFFFFFFFFFFFFF&#34;, &#34;TargetServerEntry&#34; : &#34;__TOKEN__&#34;}</code></pre><pre tabindex=\"0\"><code>echo &#39;./psiphon-tunnel-core-x86_64 -config client.own.conf&#39; &gt; run.clientchmod 755 run.client./run.client</code></pre><p>It will start Http proxy on local port 8081 and Socks proxy on port 1081. Test those proxies in another console.</p><pre tabindex=\"0\"><code>curl -x localhost:8081 gmail.com -v </code></pre><p>That gave me 700KB/s speed.</p><p>EDIT <strong>2023-01-09</strong></p><ul><li>typos fixed</li></ul></description>\n</item>\n<item>\n<title>Ubuntu: migrate from Ifupdown to Netplan. Step by step.</title>\n<link>https://blog.tanatos.org/posts/ifupdown-netplan/</link>\n<pubDate>Thu, 07 Feb 2019 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/ifupdown-netplan/</guid>\n<description><p>In Ubuntu 18.04 old good <strong>ifupdown</strong> is considered obsolete. And you need to migrate your servers to <strong>systemd-networkd + netplan</strong>. Lots of internet resources describe how to revert back to <strong>ifupdown</strong> but I am hailing the future! So this manual covers 2 situations, <strong>static</strong> networking &amp; <strong>DHCP</strong>. Both are IPv4.</p><pre tabindex=\"0\"><code>systemctl disable networking.service apt-get remove ifupdown -yapt-get install netplan.io libnet-cidr-perl -ysystemctl enable systemd-networkdDEV=`facter networking.primary`rm /etc/netplan/*grep dhcp /etc/network/interfaces </code></pre><p>Now follow either DHCP or Static part.</p><pre tabindex=\"0\"><code>######## DHCP STARTecho &#34;network: version: 2 renderer: networkd ethernets: $DEV: nameservers: addresses: [*******] dhcp4: yes&#34; | tee /etc/netplan/01-netcfg.yaml######## DHCP END</code></pre><pre tabindex=\"0\"><code>###### STATIC STARTGW=$( ip ro get ******* | grep -P &#39;(?&lt;=via )[\\d\\.]+(?= )&#39; -o )NETMASK=$( facter networking.netmask )CIDR=$( perl -e &#39;use Net::CIDR; print \\ Net::CIDR::addrandmask2cidr(&#34;&#39;$IP&#39;&#34;, &#34;&#39;$NETMASK&#39;&#34;).&#34;\\n&#34;; &#39; ) IP=$( facter networking.ip )echo &#34;network: version: 2 renderer: networkd ethernets: $DEV: addresses: - $IP/24 gateway4: $GW nameservers: addresses: [*******]&#34; | tee /etc/netplan/01-netcfg.yaml ###### STATIC END</code></pre><p>Then apply new conf &amp; see its status in <code>networkctl</code></p><pre tabindex=\"0\"><code>netplan applysystemctl stop networking.servicesleep 2; networkctl list $DEV</code></pre><p>And reboot the server!</p></description>\n</item>\n<item>\n<title>Why Ansible? Ansible vs Puppet</title>\n<link>https://blog.tanatos.org/posts/ansible-puppet/</link>\n<pubDate>Wed, 23 Jan 2019 00:00:00 +0000</pubDate>\n<author><EMAIL> (Pavel Piatruk)</author>\n<guid>https://blog.tanatos.org/posts/ansible-puppet/</guid>\n<description><h3 id=\"ansible-pros\">Ansible PROS</h3><ul><li><p>For each “piece of code” you have nodes where code will be executed. In puppet thou, it is vice versa.</p></li><li><p>Orchestration. Macro events can be scheduled easily i.e. inter-nodes dependencies. Inter-action dependencies.E.g. live migration of the whole distributed cloud web app ( all the load balancers, web-/db-/cache servers) from one cloud vendor to another, can be achieved with an Ansible playbook. Because Ansible is declarative language that in some cases can be used as a Bash script.</p></li><li><p>consequences are important &amp; can be leveraged. E.g. action1 on nodes1 before action2 on nodes2. In Puppet nodes don’t know about each other.</p></li><li><p>Ansible works via SSH connection, so you even can reboot the server &amp; wait till it is booted up again, then execute something on it right after reboot. Everything via Ansible.</p></li><li><p>signing/revoking SSL certificates in Puppet is s hell comparing to SSH access which is enough for Ansible. With Ansible you only need SSH access, no agents/masters.</p></li><li><p>Initiator/Ansible model is better than polling a server/Puppet. No need to invent (GEO) caches on 100+ nodes Puppet-setup.</p></li><li><p>Some Ansible code can be executed hourly, some monthly, some ad-hoc manually. Hardly possible in puppet.</p></li><li><p>No need to install agent software on node. Python is enough.</p></li></ul><h3 id=\"the-same\">The SAME</h3><ul><li>It is difficult to find a module per your needs. Really, how can I trust the author of a module? Some modules provide what I need but abandoned, some are new but too simple.</li></ul><h3 id=\"ansible-cons\">Ansible CONS</h3><ul><li>Yaml sucks. Those nasty spaces VS tabs, formatting is something I havent been familiar with: some notions of YAML structure ( multiline VS single line). And YAML looks like Windows Registry!</li><li>Bad reports. Really, I need to know what happened after Ansible run &amp; track history of runs.</li><li>Bad logging. What I am seeing on the screen, doesnt reflect major things I expect: what changed &amp; how. How means the diff betwen it had been &amp; had became.</li><li>Speed. O Lord, Ansible is slow. It takes 40 sec to apply a playbook with SSHD + Fail2ban + NRPE + APT roles. Technically it means to place a bunch of files based on templates &amp; restart some services upon changes. I bet on Puppet it would take 10 sec.</li></ul></description>\n</item>\n</channel>\n</rss>"}, {"title": "Why Ansible? Ansible vs Puppet - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/ansible-puppet/", "html": "<PERSON>’ tech & personal blog\nTags\nWhy Ansible? Ansible vs Puppet\nJan 23, 2019\n#ansible , #puppet\n2 minute read\nAnsible PROS\n\nFor each “piece of code” you have nodes where code will be executed. In puppet thou, it is vice versa.\n\nOrchestration. Macro events can be scheduled easily i.e. inter-nodes dependencies. Inter-action dependencies. E.g. live migration of the whole distributed cloud web app ( all the load balancers, web-/db-/cache servers) from one cloud vendor to another, can be achieved with an Ansible playbook. Because Ansible is declarative language that in some cases can be used as a Bash script.\n\nconsequences are important & can be leveraged. E.g. action1 on nodes1 before action2 on nodes2. In Puppet nodes don’t know about each other.\n\nAnsible works via SSH connection, so you even can reboot the server & wait till it is booted up again, then execute something on it right after reboot. Everything via Ansible.\n\nsigning/revoking SSL certificates in Puppet is s hell comparing to SSH access which is enough for Ansible. With Ansible you only need SSH access, no agents/masters.\n\nInitiator/Ansible model is better than polling a server/Puppet. No need to invent (GEO) caches on 100+ nodes Puppet-setup.\n\nSome Ansible code can be executed hourly, some monthly, some ad-hoc manually. Hardly possible in puppet.\n\nNo need to install agent software on node. Python is enough.\n\nThe SAME\nIt is difficult to find a module per your needs. Really, how can I trust the author of a module? Some modules provide what I need but abandoned, some are new but too simple.\nAnsible CONS\nYaml sucks. Those nasty spaces VS tabs, formatting is something I havent been familiar with: some notions of YAML structure ( multiline VS single line). And YAML looks like Windows Registry!\nBad reports. Really, I need to know what happened after Ansible run & track history of runs.\nBad logging. What I am seeing on the screen, doesnt reflect major things I expect: what changed & how. How means the diff betwen it had been & had became.\nSpeed. O Lord, Ansible is slow. It takes 40 sec to apply a playbook with SSHD + Fail2ban + NRPE + APT roles. Technically it means to place a bunch of files based on templates & restart some services upon changes. I bet on Puppet it would take 10 sec.\nUbuntu: migrate from Ifupdown to Netplan. Step by step. \nRendered by Hugo | Subscribe"}, {"title": "netplan - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/netplan/", "html": "<PERSON>’ tech & personal blog\nTags\n#netplan\n2019\nUbuntu: migrate from Ifupdown to Netplan. Step by step.\nFeb 7\nRendered by <PERSON> | Subscribe"}, {"title": "Ubuntu: migrate from Ifupdown to Netplan. Step by step. - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/ifupdown-netplan/", "html": "<PERSON>’ tech & personal blog\nTags\nUbuntu: migrate from Ifupdown to Netplan. Step by step.\nFeb 7, 2019\n#ubuntu , #netplan\nOne minute read\n\nIn Ubuntu 18.04 old good ifupdown is considered obsolete. And you need to migrate your servers to systemd-networkd + netplan. Lots of internet resources describe how to revert back to ifupdown but I am hailing the future! So this manual covers 2 situations, static networking & DHCP. Both are IPv4.\n\nsystemctl disable networking.service \napt-get remove ifupdown -y\n\napt-get install netplan.io libnet-cidr-perl -y\nsystemctl enable systemd-networkd\n\nDEV=`facter networking.primary`\n\nrm /etc/netplan/*\n\ngrep dhcp /etc/network/interfaces \n\n\nNow follow either DHCP or Static part.\n\n######## DHCP START\necho \"network:\n  version: 2\n  renderer: networkd\n  ethernets:\n    $DEV:\n      nameservers:\n          addresses: [*******]\n      dhcp4: yes\" | tee /etc/netplan/01-netcfg.yaml\n\n######## DHCP END\n\n###### STATIC START\n\nGW=$( ip ro get *******  | grep -P '(?<=via )[\\d\\.]+(?= )' -o )\nNETMASK=$( facter networking.netmask )\nCIDR=$( perl -e 'use Net::CIDR; print \\\n    Net::CIDR::addrandmask2cidr(\"'$IP'\", \"'$NETMASK'\").\"\\n\"; ' ) \nIP=$( facter networking.ip )\n\necho \"network:\n  version: 2\n  renderer: networkd\n  ethernets:\n    $DEV:\n      addresses:\n        - $IP/24\n      gateway4: $GW\n      nameservers:\n          addresses: [*******]\" | tee   /etc/netplan/01-netcfg.yaml \n\n###### STATIC END\n\n\nThen apply new conf & see its status in networkctl\n\nnetplan apply\nsystemctl stop  networking.service\nsleep 2; networkctl list $DEV\n\n\nAnd reboot the server!\n\n Why Ansible? Ansible vs Puppet\nPsiphon: setting up own server & Linux client \nRendered by Hugo | Subscribe"}, {"title": "Psiphon: setting up own server & Linux client - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/psiphon-own/", "html": "<PERSON> tech & personal blog\nTags\nPsiphon: setting up own server & Linux client\nSep 5, 2020\n#ubuntu , #psiphon , #proxy\n2 minute read\n\nPsiphon is obfuscated proxy, and its client & server are open-source.\n\nThe plan is:\n\nsetting up own Psiphon server\nsetting up Psiphon Client on Linux that connects to your own Psiphon server\nPsiphon Server\n\nNotes:\n\nconsider changing OSSH port from 9991 to something cool e.g. 22, 443 etc…\nwhen you change server port or server IP, regenerate the token & update the clients.\nmultiple protocols, on different ports, they all will be tried by a client. List of available protocols :\n\n“SSH”, “OSSH”, “UNFRONTED-MEEK-OSSH”, “UNFRONTED-MEEK-HTTPS-OSSH”, “UNFRONTED-MEEK-SESSION-TICKET-OSSH”, “FRONTED-MEEK-OSSH”, “FRONTED-MEEK-QUIC-OSSH”, “FRONTED-MEEK-HTTP-OSSH”, “QUIC-OSSH”, “TAPDANCE-OSSH”, abd “CONJURE-OSSH”.\n\nmkdir -p /opt/psiphond-server/\ncd /opt/psiphond-server/\ncurl https://github.com/Psiphon-Labs/psiphon-tunnel-core-binaries/raw/master/psiphond/psiphond -o psiphond -L\nchmod 755 psiphond\nMYIP=`curl -4 https://api.ipify.org -Ss`\necho $MYIP\n./psiphond -ipaddress $MYIP  -protocol OSSH:9991 -protocol QUIC-OSSH:9996  -protocol UNFRONTED-MEEK-OSSH:9999 generate\ncat server-entry.dat ; echo\n\n\nOutput: ~2500 letters and digits. It is the token for clients ; save it.\n\nRun Psiphon server manually …\n\n./psiphond  run\n\n\n.. or stop & create Systemd unit:\n\nvim /etc/systemd/system/psiphond.service \n\n[Unit]\nAfter=network.target\n\n[Service]\nExecStart=/opt/psiphond-server/psiphond run\nType=simple\nWorkingDirectory=/opt/psiphond-server\n\n[Install]\nWantedBy=default.target\n\n\nEnable the unit:\n\nsystemctl daemon-reload\nsystemctl start psiphond.service\nsystemctl enable psiphond.service\nsystemctl status  psiphond.service\n\nPsiphon Client\nrm -rf ~/psiphon.client.to.own.server\nmkdir -p ~/psiphon.client.to.own.server\ncd ~/psiphon.client.to.own.server\ncurl https://github.com/Psiphon-Labs/psiphon-tunnel-core-binaries/raw/master/linux/psiphon-tunnel-core-x86_64 -o psiphon-tunnel-core-x86_64 -L\nchmod 755 psiphon-tunnel-core-x86_64\nvim client.own.conf\n\n\npaste text below but replace __TOKEN__ to the Token from step above.\n\n{\n    \"LocalHttpProxyPort\" : 8081,\n    \"LocalSocksProxyPort\" : 1081,\n    \"PropagationChannelId\" : \"FFFFFFFFFFFFFFFF\",\n    \"SponsorId\" :            \"FFFFFFFFFFFFFFFF\",\n    \"TargetServerEntry\" : \"__TOKEN__\"\n}\n\necho './psiphon-tunnel-core-x86_64 -config client.own.conf' > run.client\nchmod 755 run.client\n./run.client\n\n\nIt will start Http proxy on local port 8081 and Socks proxy on port 1081. Test those proxies in another console.\n\ncurl -x localhost:8081 gmail.com -v \n\n\nThat gave me 700KB/s speed.\n\nEDIT 2023-01-09\n\ntypos fixed\n Ubuntu: migrate from Ifupdown to Netplan. Step by step.\nPsiphon: setting up Linux client (with free servers) \nRendered by Hugo | Subscribe"}, {"title": "proxy - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/proxy/", "html": "<PERSON>’ tech & personal blog\nTags\n#proxy\n2020\nPsiphon: setting up own server & Linux client\nSep 5\nPsiphon: setting up Linux client (with free servers)\nSep 5\nRendered by <PERSON> | Subscribe"}, {"title": "psiphon - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/psiphon/", "html": "<PERSON>’ tech & personal blog\nTags\n#psiphon\n2020\nPsiphon: setting up own server & Linux client\nSep 5\nPsiphon: setting up Linux client (with free servers)\nSep 5\nRendered by <PERSON> | Subscribe"}, {"title": "ubuntu - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/ubuntu/", "html": "<PERSON>’ tech & personal blog\nTags\n#ubuntu\n2020\nPsiphon: setting up own server & Linux client\nSep 5\nPsiphon: setting up Linux client (with free servers)\nSep 5\n2019\nUbuntu: migrate from Ifupdown to Netplan. Step by step.\nFeb 7\nRendered by <PERSON> | Subscribe"}, {"title": "Psiphon: setting up Linux client (with free servers) - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/psiphon-free/", "html": "<PERSON> tech & personal blog\nTags\nPsiphon: setting up Linux client (with free servers)\nSep 5, 2020\n#ubuntu , #psiphon , #proxy\n2 minute read\n\nPsiphon is obfuscated proxy, and its client & server are open-source.\n\nThe plan is:\n\nsetting up Linux Psiphon Client that connects to free Psiphon network.\nrm -rf ~/psiphon.client.free\nmkdir -p ~/psiphon.client.free\ncd ~/psiphon.client.free\ncurl https://github.com/Psiphon-Labs/psiphon-tunnel-core-binaries/raw/master/linux/psiphon-tunnel-core-x86_64 -L -o psiphon-tunnel-core-x86_64\nchmod 755 psiphon-tunnel-core-x86_64\nvim client.free.conf\n\n\npaste text below\n\n{\n\"LocalHttpProxyPort\":8081,\n\"LocalSocksProxyPort\":1081,\n\"PropagationChannelId\":\"FFFFFFFFFFFFFFFF\",\n\"RemoteServerListDownloadFilename\":\"remote_server_list\",\n\"RemoteServerListSignaturePublicKey\":\"MIICIDANBgkqhkiG9w0BAQEFAAOCAg0AMIICCAKCAgEAt7Ls+/39r+T6zNW7GiVpJfzq/xvL9SBH5rIFnk0RXYEYavax3WS6HOD35eTAqn8AniOwiH+DOkvgSKF2caqk/y1dfq47Pdymtwzp9ikpB1C5OfAysXzBiwVJlCdajBKvBZDerV1cMvRzCKvKwRmvDmHgphQQ7WfXIGbRbmmk6opMBh3roE42KcotLFtqp0RRwLtcBRNtCdsrVsjiI1Lqz/lH+T61sGjSjQ3CHMuZYSQJZo/KrvzgQXpkaCTdbObxHqb6/+i1qaVOfEsvjoiyzTxJADvSytVtcTjijhPEV6XskJVHE1Zgl+7rATr/pDQkw6DPCNBS1+Y6fy7GstZALQXwEDN/qhQI9kWkHijT8ns+i1vGg00Mk/6J75arLhqcodWsdeG/M/moWgqQAnlZAGVtJI1OgeF5fsPpXu4kctOfuZlGjVZXQNW34aOzm8r8S0eVZitPlbhcPiR4gT/aSMz/wd8lZlzZYsje/Jr8u/YtlwjjreZrGRmG8KMOzukV3lLmMppXFMvl4bxv6YFEmIuTsOhbLTwFgh7KYNjodLj/LsqRVfwz31PgWQFTEPICV7GCvgVlPRxnofqKSjgTWI4mxDhBpVcATvaoBl1L/6WLbFvBsoAUBItWwctO2xalKxF5szhGm8lccoc5MZr8kfE0uxMgsxz4er68iCID+rsCAQM=\",\n\"RemoteServerListUrl\":\"https://s3.amazonaws.com//psiphon/web/mjr4-p23r-puwl/server_list_compressed\",\n\"SponsorId\":\"FFFFFFFFFFFFFFFF\",\n\"UseIndistinguishableTLS\":true\n}\n\necho './psiphon-tunnel-core-x86_64 -config client.free.conf' > run.client\nchmod 755 run.client\n./run.client\n\n\nIt will start Http proxy on local port 8081 and Socks proxy on port 1081. Test those proxies in another console.\n\ncurl -x localhost:8081 gmail.com -v \n\n\nThat gave me 150KB/s speed.\n\nSolve the situation when you are already under DPI.\n\nAs you can see in free client config, it downloads servers list from RemoteServerListUrl. Of course you can download it beforehand & point Psiphon client to some arbitrary Psiphon server . Get server tokens list:\n\ncd ~/tmp/\nwget https://s3.amazonaws.com//psiphon/web/mjr4-p23r-puwl/server_list_compressed\n\n printf \"\\x1f\\x8b\\x08\\x00\\x00\\x00\\x00\\x00\" |cat - server_list_compressed |gzip -dc | json_xs | grep '\"data\"' | awk -F\\\" '{print $4}' | sed \"s@\\\\\\n@\\n\\n\\n\\n@g\"  > server_tokens.txt\n\nless server_tokens.txt\n\n\nEach line is then a token. Use client config like this ( client.free_servers.1.conf ) :\n\n( replace __TOKEN__ to some token from the file server_tokens.txt )\n\n{\n    \"LocalHttpProxyPort\" : 8081,\n    \"LocalSocksProxyPort\" : 1081,\n    \"PropagationChannelId\" : \"FFFFFFFFFFFFFFFF\",\n    \"SponsorId\" :            \"FFFFFFFFFFFFFFFF\",\n    \"TargetServerEntry\" : \"__TOKEN__\"\n}\n\n./psiphon-tunnel-core-x86_64 -config  client.free_servers.1.conf\n\n Psiphon: setting up own server & Linux client\nFighting against Passive OS Fingerprinting \nRendered by Hugo | Subscribe"}, {"title": "proxidize - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/proxidize/", "html": "<PERSON> tech & personal blog\nTags\n#proxidize\n2022\nProxysmart: my alternative to Proxidize\nMar 10\nRendered by <PERSON> | Subscribe"}, {"title": "Fighting against Passive OS Fingerprinting - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/osfooler-ng/", "html": "<PERSON>’ tech & personal blog\nTags\nFighting against Passive OS Fingerprinting\nNov 3, 2020\n#linux , #networking\nOne minute read\n\nThe article describes steps to install OSfooler-ng to prevent your host OS be recognized by Passive OS Fingerpringting (p0f).\n\nI used a fork of OSfooler-ng because original project seems to be abandoned. Moreover the fork can be installed on modern Linux distros and doesn’t require old packages.\n\nCheck the fork’s page: https://github.com/moonbaseDelta/OSfooler-ng\n\nCentos 7\nyum install epel-release\nyum install python git python-pip wget gcc make python-devel libnetfilter_queue-devel libnfnetlink-devel \n\nCentos 8\nsed -i s/enabled=0/enabled=1/ /etc/yum.repos.d/CentOS-PowerTools.repo \nyum install epel-release\nyum install python2 git python2-pip wget gcc make python2-devel libnetfilter_queue-devel libnfnetlink-devel\n\nUbuntu 18.04\napt install libnetfilter-queue-dev libnfnetlink-dev python python-setuptools python-pip\n\nInstall OSfooler-ng\n\nThis step and following steps are OS-independant.\n\npip2 install NetfilterQueue\ngit clone https://github.com/moonbaseDelta/OSfooler-ng ; cd OSfooler-ng\npython2 setup.py install\nosfooler-ng -u \nosfooler-ng -o Windows  -d \"SP3\" -i tun0 \n\nCheck\ncurl http://witch.valdikss.org.ru/ -Ss | sed -n '/^<pre/,/NTL/p'\n\n\nShould return Detected OS = Windows NT kernel [generic]\n\nOr visit https://whatleaks.com or https://doileak.com or https://whoer.net\n\nNotes\nsome home routers do change TCP options, so consider using a VPN to the cloud for tests\nVirtualbox also may change TCP options, so GuestVM->Host->VPN may not work because TCP opts are changed between GuestVM->Host.\nfor tests you can use P0F (https://linux.die.net/man/1/p0f) on remote side.\n Psiphon: setting up Linux client (with free servers)\nProxysmart: my alternative to Proxidize \nRendered by Hugo | Subscribe"}, {"title": "Proxysmart: my alternative to Proxidize - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/proxidize_alternative/", "html": "<PERSON> tech & personal blog\nTags\nProxysmart: my alternative to Proxidize\nMar 10, 2022\n#proxidize , #modems , #proxies\nOne minute read\n\nProxidize significally raised prices on their service and I have a drop-in replacement.\n\nI have been developing 4g proxy farm software Proxysmart which is a set of software for building your own 4g\\LTE proxy farm. Proxidize is a big competitor, and they have raised prices, i.e. up to $20 per modem per month. It is something a common “4g proxy” farmer can’t afford. So recently multiple ex-proxidize customers reached out to me. They cancelled the conracts with Proxidize and all the Proxidize hardware was left: in particular 4g modems.\n\nSo I offered them my service and built new 4g proxy farm for them, on top of old hardware, with significally lower price, yet it was one time payment , not recurring, like Proxidize charged them.\n\n Fighting against Passive OS Fingerprinting\nAdded new modems models to Proxysmart \nRendered by Hugo | Subscribe"}, {"title": "USA 4G\\LTE modems - <PERSON> tech & personal blog", "url": "https://blog.tanatos.org/posts/usa_modems/", "html": "<PERSON> tech & personal blog\nTags\nUSA 4G\\LTE modems\nMay 21, 2022\nModified on Jan 25, 2024\n#modems , #proxies\n2 minute read\n\nThe following modems should be working in the USA e.g. with my 4g proxy farm software Proxysmart software.\n\nMain LTE bands in the USA:\n\nFDD B2 1900 Mhz\nFDD B4 1700 Mhz\nTL;DR;\n\nMost popular and easy to get modems for the USA:\n\nAlcatel IK41US, IK41UC\nQuectel EC25-AFX, EC25-AF\nSierra Wireless EM7455\nUSB dongles\nAlcatel\n\nAlcatel IK41UC https://www.ebay.com/itm/************ https://www.ebay.com/itm/************\n\nB2/4/5/7/12/13/17/71\n\nAlcatel IK41US\n\nhttps://www.ebay.com/itm/************\n\nAlcatel MW41NF https://www.amazon.com/Alcatel-LINKZONE-Unlocked-MW41NF-2AOFUS2-T-Mobile/dp/B08D4PB55J/\n\nLTE: B1/2/3/4/5/8/12/13/17/20/28\n\nAlcatel MW41TM https://www.ebay.com/itm/184607733572 https://www.amazon.com/Alcatel-LINKZONE-MW41TM-Download-Anywhere/dp/B07791Y58K/ https://aliexpress.com/item/1005004421203808.html\n\nFDD LTE: B2/4/12\n\nAlcatel MW43TM\n\nhttps://www.ebay.com/itm/385371469463\n\nFDD LTE: 2/4/5/12/25/26/66/71\n\nAlcatel LINKZONE 2 (not supported in Proxysmart )\n\nhttps://us.alcatelmobile.com/alcatel-linkzone-2/\n\nLTE FDD: 2/4/5/12/25/26/66/71\n\nZTE\n\nZTE MF971, https://aliexpress.com/item/1728282949.html , https://www.amazon.com/Router-Hotspot-ZTE-Unlocked-Caribbean/dp/B0775ZQCYD/\n\nB1/2/3/4/5/7/8/17&12/20/28\n\nZTE Velocity MF985 (not supported in Proxysmart )\n\nhttps://www.ebay.com/itm/265698369028 , https://aliexpress.com/item/1005003911031389.html\n\nLte Bands 2, 4, 5, 12, 29, 30, 66\n\nZTE MF279T (not supported in Proxysmart )\n\nhttps://www.amazon.com/ZTE-Wireless-Internet-Device-Unlocked/dp/B07PLSTM1K/\n\nLte Bands 2, 4, 5, 12, 29, 30\n\nZTE Velocity MF923 https://www.amazon.com/ZTE-Velocity-Mobile-Hotspot-Unlocked/dp/B0747PF6P6/\n\nLTE bands 2/4/5/17/29\n\nZTE MF833V https://www.amazon.com/ZTE-MF833V-Customized-Version-Covering/dp/B07XXBQPZL/\n\nLTE-FDD B1/2/3/4/5/7/8/12/17/20\n\nHuawei\n\nE3372H-510 https://www.ebay.com/itm/394116161904\n\nLTE 1,2,4,5,7,28\n\nE8372h-511 https://aliexpress.com/item/32826491455.html\n\n4G LTE: Band 1, 2, 4, 5,17\n\nE8372h-510 https://aliexpress.com/item/4001209567895.html\n\nBand 1, 2, 4, 5, 7, 28\n\nE8372h-517 https://aliexpress.com/item/4000374160998.html https://aliexpress.com/item/32727646816.html\n\nBand 1, 2, 4, 5, 7, 12, 17\n\nE3276s-500 https://aliexpress.com/item/1966339368.html\n\nB 2/4/5/7\n\nHuawei MS2372h-517 , MS2372h-518 (not supported in Proxysmart )\n\nhttps://www.ebay.com/itm/325139289134 https://www.ebay.com/itm/275602522962 https://www.ebay.com/itm/295546421021\n\nLTE FDD B1/B2/B4/B5/B7/B12/B28\n\nE8278s-603 https://aliexpress.com/item/32904377087.html (not supported in Proxysmart )\n\nB 1/2/4/5/8/40\n\nE5788 https://aliexpress.com/item/4000583132418.html\n\n4G LTE: 1/2/3/4/5/7/8/19/20/28/38/40/41/42 , Cat16\n\nHuawei 603HW Pocket WiFi (not supported in Proxysmart )\n\nhttps://aliexpress.com/item/4000250805947.html\n\nFDD-LTE B1 (2100 МГц), B2(1900 МГц), B4(1700/2100 МГц), B8(900 МГц), B11 z), B12(700 МГц), b17 (700bMHz), B18/26 ( z), B25( ), B26( )\n\nOther\n\nFranklin T9 and Franklin T10\n\nJetpack AC791L\n\nLAN routers\n\nScheme of the setup\n\nThey don’t have USB support, only LAN\\WIFI.\n\nHuawei\n\nHuawei B612s-51d (not supported in Proxysmart )\n\nhttps://www.amazon.com/Huawei-B612s-51d-Unlocked-Caribbean-Renewed/dp/B08GKZGSXC/\n\nhttps://www.amazon.com/Huawei-B612s-51d-Router-Unlocked-Caribbean/dp/B07RC8JSMM\n\nLTE Bands: B2/B4/B5/B7/B41 LTE Cat.6\n\nHuawei B311-521 https://www.amazon.com/Huawei-B311-521-Unlocked-Venezuela-Caribbean/dp/B094W8H943/\n\nLTE Bands: 2/4/5/7/28/66\n\nHuawei B890 (not supported in Proxysmart ) https://www.amazon.com/Huawei-Wireless-Gateway-Mobile-Unlocked/dp/B01B5DQGV2/\n\nLTE FDD Band 2/4/5/7/12/13/17\n\nmore read: https://www.4gltemall.com/4g-wireless-router/huawei-4g-lte-wifi-router.html\n\nZTE\n\nZTE MC7010CA\n\nZTE MC8010\n\nLTE modules\nQuectel\nModule (chip) and USB box + antennas: separately\n\nModule (chip) itself: https://www.aliexpress.com/item/1005002833181873.html or https://www.ebay.com/itm/384449850486\n\nUSB box + antennas: https://www.aliexpress.com/item/32970948654.html or https://www.ebay.com/itm/123427557392\n\nOr, everything in 1 piece (chip + USB box):\n\nhttps://aliexpress.com/item/1005003984632017.html\n\nStill antennas are needed.\n\nChip versions:\n\nEC25-AF (Verizon, AT&T, T-mobile, Rogers)\n\nB 2,4,5,12,13,14,66,71\n\nEC25-AFX (Verizon, AT&T)\n\nB 2,4,5,12,13,14,66,71\n\nSierra Wireless\n\nSierra Wireless EM7455\n\nAlso M.2-USB adapter and antennas are needed;\n\nSierra Wireless EM7565\n\nHow to find suitable modems\n\nOn AliExpress or Ebay, look up for “Huawei LTE band 2” “ZTE 4g band 4” or similar queries.\n\n Added new modems models to Proxysmart\nIdeal Proxy Software \nRendered by Hugo | Subscribe"}, {"title": "Ideal Proxy Software - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/ideal_proxy/", "html": "<PERSON>’ tech & personal blog\nTags\nIdeal Proxy Software\nJun 11, 2022\nModified on Feb 7, 2024\n#proxies\nOne minute read\n\nA little comparison of open source proxy software: Haproxy, 3proxy, Squid, Nginx, Gost (2), V2ray\n\n\n                                  Haproxy 3proxy Squid Nginx Gost2 V2ray\ncan send\\receive PROXY PROTOCOL   y       n       y    y     n    n\ncan listen on HTTP                y       y       y    y     y    y \ncan listen on HTTPS               y       n       y    y     n    n\ncan listen on SOCKS5              n       y       n    n     y    y\ncan send to parent HTTP proxy     y       y       y    y     y    y\ncan send to parent SOCKS5 proxy   y       y       n    n     y    y\ncan listen as transparent proxy   y       y       y    y     n    n\ncan send to transparent proxy     y       n       y    y     n    n\nhas ACL (domains, src\\dst IP)     y       y       y    y     n    y\nhas password-auth                 y       y       y    y     y    y\nhas bandwidth speedlimit          n       y       y    y     n    n\nhas bandwidth quota               n       y       y    n     n    y\nhas bandwidth counter             ~y      y       ~y   ~y    n    ~y\n\n\n~ means “almost”\n\n USA 4G\\LTE modems\nHow to build VPN from PROXY \nRendered by Hugo | Subscribe"}, {"title": "Added new modems models to Proxysmart - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/newmodems/", "html": "<PERSON>’ tech & personal blog\nTags\nAdded new modems models to Proxysmart\nApr 25, 2022\n#modems , #proxies\nOne minute read\n\nRecently I have added new modems models to my 4g proxy farm software Proxysmart, in particular Quectel family and ZTE MF79 and ZTE MF971. For each, it is possible to read SMS, and reset IP.\n\nZTE MF79, ZTE MF971\n\nThose are new ZTE models that the cell operators start selling to the customers. Their WEB API is pretty the same as of previous ZTE models, except one thing. It requires authentication and hassling with cookies and tokens encryption. So it took some time to reverse its API.\n\nQuectel series\n\nThose are very promising, because they from the beginning work in NCM mode, yet they have awesome open API documentation, you have full control of almost every aspect of them. Yet it is possible to change their IMEI, which is useful for bypassing cell operators restrictions of tethering.\n\n Proxysmart: my alternative to Proxidize\nUSA 4G\\LTE modems \nRendered by Hugo | Subscribe"}, {"title": "vpn - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/vpn/", "html": "<PERSON>’ tech & personal blog\nTags\n#vpn\n2022\nHow to build VPN from PROXY\nAug 12\nRendered by <PERSON> | Subscribe"}, {"title": "4g - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/4g/", "html": "<PERSON>’ tech & personal blog\nTags\n#4g\n2022\nHow to build proxies from 4G/5G LAN routers\nSep 2\nRendered by <PERSON> | Subscribe"}, {"title": "How to build VPN from PROXY - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/vpn2socks/", "html": "<PERSON>’ tech & personal blog\nTags\nHow to build VPN from PROXY\nAug 12, 2022\n#proxies , #vpn\nOne minute read\n\nRecently I built a solution for making VPN from Proxies. Any proxies are supported (HTTP, SOCKS5) and a resulting Openvpn user traffic is routed via the mapped proxy.\n\nEvery Openvpn user has its own backend proxy.\n\nOf course only TCP is supported because majority of proxies don’t support UDP. And it is enough for browsing \\ scraping.\n\nThe main tricky think was routing DNS packets via a proxy. So I used DoH (DNS-over-HTTPS).\n\nHow it works:\n\nFor each vpn client it starts TCP redirector which establishes all TCP connections through a proxy\nFor each vpn client it starts DNS caching server & DNS resolver. DNS is fwd’ed to local DNS caching server, then to local DNS resolver, then (via proxy) to a public DNS DoH resolver like Google\n\nUsed tools:\n\nhttps://github.com/ginuerzh/gost for DNS cache\nhttps://github.com/aarond10/https_dns_proxy for DNS resolver via a backend proxy, using DoH\nOpenvpn, Linux, bash :-)\n\nIt is briefly described here: https://askubuntu.com/a/1423292/906035\n\n Ideal Proxy Software\nHow to build proxies from 4G/5G LAN routers \nRendered by <PERSON> | Subscribe"}, {"title": "networking - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/networking/", "html": "<PERSON>’ tech & personal blog\nTags\n#networking\n2022\nHow to Change OS TCP/IP Fingerprint\nOct 3\n2020\nFighting against Passive OS Fingerprinting\nNov 3\nRendered by <PERSON> | Subscribe"}, {"title": "How to build proxies from 4G/5G LAN routers - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/build_proxies_from_4g_lan_routers/", "html": "<PERSON>’ tech & personal blog\nTags\nHow to build proxies from 4G/5G LAN routers\nSep 2, 2022\n#proxies , #4g\nOne minute read\n\nRecently I added support of 4G or 5G LAN routers like Huawei Bxxx, to my 4G proxy farm software Proxysmart software:\n\nHuawei B311\nHuawei B525\nHuawei B628\n\netc\n\nThey are not USB modems, so have to be put in a separate LAN + an extra Ethernet switch is used. The scheme is below.\n\n How to build VPN from PROXY\nHow to Change OS TCP/IP Fingerprint \nRendered by <PERSON> | Subscribe"}, {"title": "How to Change OS TCP/IP Fingerprint - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/os_tcp_fingerprint_2022/", "html": "<PERSON>’ tech & personal blog\nTags\nHow to Change OS TCP/IP Fingerprint\nOct 3, 2022\n#linux , #networking\n4 minute read\n\nSome useful info & tips on OS TCP/IP Fingerprint.\n\nPassive OS TCP/IP Fingerprinting, how it works\n\nClient opens a connection, sends TCP SYN, and somebody inbetween analyzes it. This technique is named p0f. Only TCP SYN is unique for each OS, so OS name & version & even uptime can be extracted from it. In particular:\n\nip ttl (p0f3: ittl)\ntcp mss (p0f3: mss)\ntcp options sequence (p0f3: olayout)\ntcp window size (p0f3: wsize)\ntcp window scaling (p0f3: scale)\ntcp quirks\n\nMore read: https://github.com/laoshaw/p0f3 => scroll down to “== TCP signatures ==”\n\nAll subsequent packets from client to the server are not unique: TCP ACK,RST,FIN etc.\n\nUptime is detected by a TS (timestamp) option in tcp options sequence, which contains 2 values, and TSval correlates with uptime.\n\nActive OS TCP/IP Fingerprinting, how it works\n\nAttacker (well, it is not an attack, but anyways) sends a TCP SYN to the object, connection established, TCP SYN+ACK is sent back & analyzed by the attacker. The same factors do matter as for TCP SYN, but signatures do differ.\n\nSoftware used for p0f (CLI)\nhttps://github.com/MOL0ToK/sp0ky\nhttps://github.com/cesarghali/OS-Fingerprinting\nhttps://github.com/laoshaw/p0f3\nhttps://github.com/NikolaiT/zardaxt\ntcpdump / tshark (for checking raw signatures)\nWEB services for p0f\n\nThose use p0f3 or very similar:\n\nhttp://witch.valdikss.org.ru/\nhttps://browserleaks.com/ip\nhttps://whoer.net - click “Extended version”\n\nUses its own set of signatures:\n\nhttps://tcpip.incolumitas.com/classify?by_ip=1\nDatabases of signatures\n\nhttps://raw.githubusercontent.com/laoshaw/p0f3/master/p0f.fp p0f3 , TCP:request + TCP:response\n\nhttps://raw.githubusercontent.com/xnih/satori/master/fingerprints/tcp.xml p0f1 , TCP:request\n\nhttps://raw.githubusercontent.com/NikolaiT/zardaxt/master/database/combinedJune2022.json kind of p0f1 format , used in Incolumitas checker.\n\nProblem:\n\np0f3 database (not mentioning the p0f which is even more old!) is very outdated and don’t reflect modern situation with modern devices. For example, if you want to spoof TCP/IP fingerprint to pretent iOS, then even if you grab proper set of TCP options from real iOS, you still be recognized as MAC OSX generic. There is only 1 entry for iOS in p0f3, and when you start emulating it, yes, you will be seeing iPad or iPhone but it has a poor Wscale, so your connection will be starting too slowly.\n\nthe same is for Android. There are just 2 records in p0f3 for Android. While real Androids are visible as Linux generic or similar.\n\nFor Windows. p0f3 doesn’t have records for modern Windows versions, they are detected as Windows 7 or 8.\n\nSolution\n\nYou have 2 solutions\n\nGrab real TCP signatures from real devices and use them for TCP/IP OS spoofing. Pro: it will be real & genuine. Con: those public detectors will detect you as a wrong OS.\n\nUse OS TCP signatures from p0f3. Pro: those public detectors will detect you as a target OS. Con: some private detectors still may detect you as a spoofer :-) And expect some speed losses because of non-optimal Wscale.\n\nHow to spoof OS TCP/IP Fingerprint? Theory.\nLinux Iptables firewall diverts outgoing TCP SYN packets to a local NFQUEUE\nA software opens a Netfilter Queue, which accepts TCP packets.\nunpacks them, adjusts mandatory TCP options, re-packs & sends\n\nThe software is scapy, a python module.\n\nHow to spoof OS TCP/IP Fingerprint? Practically.\n\nThere is a good easy to use software called OSfooler-ng + its many forks. It is abandoned now. Also it uses ancient p0fv1 database. Also it simply can’t process TS options which are VERY important for simulating MAC/iOS. In fact it is only useful for simulating Windows.\n\nThere is a python module scapy-p0f, which uses p0f3 database, but still can’t process TS options. Yet it doesn’t have a ready to use program.\n\nSo the solution would be to take OSfooler-ng, modify it, so it works with scapy-p0f. I have done it, please DM me!\n\nResult\n\noriginal screenshot\n\nMacOSX\nOSGENRE=macosx ; DETAILS_P0F=1\nincolumitas\n\n{“Android”:5.39,“Linux”:4.4,“Mac OS”:11.3,“Windows”:2.4,“iOS”:11.05}\n\nvaldik\n\nDetected OS = Mac OS X 10.x;MTU = 1398;Network link = ???\n\nOSGENRE=macosx ; DETAILS_P0F=2\nincolumitas\n\n{ “Android”: 5.39, “Linux”: 4.39, “Mac OS”: 11.3, “Windows”: 2.42, “iOS”: 11.05 }\n\nvaldik\n\nDetected OS = Mac OS X 10.x ; MTU = 1398; Network link = ???\n\nOSGENRE=macosx DETAILS_P0F=3\nincolumitas\n\n{“Android”:5.39,“Linux”:4.42,“Mac OS”:11.35,“Windows”:2.44,“iOS”:11.06}\n\nvaldik\n\nDetected OS = Mac OS X 10.9 or newer (sometimes iPhone;MTU = 1398;Network link = ???\n\nIOS\nOSGENRE=ios ; DETAILS_P0F=1\nincolumitas\n\n{“Android”:5.43,“Linux”:4.48,“Mac OS”:11.32,“Windows”:2.7,“iOS”:11.07}\n\nvaldik\n\nDetected OS = Mac OS X iPhone or iPad;MTU = 1398;Network link = ???\n\nOSGENRE=ios ; DETAILS_P0F=2\nincolumitas\n\n{“Android”:5.41,“Linux”:4.44,“Mac OS”:11.92,“Windows”:2.4,“iOS”:11.93}\n\nvaldik\n\nDetected OS = Mac OS X [generic];MTU = 1398;Network link = ???\n\nWindows\nOSGENRE=windows DETAILS_P0F=1\nincolumitas\n\n{“Android”:5.01,“Linux”:4.24,“Mac OS”:2.89,“Windows”:13.66,“iOS”:2.78}\n\nvaldik\n\nDetected OS = Windows 7 or 8;MTU = 1398;Network link = ???\n\nOSGENRE=windows DETAILS_P0F=2\nincolumitas\n\n{“Android”:3.86,“Linux”:3.99,“Mac OS”:2.71,“Windows”:6.68,“iOS”:2.55}\n\nvaldik\n\nDetected OS = Windows 7 or 8;MTU = 1398;Network link = ???\n\nOSGENRE=windows DETAILS_P0F=3\nincolumitas\n\n{“Android”:4.19,“Linux”:4.25,“Mac OS”:2.91,“Windows”:12.57,“iOS”:2.81}\n\nvaldik\n\nDetected OS = Windows 7 or 8;MTU = 1398;Network link = ???\n\nAndroid\nOSGENRE=android DETAILS_P0F=1\nincolumitas\n\n{“Android”:10.23,“Linux”:10.82,“Mac OS”:2.52,“Windows”:3.6,“iOS”:2.47}\n\nvaldik\n\nDetected OS = Linux (Android);MTU = 1398;Network link = ???\n\n How to build proxies from 4G/5G LAN routers\nQuectel EC200T on OpenWRT \nRendered by Hugo | Subscribe"}, {"title": "Quectel EC200T on OpenWRT - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/quectel_ec200t_openwrt/", "html": "<PERSON> tech & personal blog\nTags\nQuectel EC200T on OpenWRT\nDec 16, 2022\n#modems\n3 minute read\n\nThe issue with EC200T that it doesn’t have QMI\\MBIM. It has only ECM and AT-port , so we have to start WAN connection on AT-port and use it via the WAN port.\n\nInstall some packages\n\nopkg update\nopkg install kmod-usb-net-cdc-ether kmod-usb-serial-option usbutils socat \n\nkmod-usb-net-cdc-ether for WWAN netcard device\nkmod-usb-serial-option for /dev/ttyUSB* devices\nusbutils for lsusb\nsocat for sending AT commands.\n\nPlug in the module (EC200T) in USB, wait ~30sec, run lsusb, interesting line is\n\nBus 002 Device 005: ID 2c7c:6026 Quectel Wireless Solutions Co., Ltd. \n\n\nVendor and Product ID are 2c7c:6026 , so bind USB-OPTION driver to the device.\n\necho  2c7c 6026 ff > /sys/bus/usb-serial/drivers/option1/new_id\n\n\nCheck if ttyUSB character devices are added:\n\nls /dev/ttyUSB*\n\n\nOutput:\n\ncrw-rw----    1 root     dialout   188,   0 Dec 16 10:46 /dev/ttyUSB0\ncrw-rw----    1 root     dialout   188,   1 Dec 16 10:46 /dev/ttyUSB1\ncrw-rw----    1 root     dialout   188,   2 Dec 16 10:46 /dev/ttyUSB2\n\n\nCheck if USB WWAN net card is added as usb0\n\nip  li  show\n\n\nOutput contains usb0.\n\nFor EC200T, AT port sits on /dev/ttyUSB1. Let’s talk there.\n\nAT_PORT=/dev/ttyUSB1\nSOCAT_RUN=\"socat - $AT_PORT,crnl\"\necho ATE0 | $SOCAT_RUN\necho ATI | $SOCAT_RUN\n\n\nOutput:\n\nQuectel\nEC200T\nRevision: EC200TEUHAR05A01M16\n\n\nOK now let’s create a WAN interface with DHCP client.\n\nEdit /etc/config/network , add\n\nconfig interface 'QUECTEL_EC200T'\n        option proto 'dhcp'\n        option ifname 'usb0'\n        option metric '200'\n\n\nThen restart the network /etc/init.d/network restart , Openwrt will start sending DHCP requests on usb0 but it will not respond (yet).\n\nAdd a file /etc/init.d/quectel_EC200 and paste the content and make it 755\n\n#!/bin/sh /etc/rc.common\n\nSTART=98\n\nstart(){\n\nset -x\necho Start\n\nAT_PORT=/dev/ttyUSB1\nDEV=usb0\nSOCAT_RUN=\"socat - $AT_PORT,crnl\"\n\nif test -c $AT_PORT\nthen echo port ready\nelse\n\techo  2c7c 6026 ff > /sys/bus/usb-serial/drivers/option1/new_id\n\techo sleep till port is ready\n\tsleep 30\n\tfor i in $(seq 10);\n\tdo\n\t    test -c $AT_PORT && break\n\t    echo .\n\t    sleep 10\n\tdone\nfi\n\ntest -c $AT_PORT || { echo \"$AT_PORT cant be found, exit\"; return 22; }\n\necho ATE0 | $SOCAT_RUN\necho ATI | $SOCAT_RUN\necho 'AT+QCFG=\"usbnet\"' | $SOCAT_RUN\necho at+cgdcont? | $SOCAT_RUN\necho at+cgatt? | $SOCAT_RUN\necho at+cgact? | $SOCAT_RUN\necho at+cgact=1,1 | $SOCAT_RUN\necho AT+CGCONTRDP=1 | $SOCAT_RUN\necho AT+QNETDEVCTL=1,1,1 | $SOCAT_RUN ;  sleep 1\necho AT+QNETDEVCTL=1,1,1 | $SOCAT_RUN ;  sleep 1\necho AT+QNETDEVCTL=1,1,1 | $SOCAT_RUN ;  sleep 1\necho AT+QNETDEVCTL? | $SOCAT_RUN\n\n}\n\nstop(){\n    echo Stop\n}\n\n\nRun it, it will connect the module to Internet\n\n/etc/init.d/quectel_EC200 start\n\n\nand DHCP client in previous step will obtain an IP, so expect a new default gateway, with metric 200. Then check routing table\n\nip ro\n\n\nOutput:\n\n...\ndefault via ************ dev usb0  src ***********  metric 200\n...\n\n\nThen enable its autostart, edit /etc/hotplug.d/usb/10-quectel_ec200.sh and paste the content and make it 755\n\n#!/bin/sh\n\n[ \"$ACTION\" = add -a \"$DEVTYPE\" = usb_device ] || exit 0\n\nvid=$(cat /sys$DEVPATH/idVendor)\npid=$(cat /sys$DEVPATH/idProduct)\n\nif [[ $vid == 2c7c ]] && [[ $pid == 6026 ]]\nthen\ttrue\nelse\texit\nfi\n\nsleep 30 \n/etc/init.d/quectel_EC200 restart 2>&1 | logger -t quectel_EC200\n\n\nReboot the router, Quectel EC200T will be online.\n\nSummary of the flow:\nplug in the module\nhotplug will init the module and connect it online\nOpenwrt will detect its interface and start DHCP on it\nFurther reading:\nhttps://openwrt.org/docs/guide-user/base-system/hotplug\nhttps://openwrt.org/docs/guide-user/network/wan/wwan/ltedongle\nhttps://openwrt.org/docs/guide-user/network/wan/wwan/ethernetoverusb_ncm\n How to Change OS TCP/IP Fingerprint\nQUIC support in Socks5 Proxies \nRendered by Hugo | Subscribe"}, {"title": "QUIC support in Socks5 Proxies - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/quic/", "html": "<PERSON>’ tech & personal blog\nTags\nQUIC support in Socks5 Proxies\nDec 19, 2022\n#proxies\n2 minute read\n\nIn 2023 you will loose the battle when your proxies don’t support QUIC (HTTP/3.0). With 4g proxy farm software Proxysmart, you will win. I recently added QUIC support to my proxies building software.\n\nIndeed, when major websites like Instagram, FB detect that you run a modern Web browser on a modern OS on a modern hardware, and yet you can’t send the data over QUIC (HTTP/3.0) , your requests are red-flagged and your account may be banned.\n\nI already brought this topic on BHW, let me quote from there:\n\nHi, there is a concern about raising usage of QUIC protocol (HTTP 3.0) with combination of using a proxy in browser. QUIC is UDP. All modern browsers do support QUIC. Yet none of them support sending QUIC via a proxy. Even if the proxy itself can send UDP (most Socks5 proxies can’t but some can!).\n\nAnd when a website finds out you have a modern browser and can’t send QUIC, it’s a big red flag to consider you are a bot!\n\n3rd party solutions like Proxifier don’t support sending QUIC either.\n\nPossible solutions:\n\nwait till browsers can send QUIC via a proxy + proxy providers start adding UDP to their proxies\nwait till tools like Proxifier can forward browser’s UDP traffic via SOCKS5\nswitch to 4G\\5G VPN which are way more expensive.\n\nPeople answered, cheer up, proxies companies will solve it, but my opinion is, browser’s can’t send HTTP3 via any proxy now. So eventhough proxy providers adapted, it is useless.\n\nOK, if you’ve got a proxy, how to check if it’s UDP capable ? If it is, it can also do QUIC. I built a website, QUIC HTTP/3.0 checker to check it. Refresh it multiple times (1st requst is always HTTP/2!).\n\nAlso you can send a raw UDP packet, using udpchk.py by semigodking , if it sends, then the proxy is UDP capable.\n\nFurther info:\n\nhttps://www.youtube.com/watch?v=07n9BUkyn8o\nhttps://www.blackhatworld.com/seo/proxies-will-die-in-2023.1448749/\n Quectel EC200T on OpenWRT\nUSB hubs for 4g proxies farm \nRendered by Hugo | Subscribe"}, {"title": "USB hubs for 4g proxies farm - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/usb_hub/", "html": "<PERSON>’ tech & personal blog\nTags\nUSB hubs for 4g proxies farm\nJan 22, 2023\n#proxies\nOne minute read\n\nHow to buy best USB hub for a building a 4g proxy farm.\n\nMain things to check before buying\nWe don’t need USB 3.0\nSolid big charger, should look like a solid box 2\" x 2\" x 5\" , not like a cheap USB charger\nat least 1 Amper per 1 USB port\nGood USB hubs:\nOrico A3H13P2 11 ports\nOrico P12-U3 12 ports\nOrico P10-U3 10 ports\nOrico Industrial USB Hub, 30 Port\nOrico Industrial USB Hub, 20 Ports\nOrico Industrial USB Hub, 10 Ports\nSipolar A-805p 20 ports\nSipolar A-300 10 ports\nSipolar A-103 10 ports\nSipolar other 7 ports\n QUIC support in Socks5 Proxies\nHuawei E3372-325 'BROVI' and Linux (Ubuntu) \nRendered by Hugo | Subscribe"}, {"title": "Huawei E3372-325 'BROVI' and Linux (Ubuntu) - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux/", "html": "<PERSON>’ tech & personal blog\nTags\nHuawei E3372-325 'BROVI' and Linux (Ubuntu)\nJan 28, 2023\n#modems\n3 minute read\n\nHow to get to work Huawei E3372-325 ‘BROVI’ in Linux (ubuntu)\n\nIn Jan 2023 there is still no good integration of this new modem to Linux usb-modeswitch. It doesn’t include its VID:PID, which are 3566:2001.\n\nThe weird thing about this modem, when it is plugged in a Linux machine, it goes through these steps on its own:\n\nRNDIS+CDROM (netcard) , it stays here for 1..3 seconds, then switches on its own to\nCDROM , and stays here forever, and doesn’t react on any SCSI sequence sent by usb-modeswitch .\n\nIt only accepts a sequence when in mode RNDIS+CDROM . And when it does so , it re-attaches its ports, so its netcard appears again. In latter case we should ignore it.\n\nRNDIS+CDROM (netcard) , it stays here for 1..3 seconds,\nwe manage to send a sequence\nduring that sequence the RNDIS netcard appears again on the same USB port, but we ignore it (sic!)\nit stays in RNDIS forever\n\nSo we have to develop a script that\n\naddresses a modem by USB ID (not just by Vendor+Product ID!)\ncatches a modem with 3566:2001 in RNDIS mode\nignores other modes : CDROM, AT-PORT, NCM card\nignores already switched modem\n\nSo 2 scripts are below\n\n/usr/local/bin/brovi_switch (make it executable!)\n\n 1\n 2\n 3\n 4\n 5\n 6\n 7\n 8\n 9\n10\n11\n12\n13\n14\n15\n16\n17\n18\n19\n20\n21\n22\n23\n24\n25\n26\n27\n28\n29\n30\n31\n32\n33\n34\n35\n36\n37\n38\n39\n40\n41\n42\n43\n44\n45\n46\n47\n48\n49\n50\n51\n52\n53\n54\n55\n56\n57\n58\n59\n60\n61\n62\n63\n64\n65\n66\n67\n68\n69\n70\n71\n72\n\n\t\n#!/bin/bash\nPATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\n# vim: filetype=bash\n\n#   2023-01-28 Pavel Piatruk, piatruk.by\n\nID=$$\n\nUSB_ID=$(basename $DEVPATH)\n\n\n{\n#set|sort \n\n#ls -la /sys/$DEVPATH\n\necho bInterfaceClass on ports as follows\n\ngrep -H .  /sys$DEVPATH/1*/bInterfaceClass\n\nIC=$( grep -h .  /sys$DEVPATH/*:1.0/bInterfaceClass )\n\necho \"got bInterfaceClass on 1st port $IC\"\n    echo usb_modeswitch -b $BUSNUM -g $DEVNUM -v 3566 -p 2001\n\ncase $IC in\n08) \n    echo Storage MODE\n    ;;\ne0)\n\n    echo \"Already RNDIS\"\n    LOCKFILE=/var/run/brovi.$USB_ID.lock\n    if [[ -e $LOCKFILE ]]\n    then    \n        LOCKFILE_AGE=$(( $(date +%s ) - $(stat  $LOCKFILE -c %Y) )) \n        echo LOCKFILE_AGE=$LOCKFILE_AGE\n    fi\n\n    if [[ -n $LOCKFILE_AGE ]] && [[ $LOCKFILE_AGE -lt  10 ]]\n    then    echo was switched VERY recently, noop\n    else\n\n    set > $LOCKFILE\n\n    CMDS=(\n        \"usb_modeswitch -b $BUSNUM -g $DEVNUM -v $ID_VENDOR_ID -p $ID_MODEL_ID  -W -R  -w 400 \"\n        \"usb_modeswitch -b $BUSNUM -g $DEVNUM -v $ID_VENDOR_ID -p $ID_MODEL_ID  -W -R \"\n    )\n\n    i=0\n\n    for CMD in \"${CMDS[@]}\"\n    do\n        i=$(($i+1))\n        echo \"=====STEP$i, run: $CMD\"\n        $CMD\n    done\n    fi\n\n    ;;\nff)\n    echo Serial Port\n    ;;\n*)\n    echo Unknown mode\n    ;;\nesac\n\n} | logger -t BROVI\n\nexit 0\n\n\nand UDEV script /etc/udev/rules.d/40-huawei.rules\n\n\nACTION!=\"add\", GOTO=\"modeswitch_rules_end\"\nSUBSYSTEM!=\"usb\", GOTO=\"modeswitch_rules_end\"\n\n# All known install partitions are on interface 0\nATTRS{bInterfaceNumber}!=\"00\", GOTO=\"modeswitch_rules_end\"\n\nGOTO=\"modeswitch_rules_begin\"\n\nLABEL=\"modeswitch_rules_begin\"\n# Huawei E3372-325\nATTR{idVendor}==\"3566\", ATTR{idProduct}==\"2001\", RUN+=\"/usr/local/bin/brovi_switch %k %p\"\n\nLABEL=\"modeswitch_rules_end\"\n\n\nEDIT 2023-01-30 It seems Huawei released new firmware (below) that works fine with Linux. It switches to RNDIS -> RNDIS -> CDROM 12d1:1f01 -> usb-modeswitch switches to RNDIS.\n\n'SoftwareVersion' => '********(H057SP9C983)\n'WebUIVersion' => 'WEBUI ********(W13SP5C7702)',\n'iniversion' => 'E3372-325-CUST *******(C778)',\n'HardwareVersion' => 'CL5E3372M',\n\n\nEDIT 2023-02-28\n\nThere is an alternative guide https://www.draisberghof.de/usb_modeswitch/bb/viewtopic.php?f=3&t=3043&p=20026#p20054 , it doesn’t properly handle the case when multiple Brovi’s are plugged in – it resets them ALL – the devices are referred by Vendor_ID and Product_ID, which are the same on identical modems. It`s fine when you have 1 BROVI on the PC.\n\nWhile my solution addresses by Vendor_ID + Product_ID + Bus_ID + Deviced_ID. So the case with multiple BROVI on the PC is covered.\n\nEDIT 2023-05-29\n\nI got it working also in STICK mode CLICK\n\n USB hubs for 4g proxies farm\nUK 4G\\LTE modems \nRendered by Hugo | Subscribe"}, {"title": "UK 4G\\LTE modems - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/uk_modems/", "html": "<PERSON> tech & personal blog\nTags\nUK 4G\\LTE modems\nApr 4, 2023\nModified on Jan 25, 2024\n#modems , #proxies\nOne minute read\n\nThe following modems should be working in the UK e.g. with my 4g proxy farm software Proxysmart software.\n\nMain LTE bands in the UK:\n\nFDD B3 1800Mhz\nFDD B7 2100Mhz\nFDD B20 800Mhz\nTL;DR;\n\nMost popular and easy to get modems for the UK:\n\nHuawei E3372-320\nQuectel EC25, EP06\nZTE MF79\nSierra Wireless EM7455\nDONGLES\nAlcatel IK40\n\nhttps://www.amazon.co.uk/Alcatel-LinkKey-Mobile-Internet-Dongle-Black/dp/B073X3NWF7/\n\nHuawei E3372-320 (recommended)\n\n..\n\nHuawei E3372-325 (not recommended)\n\nhttps://www.amazon.co.uk/Huawei-E3372-Cat4-Modem-E3372H-153-White/dp/B013UURTL4/\n\nZTE-MF833U1\n\nhttps://www.amazon.co.uk/ZTE-MF833U1-Unlocked-Configuration-Warranty/dp/B08R69YGLV/\n\nMIFI\nHuawei E5783\n\nhttps://www.amazon.co.uk/Huawei-E5783B-230-Super-Fast-Hotspot-Warranty/dp/B07KF93DFH/\n\nhttps://www.amazon.co.uk/E5783-330-Soyealink-Hotspot-Worldwide-Wireless-Black/dp/B09MFSNSDN/\n\nHuawei E5577\n\nhttps://www.amazon.co.uk/Super-Fast-Portable-Hotspot-Unlocked-Network/dp/B08L85339K/\n\nhttps://www.amazon.co.uk/Huawei-E5577-320-300Mbps-Mobile-Broadband-Black/dp/B09BYWSQ1Y/\n\nHuawei E5576-320\n\nhttps://www.amazon.co.uk/E5576-320-Networks-Configuration-required-Warranty/dp/B081SCZRHL/\n\nhttps://www.amazon.co.uk/Huawei-E5576-320-Networks-Configuration-required/dp/B07Y3ZSR7D/\n\nhttps://www.amazon.co.uk/E5330-3G-Unlocked-Networks-Configuration-Required/dp/B07Z5LWMNZ/\n\nZTE MF79\n\nZTE MF920\n\nhttps://www.amazon.co.uk/ZTE-MF920U-Travel-Hotspot-SMARTY/dp/B08CZYM51X/\n\nhttps://www.amazon.co.uk/ZTE-MF920V-Hotspot-Unlocked-Networks/dp/B00YTI0ZRW/\n\nLan modems\nHuawei B311\n\nhttps://www.amazon.co.uk/Unlocked-Networks-Genuine-Warranty-Network/dp/B08235ZNPV/\n\nLTE modules\nQuectel\n\nhttps://www.amazon.co.uk/dp/B0B3C94GCM/\n\n(EC25-E EC25-EU )\n\nSierra Wireless EM7455\n Huawei E3372-325 'BROVI' and Linux (Ubuntu)\nHuawei E3372-325 'BROVI' and Linux (Ubuntu) - Stick mode. \nRendered by Hugo | Subscribe"}, {"title": "phones - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/phones/", "html": "<PERSON>’ tech & personal blog\nTags\n#phones\n2023\nBuilding proxies - phones VS dongles\nJul 10\nRendered by <PERSON> | Subscribe"}, {"title": "Huawei E3372-325 'BROVI' and Linux (Ubuntu) - Stick mode. - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/huawei_e3372h-325_brovi_with_linux_stickmode/", "html": "<PERSON>’ tech & personal blog\nTags\nHuawei E3372-325 'BROVI' and Linux (Ubuntu) - Stick mode.\nMay 29, 2023\nModified on Nov 25, 2023\n#modems\n3 minute read\n\nHow to get to work Huawei E3372-325 ‘BROVI’ in Linux (ubuntu) - Stick mode.\n\nIn Part1 I explained how to get it work in HiLink mode. For some reasons sometimes you need it in STICK mode.\n\nNotes:\n\nI could not get it to work in NCM mode (with controlling AT\\CDC port and DHCP on a network card) so PPP mode was used.\n\n/usr/local/bin/brovi_switch.X (make it executable!)\n\n#!/bin/bash\nPATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin\n# vim: filetype=bash\n\n#   2023-01-28 Pavel <PERSON>, piatruk.by\n# switches BROVI to STICK mode\n\nID=$$\necho $ID\nUSB_ID=$(basename $DEVPATH)\n\n{\necho bInterfaceClass on ports as follows \ngrep -H .  /sys$DEVPATH/1*/bInterfaceClass \nIC=$( grep -h .  /sys$DEVPATH/*:1.0/bInterfaceClass )\necho \"got bInterfaceClass on 1st port $IC\"\necho usb_modeswitch -b $BUSNUM -g $DEVNUM -v 3566 -p 2001\n\ncase $IC in\n08) \n    echo Storage MODE\n    usb_modeswitch -b $BUSNUM -g $DEVNUM -v $ID_VENDOR_ID -p $ID_MODEL_ID  -X\n    ;;\ne0)\n    echo \"Already RNDIS\"\n    ;;\nff)\n    echo Serial Port\n    ;;\n*)\n    echo Unknown mode\n    ;;\nesac\n\n} | logger -t BROVI\n\nexit 0\n\n\nand UDEV script /etc/udev/rules.d/40-huawei.rules\n\nACTION!=\"add\", GOTO=\"modeswitch_rules_end\"\nSUBSYSTEM!=\"usb\", GOTO=\"modeswitch_rules_end\"\n#\n## All known install partitions are on interface 0\nATTRS{bInterfaceNumber}!=\"00\", GOTO=\"modeswitch_rules_end\"\n#\nGOTO=\"modeswitch_rules_begin\"\n\nLABEL=\"modeswitch_rules_begin\"\nATTR{idVendor}==\"3566\", ATTR{idProduct}==\"2001\", RUN+=\"/usr/local/bin/brovi_switch.X  %k %p\"\nLABEL=\"modeswitch_rules_end\"\n\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"3566\", ATTRS{idProduct}==\"2001\", NAME=\"wwan_brovi\"\n# ignore AT ports on interfaces 2,4 for ModemManager cuz they stuck for some reason\nSUBSYSTEM==\"tty\", ACTION==\"add\", DEVPATH==\"*:1.[24]/*\", ATTRS{idVendor}==\"3566\", ATTRS{idProduct}==\"2001\", ENV{ID_MM_PORT_IGNORE}=\"1\" \n\n\nAfter editing these 2 , replug the modem. Or when the modem is in, and is in CDROM mode, just run\n\nusb_modeswitch  -v 3566 -p 2001  -X\n\n\nSo it will open its ports & 3 new tty devices will appear. If they don’t - tell the OS to bind this device\n\nmodprobe option\necho 3566 2001 ff > /sys/bus/usb-serial/drivers/option1/new_id\n\n\nAt this point AT ports are present. Check kernel logs.\n\nNow you have 2 options to get online.\n\nOption1. Use NetworkManager + ModemManager\n\nCheck output of mmcli -L , it shows smth like /org/freedesktop/ModemManager1/Modem/5 [ZOWEE TECHNOLOGY (HEYUAN) CO., LTD.] E3372-325.\n\nCheck verbose output from MM(ModemManager), it shows signal levels etc: mmcli -m 5\n\nAnd nmcli dev shows ttyUSB1.\n\nCreate NM(NetworkManager) connection:\n\nnmcli con add con-name BROVI_PPP ipv4.route-metric 3000 connection.type gsm ifname ttyUSB1\nnmcli con modify BROVI_PPP ipv6.method disabled\nnmcli con modify BROVI_PPP gsm.apn internet\n\n\nit is started automatically when the modem appears.\n\nOption2. use Wvdial.\n\ncreate a file /etc/wvdial.conf\n\n[Dialer Defaults]\nInit1 = AT\nInit3 = AT+CGDCONT=1,\"IP\",\"internet\"\nInit7 = AT+CGATT?\nInit8 = AT+CGACT?\n\nPhone = *99#\nUsername = { }\nPassword = { }\nModem Type = Analog Modem\nISDN = 0\nNew PPPD = yes\nStupid Mode = yes\nModem = /dev/ttyUSB1\nAuto DNS = no\nBaud = 9600\n\n\nand run wvdial\n\n UK 4G\\LTE modems\nBuilding proxies - phones VS dongles \nRendered by Hugo | Subscribe"}, {"title": "Building proxies - phones VS dongles - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/phones_vs_modems/", "html": "<PERSON>’ tech & personal blog\nTags\nBuilding proxies - phones VS dongles\nJul 10, 2023\n#proxies , #phones , #modems\n2 minute read\n\nLet’t compare Pros and Cons of phones and dongles (modems) when building 4G/5G proxies on them.\n\nDongles (modems)\nDongles (modems) PRO’s:\neasy to set up (insert a SIM card and go)\nanonymity (no Google accounts)\ncheap hardware\nless cables\nDongles (modems) CON’s:\nno 5G, unless you buy expensive modems.\nbig tech companies stopped developing new dongles around 2018, so present dongles are often cheap 4G LTE pieces with LTE cat.4\nlower LTE category (in general) so worse perception / slower speed, they need antennas or a GSM booster\na PC is needed to manage the 4g proxy farm\na good USB hub is needed to connect the modems with the PC (unless you use 4g/5g LAN routers..)\na trick with IMEI is needed when the Carrier sells a DATA plan for Phones/Tablets.\ndifficult to find for some areas (e.g. the USA) for some Carriers.\nPhones\nPhones PRO’s\nhigh speed out of the box because of 5G or higher LTE category (LTE cat. 12..20)\nno need to use a PC for management; phones can connect to a VPS on their own\nno need to buy a good USB hub; wall chargers are enough\nnative OS TCP fingerprint\neasier to find and buy than modems (critical in some areas)\nfit in with Carrier Data plans (for Phones/Tablets)\n4g proxy farm survives short electricyty outages\nPhones CON’s\nmore expensive than a modem\nhastle with initial set up (fake Google accounts for 100 phones is a nightmare)\nrooting might be needed to unlock some tasty features\nchanging OS TCP fingerprint can be tricky.\nbattery health issues\n Huawei E3372-325 'BROVI' and Linux (Ubuntu) - Stick mode.\nOpenvpn Connect - fix support of old profiles \nRendered by Hugo | Subscribe"}, {"title": "openvpn - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/openvpn/", "html": "<PERSON>’ tech & personal blog\nTags\n#openvpn\n2023\nOpenvpn Connect - fix support of old profiles\nOct 22\nRendered by <PERSON> | Subscribe"}, {"title": "Openvpn Connect - fix support of old profiles - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/openvpn_connect_fix/", "html": "<PERSON>’ tech & personal blog\nTags\nOpenvpn Connect - fix support of old profiles\nOct 22, 2023\n#openvpn\nOne minute read\n\nAround July 2023 , Openvpn Connect 3.4 was released which has broken support of old OpenVPN profiles. It gone global.\n\nMore info: DuckDuckGo\n\nHow the issue appears\n\nOpenvpn Connect won’t connect and throws an error: unknown/unsupported option\n\nSolutions:\nSolution 1.\n\nEdit the profile (.ovpn file) & delete 2 lines:\n\nroute-delay\npull\n\n\nSave the file.\n\nAdd the profile and connect\n\nSolition 2\n\nUse older Openvpn Connect (pre 3.4 ).\n\nSolution 3\n\nUse Openvpn Community client\n\n Building proxies - phones VS dongles\nUSB modems don't show up \nRendered by <PERSON> | Subscribe"}, {"title": "usb - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/usb/", "html": "<PERSON>’ tech & personal blog\nTags\n#usb\n2023\nUSB modems don’t show up\nNov 1\nRendered by <PERSON> | Subscribe"}, {"title": "modems - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/modems/", "html": "<PERSON>’ tech & personal blog\nTags\n#modems\n2023\nUSB modems don’t show up\nNov 1\nBuilding proxies - phones VS dongles\nJul 10\nHuawei E3372-325 ‘BROVI’ and Linux (Ubuntu) - Stick mode.\nMay 29\nUK 4G\\LTE modems\nApr 4\nHuawei E3372-325 ‘BROVI’ and Linux (Ubuntu)\nJan 28\n2022\nQuectel EC200T on OpenWRT\nDec 16\nUSA 4G\\LTE modems\nMay 21\nAdded new modems models to Proxysmart\nApr 25\nProxysmart: my alternative to Proxidize\nMar 10\nRendered by <PERSON> | Subscribe"}, {"title": "proxies - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/proxies/", "html": "<PERSON>’ tech & personal blog\nTags\n#proxies\n2023\nAcom 716\nNov 20\nUSB modems don’t show up\nNov 1\nBuilding proxies - phones VS dongles\nJul 10\nUK 4G\\LTE modems\nApr 4\nUSB hubs for 4g proxies farm\nJan 22\n2022\nQUIC support in Socks5 Proxies\nDec 19\nHow to build proxies from 4G/5G LAN routers\nSep 2\nHow to build VPN from PROXY\nAug 12\nIdeal Proxy Software\nJun 11\nUSA 4G\\LTE modems\nMay 21\nAdded new modems models to Proxysmart\nApr 25\nProxysmart: my alternative to Proxidize\nMar 10\nRendered by <PERSON> | Subscribe"}, {"title": "USB modems don't show up - <PERSON> tech & personal blog", "url": "https://blog.tanatos.org/posts/usb2__not-enough-usb/", "html": "<PERSON> tech & personal blog\nTags\nUSB modems don't show up\nNov 1, 2023\nModified on Jan 22, 2024\n#proxies , #linux , #kernel , #modems , #usb\n2 minute read\n\nHow to solve Not enough host resources in Linux system log, when USB modems don’t show up though they are visible in lsusb and only 3..10 of them are actually working.\n\nCheck with the command :\n\nsudo journalctl -b -k  | grep -E 'Not enough host .* resources|set config' | tail \n\n\nIf you see this output :\n\nOct 31 21:39:38 px kernel: usb 3-4.1.3: can't set config #1, error -12\nOct 31 21:39:45 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 61\nOct 31 21:39:45 px kernel: usb 3-4.6.2: can't set config #1, error -12\nOct 31 21:39:48 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:39:48 px kernel: usb 3-4.1.3: can't set config #1, error -12\nOct 31 21:39:59 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 58\nOct 31 21:39:59 px kernel: usb 3-4.1.3: can't set config #1, error -12\nOct 31 21:40:10 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:40:10 px kernel: usb 3-4.6.2: can't set config #1, error -12\nOct 31 21:40:21 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:40:21 px kernel: usb 3-4.6.2: can't set config #1, error -12\nOct 31 21:40:32 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:40:32 px kernel: usb 3-4.6.2: can't set config #1, error -12\nOct 31 21:40:42 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:40:42 px kernel: usb 3-4.6.2: can't set config #1, error -12\nOct 31 21:40:53 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:40:53 px kernel: usb 3-4.6.2: can't set config #1, error -12\nOct 31 21:41:04 px kernel: xhci_hcd 0000:00:14.0: Not enough host resources, active endpoint contexts = 59\nOct 31 21:41:04 px kernel: usb 3-4.6.2: can't set config #1, error -12\n\n\n.. then solutions are as follows.\n\nSimple solutions\n\nTry one of, or all of them together:\n\nDisable USB 3 in BIOS completely.\nuse USB 2.0 cables\nuse USB 2.0 hub\nuse USB 2.0 slot on the PC side\nSolution with custom kernel\n\nBuild a Linux kernel without USB 3 kernel driver. Disable CONFIG_USB_XHCI_HCD while building.\n\nThen disable UEFI (Secure boot) in BIOS, because custom kernels are not signed with enrolled Ubuntu keys (or enroll yours..).\n\nOr install a kernel already built by me:\n\nhttps://pathos.tanatos.org/repo/kernel.usb2.0/\n\nDownload for your Ubuntu version.\n\nThen install it\n\n Openvpn Connect - fix support of old profiles\nAcom 716 \nRendered by Hugo | Subscribe"}, {"title": "grub - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/grub/", "html": "<PERSON>’ tech & personal blog\nTags\n#grub\n2024\nSafely try new Linux kernel in Grub\nJan 22\nRendered by <PERSON> | Subscribe"}, {"title": "Acom 716 - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/acom716/", "html": "<PERSON>’ tech & personal blog\nTags\nAcom 716\nNov 20, 2023\nModified on Dec 8, 2023\n#proxies\nOne minute read\n\nBriew review of Acom716 multi-SIM router & proxies server\n\nWhere to buy:\nOfficial site?\nAliExpress\nmade-in-china.com\nglobalsources.com\n\nAlso knows as “MTR716”.\n\nPrice: about $1300.\n\nIdea of the device:\nIt allows building 16 proxies (HTTP and SOCKS5) from each of embedded 4G modems. Each modem has to have a SIM card.\nProxies are available by LAN IP of the router.\nIt allows IP rotation of the proxies by operating the underlying modems ( Quectel EC25 ).\nAll actions can be done by clicking buttons in the Web App or by HTTP API.\nDownload\nUser Manual\nHTTP API\nIs it worth buying?\n\nPRO’s:\n\nvery easy to start 4g proxies business with\ngood for beginners at 4g proxies building\n\nCON’s:\n\nExpensive.\nYou are bound to its API which is not easy to operate.\nModems can’t be upgraded \\ replaced \\ expanded.\nyou can’t construct quite complex ACL on the proxies or configure them in a sophisticated way.\nif you are a Pro in building proxies, it is not for you.\n USB modems don't show up\nSafely try new Linux kernel in Grub \nRendered by <PERSON> | Subscribe"}, {"title": "kernel - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/kernel/", "html": "<PERSON>’ tech & personal blog\nTags\n#kernel\n2024\nSafely try new Linux kernel in Grub\nJan 22\n2023\nUSB modems don’t show up\nNov 1\nRendered by <PERSON> | Subscribe"}, {"title": "linux - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/linux/", "html": "<PERSON>’ tech & personal blog\nTags\n#linux\n2024\nSafely try new Linux kernel in Grub\nJan 22\n2023\nUSB modems don’t show up\nNov 1\n2022\nHow to Change OS TCP/IP Fingerprint\nOct 3\n2020\nFighting against Passive OS Fingerprinting\nNov 3\nRendered by <PERSON> | Subscribe"}, {"title": "- <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/tags/", "html": "<PERSON>’ tech & personal blog\nTags\nTags\n4g\n(1 entries)\nansible\n(1 entries)\ngrub\n(1 entries)\nkernel\n(2 entries)\nlinux\n(4 entries)\nmodems\n(9 entries)\nnetplan\n(1 entries)\nnetworking\n(2 entries)\nopenvpn\n(1 entries)\nphones\n(1 entries)\nproxidize\n(1 entries)\nproxies\n(12 entries)\nproxy\n(2 entries)\npsiphon\n(2 entries)\npuppet\n(1 entries)\nubuntu\n(3 entries)\nusb\n(1 entries)\nvpn\n(1 entries)\nRendered by <PERSON> | Subscribe"}, {"title": "Safely try new Linux kernel in Grub - <PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/posts/grub_try_new_kernel/", "html": "<PERSON> tech & personal blog\nTags\nSafely try new Linux kernel in Grub\nJan 22, 2024\n#linux , #kernel , #grub\nOne minute read\n\nHow to safely try new Linux kernel remotely without loosing access to the server.\n\nInstall new kernel.\n\nList kernels in Grub compatible format.\n\nMENUID=$( grep -E \"^\\\\s*(submenu) \" /boot/grub/grub.cfg | grep -oP \"gnuli.*(?=')\" )\ngrep '(?<=menuentry_id_option ).gnuli\\S+' /boot/grub/grub.cfg -Po | \\\n    sed \"s@'@@g\" |grep -vE 'recovery|simple|gnulinux-advanced' | \\\n    sed \"s@^@GRUB_DEFAULT=\\\"${MENUID}>@; s@\\$@\\\"@\"\n\n\nSample output:\n\nGRUB_DEFAULT=\"gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee>gnulinux-6.2.16-usb2-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee\"\nGRUB_DEFAULT=\"gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee>gnulinux-6.2.0-36-generic-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee\"\nGRUB_DEFAULT=\"gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee>gnulinux-6.2.0-26-generic-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee\"\n\n\nSo assume we want to try the new kernel 6.2.16-usb2 while old stable kernel is 6.2.0-36-generic.\n\nNew Kernel ID: [line1]\nOld Kernel ID: [line2]\n\nEdit /etc/default/grub and set GRUB_DEFAULT to the OLD kernel ID.\n\nGRUB_DEFAULT=\"gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee>gnulinux-6.2.0-36-generic-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee\"\n\n\nRun update-grub.\n\nMark New kernel to be booted up during the next reboot:\n\ngrub-reboot \"gnulinux-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee>gnulinux-6.2.16-usb2-advanced-c6cd75ad-e041-45b4-ad2c-6fa10b5293ee\"\n\n\nReboot the server:\n\nsync\nreboot -f\n\n\nWait ~1 minute (depends..), then:\n\nIf the server was booted OK:\n\nset GRUB_DEFAULT=0 (default kernel of higher version)\n\nrun update-grub and sync and reboot\n\nmake sure new kernel is booted up again (because of its higher version)\n\nIf the server was not booted up:\n\nreboot, old kernel will boot (because of GRUB_DEFAULT)\n\nremove new kernel with apt remove or dpkg -r\n\nset GRUB_DEFAULT=0 and run update-grub\n\n Acom 716\nRendered by Hugo | Subscribe"}, {"title": "<PERSON>' tech & personal blog", "url": "https://blog.tanatos.org/", "html": "<PERSON>’ tech & personal blog\nTags\nSafely try new Linux kernel in Grub\nJan 22, 2024\n#linux , #kernel , #grub\nOne minute read\n\nHow to safely try new Linux kernel remotely without loosing access to the server.\n\nRead more…\nAcom 716\nNov 20, 2023\nModified on Dec 8, 2023\n#proxies\nOne minute read\n\nBriew review of Acom716 multi-SIM router & proxies server\n\nRead more…\nUSB modems don't show up\nNov 1, 2023\nModified on Jan 22, 2024\n#proxies , #linux , #kernel , #modems , #usb\n2 minute read\n\nHow to solve Not enough host resources in Linux system log, when USB modems don’t show up though they are visible in lsusb and only 3..10 of them are actually working.\n\nRead more…\nOpenvpn Connect - fix support of old profiles\nOct 22, 2023\n#openvpn\nOne minute read\n\nAround July 2023 , Openvpn Connect 3.4 was released which has broken support of old OpenVPN profiles. It gone global.\n\nRead more…\nBuilding proxies - phones VS dongles\nJul 10, 2023\n#proxies , #phones , #modems\n2 minute read\n\nLet’t compare Pros and Cons of phones and dongles (modems) when building 4G/5G proxies on them.\n\nRead more…\nHuawei E3372-325 'BROVI' and Linux (Ubuntu) - Stick mode.\nMay 29, 2023\nModified on Nov 25, 2023\n#modems\n3 minute read\n\nHow to get to work Huawei E3372-325 ‘BROVI’ in Linux (ubuntu) - Stick mode.\n\nRead more…\nUK 4G\\LTE modems\nApr 4, 2023\nModified on Jan 25, 2024\n#modems , #proxies\nOne minute read\n\nThe following modems should be working in the UK e.g. with my 4g proxy farm software Proxysmart software.\n\nRead more…\nHuawei E3372-325 'BROVI' and Linux (Ubuntu)\nJan 28, 2023\n#modems\n3 minute read\n\nHow to get to work Huawei E3372-325 ‘BROVI’ in Linux (ubuntu)\n\nRead more…\nUSB hubs for 4g proxies farm\nJan 22, 2023\n#proxies\nOne minute read\n\nHow to buy best USB hub for a building a 4g proxy farm.\n\nRead more…\nQUIC support in Socks5 Proxies\nDec 19, 2022\n#proxies\n2 minute read\n\nIn 2023 you will loose the battle when your proxies don’t support QUIC (HTTP/3.0). With 4g proxy farm software Proxysmart, you will win. I recently added QUIC support to my proxies building software.\n\nRead more…\nQuectel EC200T on OpenWRT\nDec 16, 2022\n#modems\n3 minute read\n\nThe issue with EC200T that it doesn’t have QMI\\MBIM. It has only ECM and AT-port , so we have to start WAN connection on AT-port and use it via the WAN port.\n\nRead more…\nHow to Change OS TCP/IP Fingerprint\nOct 3, 2022\n#linux , #networking\n4 minute read\n\nSome useful info & tips on OS TCP/IP Fingerprint.\n\nRead more…\nHow to build proxies from 4G/5G LAN routers\nSep 2, 2022\n#proxies , #4g\nOne minute read\n\nRecently I added support of 4G or 5G LAN routers like Huawei Bxxx, to my 4G proxy farm software Proxysmart software:\n\nRead more…\nHow to build VPN from PROXY\nAug 12, 2022\n#proxies , #vpn\nOne minute read\n\nRecently I built a solution for making VPN from Proxies. Any proxies are supported (HTTP, SOCKS5) and a resulting Openvpn user traffic is routed via the mapped proxy.\n\nRead more…\nIdeal Proxy Software\nJun 11, 2022\nModified on Feb 7, 2024\n#proxies\nOne minute read\n\nA little comparison of open source proxy software: Haproxy, 3proxy, Squid, Nginx, Gost (2), V2ray\n\nRead more…\nUSA 4G\\LTE modems\nMay 21, 2022\nModified on Jan 25, 2024\n#modems , #proxies\n2 minute read\n\nThe following modems should be working in the USA e.g. with my 4g proxy farm software Proxysmart software.\n\nRead more…\nAdded new modems models to Proxysmart\nApr 25, 2022\n#modems , #proxies\nOne minute read\n\nRecently I have added new modems models to my 4g proxy farm software Proxysmart, in particular Quectel family and ZTE MF79 and ZTE MF971. For each, it is possible to read SMS, and reset IP.\n\nRead more…\nProxysmart: my alternative to Proxidize\nMar 10, 2022\n#proxidize , #modems , #proxies\nOne minute read\n\nProxidize significally raised prices on their service and I have a drop-in replacement.\n\nRead more…\nFighting against Passive OS Fingerprinting\nNov 3, 2020\n#linux , #networking\nOne minute read\n\nThe article describes steps to install OSfooler-ng to prevent your host OS be recognized by Passive OS Fingerpringting (p0f).\n\nRead more…\nPsiphon: setting up Linux client (with free servers)\nSep 5, 2020\n#ubuntu , #psiphon , #proxy\n2 minute read\n\nPsiphon is obfuscated proxy, and its client & server are open-source.\n\nThe plan is:\n\nsetting up Linux Psiphon Client that connects to free Psiphon network.\n\nRead more…\nPsiphon: setting up own server & Linux client\nSep 5, 2020\n#ubuntu , #psiphon , #proxy\n2 minute read\n\nPsiphon is obfuscated proxy, and its client & server are open-source.\n\nThe plan is:\n\nsetting up own Psiphon server\nsetting up Psiphon Client on Linux that connects to your own Psiphon server\n\nRead more…\nUbuntu: migrate from Ifupdown to Netplan. Step by step.\nFeb 7, 2019\n#ubuntu , #netplan\nOne minute read\n\nIn Ubuntu 18.04 old good ifupdown is considered obsolete. And you need to migrate your servers to systemd-networkd + netplan. Lots of internet resources describe how to revert back to ifupdown but I am hailing the future! So this manual covers 2 situations, static networking & DHCP. Both are IPv4.\n\nRead more…\nWhy Ansible? Ansible vs Puppet\nJan 23, 2019\n#ansible , #puppet\n2 minute read\n\nRead more…\nRendered by Hugo | Subscribe"}]