# SMSHub Application - Development Plan

## 1. Introduction

This document outlines the phased development plan for the SMSHub Application. The application will manage SMS communications via multiple cellular modems, integrate with the SMSHub API, and provide a dashboard for monitoring and control.

## 2. Project Structure (Initial Proposal)

```
smshub_app/
├── app/
│   ├── __init__.py
│   ├── main.py             # Flask app, routes, core logic
│   ├── modem_manager.py    # Handles modem detection, communication, and management
│   ├── smshub_api.py       # Client for interacting with SMSHub API
│   ├── config_manager.py   # Loads and manages application configuration
│   ├── models.py           # Data models (if needed, e.g., for database interaction later)
│   └── utils.py            # Utility functions
├── templates/
│   └── dashboard.html      # Main dashboard UI
├── static/
│   ├── css/
│   │   └── style.css       # Styles for the dashboard
│   └── js/
│       └── dashboard.js    # Client-side JavaScript for dashboard interactivity
├── config.json             # Application configuration (modem details, API keys, approved lists)
├── venv/                   # Virtual environment
└── README.md
```

## 3. Development Phases

### Phase 1: Core Application Setup & Configuration

*   **Goal:** Establish the basic Flask application structure and implement configuration management.
*   **Tasks:**
    1.  **Initialize Project:**
        *   Deliverable: Create project directory structure, initialize Git repository, set up Python virtual environment.
        *   Files: `smshub_app/`, `README.md`
    2.  **Basic Flask Application:**
        *   Deliverable: Create a minimal Flask application ([`app/main.py`](app/main.py)) with a simple root route.
        *   Files: [`app/__init__.py`](app/__init__.py), [`app/main.py`](app/main.py)
    3.  **Configuration Management:**
        *   Deliverable: Implement a module ([`app/config_manager.py`](app/config_manager.py)) to load settings from [`config.json`](config.json) (e.g., SMSHub API key, modem port patterns, approved numbers).
        *   Files: [`app/config_manager.py`](app/config_manager.py), [`config.json`](config.json) (initial structure)
        *   Example [`config.json`](config.json) structure:
            ```json
            {
              "smshub_api_key": "YOUR_SMSHUB_API_KEY",
              "modem_ports": [
                {"type": "fibocom", "port_pattern": "/dev/ttyUSB*", "at_command_port_index": 2, "data_port_index": 3},
                {"type": "novatel", "port_pattern": "/dev/ttyACM*", "at_command_port_index": 0, "data_port_index": 1}
              ],
              "approved_senders": ["+11234567890"],
              "approved_services_for_forwarding": ["service_A", "service_B"],
              "dashboard_refresh_interval_ms": 5000
            }
            ```
    4.  **Logging Setup:**
        *   Deliverable: Configure basic logging for the application.
        *   Files: Modified [`app/main.py`](app/main.py)

### Phase 2: Modem Management - Fibocom L850-GL (Initial Focus)

*   **Goal:** Implement core modem detection and communication logic, focusing first on the Fibocom L850-GL modem.
*   **Tasks:**
    1.  **Modem Detection Logic:**
        *   Deliverable: Implement functionality in [`app/modem_manager.py`](app/modem_manager.py) to detect connected modems based on patterns in [`config.json`](config.json).
        *   Files: [`app/modem_manager.py`](app/modem_manager.py)
    2.  **AT Command Interface (Fibocom):**
        *   Deliverable: Create a class/functions to send AT commands to a Fibocom modem and parse responses. Refer to [`smshub app docs/docs/Fibocom L850gl SMS Integration_.md`](smshub%20app%20docs/docs/Fibocom%20L850gl%20SMS%20Integration_.md).
        *   Key AT Commands: `AT` (check connection), `AT+CPIN?` (SIM status), `AT+CSQ` (signal quality), `AT+COPS?` (network operator), `AT+CNUM` (own number, if supported), `AT+CMGF=1` (set SMS to text mode).
        *   Files: [`app/modem_manager.py`](app/modem_manager.py) (or a dedicated `app/modems/fibocom.py`)
    3.  **Basic Modem Information Retrieval:**
        *   Deliverable: Functionality to retrieve basic information (Signal Strength, Network Operator, SIM status) from a connected Fibocom modem.
        *   Files: [`app/modem_manager.py`](app/modem_manager.py)

### Phase 3: Initial Dashboard UI

*   **Goal:** Create a basic web dashboard to display information about connected modems.
*   **Tasks:**
    1.  **Dashboard HTML Structure:**
        *   Deliverable: Create [`templates/dashboard.html`](templates/dashboard.html) based on `dashboard example.png` (structure for displaying modem cards, status).
        *   Files: [`templates/dashboard.html`](templates/dashboard.html)
    2.  **Dashboard Route & Data:**
        *   Deliverable: Create a Flask route in [`app/main.py`](app/main.py) to render the dashboard, passing modem information retrieved from [`app/modem_manager.py`](app/modem_manager.py).
        *   Files: [`app/main.py`](app/main.py)
    3.  **Basic Styling:**
        *   Deliverable: Basic CSS in [`static/css/style.css`](static/css/style.css) for the dashboard layout.
        *   Files: [`static/css/style.css`](static/css/style.css)
    4.  **Dynamic Updates (Placeholder/AJAX setup):**
        *   Deliverable: Setup basic JavaScript in [`static/js/dashboard.js`](static/js/dashboard.js) to periodically refresh modem data (initial setup for AJAX calls).
        *   Files: [`static/js/dashboard.js`](static/js/dashboard.js), API endpoint in [`app/main.py`](app/main.py) to serve modem data as JSON.

### Phase 4: SMSHub API Integration

*   **Goal:** Implement client-side logic to interact with the core SMSHub API endpoints.
*   **Tasks:**
    1.  **SMSHub API Client:**
        *   Deliverable: Create [`app/smshub_api.py`](app/smshub_api.py) with functions to make requests to the SMSHub API (using API key from [`config.json`](config.json)). Refer to [`smshub app docs/docs/SMSHUB-DOCS.md`](smshub%20app%20docs/docs/SMSHUB-DOCS.md).
        *   Files: [`app/smshub_api.py`](app/smshub_api.py)
    2.  **Implement `GET_SERVICES`:**
        *   Deliverable: Function in [`app/smshub_api.py`](app/smshub_api.py) and an internal mechanism/test script to call `GET_SERVICES`.
    3.  **Implement `GET_NUMBER`:**
        *   Deliverable: Function in [`app/smshub_api.py`](app/smshub_api.py) and an internal mechanism/test script to call `GET_NUMBER` for a specific service.
    4.  **Implement `FINISH_ACTIVATION`:**
        *   Deliverable: Function in [`app/smshub_api.py`](app/smshub_api.py) to report SMS successfully received and finish activation.
    5.  **Implement `PUSH_SMS`:**
        *   Deliverable: Function in [`app/smshub_api.py`](app/smshub_api.py) to forward an incoming SMS to SMSHub if it matches criteria (to be detailed later).

### Phase 5: Basic SMS Functionality (Fibocom)

*   **Goal:** Enable sending and receiving SMS messages via the Fibocom modem.
*   **Tasks:**
    1.  **Send SMS (Fibocom):**
        *   Deliverable: Function in [`app/modem_manager.py`](app/modem_manager.py) to send an SMS message using AT commands (`AT+CMGS`).
        *   Files: [`app/modem_manager.py`](app/modem_manager.py)
    2.  **Receive SMS (Fibocom - Polling/Notification):**
        *   Deliverable: Implement logic to check for new SMS messages (`AT+CMGL="REC UNREAD"`) or handle URCs (Unsolicited Result Codes, e.g., `+CMTI`) for new SMS. Store received SMS.
        *   Files: [`app/modem_manager.py`](app/modem_manager.py)
    3.  **Dashboard SMS Display:**
        *   Deliverable: Add sections to the dashboard to display recently received SMS messages per modem and a simple form to send an SMS.
        *   Files: [`templates/dashboard.html`](templates/dashboard.html), [`static/js/dashboard.js`](static/js/dashboard.js), [`app/main.py`](app/main.py) (new routes/endpoints).

### Phase 6: Multi-Modem Support & Abstraction

*   **Goal:** Abstract modem interactions to support multiple modem types and instances.
*   **Tasks:**
    1.  **Modem Interface/Base Class:**
        *   Deliverable: Define a base class or interface in [`app/modem_manager.py`](app/modem_manager.py) (or `app/modems/base.py`) that outlines common modem operations (connect, get_info, send_sms, receive_sms).
        *   Files: [`app/modem_manager.py`](app/modem_manager.py) (or `app/modems/base.py`)
    2.  **Refactor Fibocom to Use Interface:**
        *   Deliverable: Adapt the Fibocom modem logic to implement the defined interface.
        *   Files: [`app/modem_manager.py`](app/modem_manager.py) (or `app/modems/fibocom.py`)
    3.  **Novatel USB551L Integration (Example Second Modem):**
        *   Deliverable: Implement a class for the Novatel USB551L (or another chosen modem) adhering to the modem interface. Refer to [`smshub app docs/docs/Novatel USB551L SMS Integration_.md`](smshub%20app%20docs/docs/Novatel%20USB551L%20SMS%20Integration_.md).
        *   Files: `app/modems/novatel.py` (new file)
    4.  **Dynamic Modem Loading:**
        *   Deliverable: [`app/modem_manager.py`](app/modem_manager.py) to instantiate and manage multiple modem objects based on [`config.json`](config.json).
        *   Files: [`app/modem_manager.py`](app/modem_manager.py)
    5.  **Dashboard Update for Multiple Modems:**
        *   Deliverable: Dashboard dynamically displays all connected and configured modems.
        *   Files: [`templates/dashboard.html`](templates/dashboard.html), [`static/js/dashboard.js`](static/js/dashboard.js), [`app/main.py`](app/main.py).

### Phase 7: SMS Forwarding, Filtering, and Agent Logic

*   **Goal:** Implement logic for filtering SMS messages and forwarding them to SMSHub or an "agent" based on approved lists and service matching.
*   **Tasks:**
    1.  **Approved Lists Management:**
        *   Deliverable: Load "approved_senders" and "approved_services_for_forwarding" from [`config.json`](config.json).
        *   Files: [`app/config_manager.py`](app/config_manager.py), used by [`app/main.py`](app/main.py) or a new `app/sms_handler.py`.
    2.  **SMS Processing Logic:**
        *   Deliverable: When an SMS is received:
            *   Check if sender is on `approved_senders`.
            *   If SMS content matches a known service activation pattern (related to `GET_NUMBER` and `approved_services_for_forwarding`), use `FINISH_ACTIVATION` via [`app/smshub_api.py`](app/smshub_api.py).
            *   If SMS is from an approved sender and for an approved service, and needs to be pushed to SMSHub (e.g., verification codes for a specific service obtained via `GET_NUMBER`), use `PUSH_SMS`.
            *   Otherwise, hold the SMS or route to a local "agent" (details TBD based on agent's role).
        *   Files: `app/sms_handler.py` (new or integrated into [`app/main.py`](app/main.py)/[`app/modem_manager.py`](app/modem_manager.py))
    3.  **Agent Interaction Placeholder:**
        *   Deliverable: Define how the "agent" will receive SMS (e.g., API endpoint, message queue - for now, assume a simple internal log or display on dashboard).
        *   Files: [`app/main.py`](app/main.py) (if API endpoint)

### Phase 8: API Endpoints for External Agent/Control

*   **Goal:** Expose necessary functionalities via API endpoints for external control or an agent.
*   **Tasks:**
    1.  **Define Agent API Requirements:**
        *   Deliverable: Document what information/control the agent needs (e.g., list available numbers/modems, send SMS via specific modem, get received SMS).
    2.  **Implement Agent API Endpoints:**
        *   Deliverable: Create Flask API endpoints for the agent.
            *   Example: `/api/modems` (GET - list modems and status)
            *   Example: `/api/modems/<modem_id>/sms` (POST - send SMS)
            *   Example: `/api/modems/<modem_id>/received_sms` (GET - list received SMS)
        *   Files: [`app/main.py`](app/main.py)
    3.  **API Authentication/Security (Basic):**
        *   Deliverable: Implement a basic API key or token authentication for agent endpoints.
        *   Files: [`app/main.py`](app/main.py)

### Phase 9: Refinement, Testing, and Documentation

*   **Goal:** Improve robustness, user experience, and document the application.
*   **Tasks:**
    1.  **Error Handling & Resilience:**
        *   Deliverable: Comprehensive error handling for modem communication, API calls, and application logic. Retry mechanisms where appropriate.
        *   Files: Across all relevant `.py` files.
    2.  **Dashboard UI/UX Enhancements:**
        *   Deliverable: Improve dashboard usability, add more detailed modem information, better status indicators, and user feedback.
        *   Files: [`templates/dashboard.html`](templates/dashboard.html), [`static/css/style.css`](static/css/style.css), [`static/js/dashboard.js`](static/js/dashboard.js).
    3.  **Comprehensive Testing:**
        *   Deliverable: Unit tests for key modules (modem manager, API client, config manager). End-to-end testing with actual modems.
        *   Files: New `tests/` directory with test files.
    4.  **Code Documentation & Cleanup:**
        *   Deliverable: Add inline comments, docstrings, and update `README.md` with setup and usage instructions.
        *   Files: All `.py` files, `README.md`.
    5.  **Configuration for Release:**
        *   Deliverable: Instructions or scripts for deploying/running the application in a production-like environment (e.g., using Gunicorn).

## 4. Technologies

*   **Backend:** Python, Flask
*   **Frontend (Dashboard):** HTML, CSS, JavaScript
*   **Modem Communication:** `pyserial` (or similar) for AT command interaction.
*   **API Interaction:** `requests` library for HTTP calls to SMSHub.

## 5. Future Considerations (Post-MVP)

*   Database integration for persistent SMS storage and logging.
*   Advanced agent features and UI.
*   Support for more modem types.
*   WebSockets for real-time dashboard updates.
*   User authentication for the dashboard.
*   Asynchronous task handling for modem operations and API calls (e.g., Celery, asyncio).