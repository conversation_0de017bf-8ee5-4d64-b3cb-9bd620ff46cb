"""
Monitoring and metrics system for SMS Hub application.
"""
import logging
import time
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps

try:
    from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server, CollectorRegistry, REGISTRY
    from prometheus_client.core import <PERSON><PERSON><PERSON><PERSON><PERSON> as DEFAULT_REGISTRY
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # Fallback classes
    class Counter:
        def __init__(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
    
    class Histogram:
        def __init__(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def time(self): return self
        def labels(self, *args, **kwargs): return self
        def __enter__(self): return self
        def __exit__(self, *args): pass
    
    class Gauge:
        def __init__(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def dec(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self
    
    class Info:
        def __init__(self, *args, **kwargs): pass
        def info(self, *args, **kwargs): pass

try:
    from circuitbreaker import circuit, CircuitBreakerError
except ImportError:
    def circuit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    class CircuitBreakerError(Exception):
        pass

from .config import config

logger = logging.getLogger(__name__)


class MetricsCollector:
    """Centralized metrics collection for SMS Hub."""
    
    def __init__(self):
        self.enabled = config.monitoring.enabled and PROMETHEUS_AVAILABLE
        
        if not self.enabled:
            logger.warning("Monitoring disabled or Prometheus not available")
            return
        
        # SMS Processing Metrics
        self.sms_received_total = Counter(
            'smshub_sms_received_total',
            'Total number of SMS messages received',
            ['modem_port', 'status']
        )
        
        self.sms_processed_total = Counter(
            'smshub_sms_processed_total',
            'Total number of SMS messages processed',
            ['status', 'priority']
        )
        
        self.sms_forwarded_total = Counter(
            'smshub_sms_forwarded_total',
            'Total number of SMS messages forwarded',
            ['status', 'attempt']
        )
        
        self.sms_processing_duration = Histogram(
            'smshub_sms_processing_duration_seconds',
            'Time spent processing SMS messages',
            ['operation', 'status']
        )
        
        # Modem Metrics
        self.active_modems = Gauge(
            'smshub_active_modems_count',
            'Number of active modems',
            ['status']
        )
        
        self.modem_health_score = Gauge(
            'smshub_modem_health_score',
            'Health score of individual modems (0-1)',
            ['port', 'model']
        )
        
        self.modem_response_time = Histogram(
            'smshub_modem_response_time_seconds',
            'Modem response time for AT commands',
            ['port', 'command']
        )
        
        # Queue Metrics
        self.queue_size = Gauge(
            'smshub_queue_size',
            'Current queue size',
            ['queue_name', 'priority']
        )
        
        self.queue_processing_rate = Gauge(
            'smshub_queue_processing_rate_per_minute',
            'Queue processing rate per minute',
            ['queue_name']
        )
        
        # Database Metrics
        self.database_operations_total = Counter(
            'smshub_database_operations_total',
            'Total database operations',
            ['operation', 'table', 'status']
        )
        
        self.database_connection_pool_size = Gauge(
            'smshub_database_connection_pool_size',
            'Database connection pool size',
            ['status']
        )
        
        # API Metrics
        self.api_requests_total = Counter(
            'smshub_api_requests_total',
            'Total API requests',
            ['endpoint', 'method', 'status_code']
        )
        
        self.api_request_duration = Histogram(
            'smshub_api_request_duration_seconds',
            'API request duration',
            ['endpoint', 'method']
        )
        
        # Circuit Breaker Metrics
        self.circuit_breaker_state = Gauge(
            'smshub_circuit_breaker_state',
            'Circuit breaker state (0=closed, 1=open, 2=half-open)',
            ['service']
        )
        
        self.circuit_breaker_failures = Counter(
            'smshub_circuit_breaker_failures_total',
            'Total circuit breaker failures',
            ['service']
        )
        
        # System Info
        self.system_info = Info(
            'smshub_system_info',
            'System information'
        )
        
        # Set system info
        self.system_info.info({
            'version': '2.0.0',
            'database_type': config.database.type,
            'redis_enabled': str(config.redis.host != 'localhost' or config.redis.port != 6379),
            'monitoring_enabled': str(self.enabled)
        })
        
        logger.info("Metrics collector initialized")
    
    def record_sms_received(self, modem_port: str, status: str = "success"):
        """Record SMS received metric."""
        if self.enabled:
            self.sms_received_total.labels(modem_port=modem_port, status=status).inc()
    
    def record_sms_processed(self, status: str, priority: int = 2):
        """Record SMS processed metric."""
        if self.enabled:
            self.sms_processed_total.labels(status=status, priority=str(priority)).inc()
    
    def record_sms_forwarded(self, status: str, attempt: int = 1):
        """Record SMS forwarded metric."""
        if self.enabled:
            self.sms_forwarded_total.labels(status=status, attempt=str(attempt)).inc()
    
    def record_processing_time(self, operation: str, duration: float, status: str = "success"):
        """Record processing time metric."""
        if self.enabled:
            self.sms_processing_duration.labels(operation=operation, status=status).observe(duration)
    
    def update_active_modems(self, count: int, status: str = "healthy"):
        """Update active modems count."""
        if self.enabled:
            self.active_modems.labels(status=status).set(count)
    
    def update_modem_health(self, port: str, health_score: float, model: str = "unknown"):
        """Update modem health score."""
        if self.enabled:
            self.modem_health_score.labels(port=port, model=model).set(health_score)
    
    def record_modem_response_time(self, port: str, command: str, duration: float):
        """Record modem response time."""
        if self.enabled:
            self.modem_response_time.labels(port=port, command=command).observe(duration)
    
    def update_queue_size(self, queue_name: str, size: int, priority: str = "normal"):
        """Update queue size metric."""
        if self.enabled:
            self.queue_size.labels(queue_name=queue_name, priority=priority).set(size)
    
    def update_queue_processing_rate(self, queue_name: str, rate: float):
        """Update queue processing rate."""
        if self.enabled:
            self.queue_processing_rate.labels(queue_name=queue_name).set(rate)
    
    def record_database_operation(self, operation: str, table: str, status: str = "success"):
        """Record database operation."""
        if self.enabled:
            self.database_operations_total.labels(operation=operation, table=table, status=status).inc()
    
    def update_database_pool_size(self, active: int, idle: int):
        """Update database connection pool metrics."""
        if self.enabled:
            self.database_connection_pool_size.labels(status="active").set(active)
            self.database_connection_pool_size.labels(status="idle").set(idle)
    
    def record_api_request(self, endpoint: str, method: str, status_code: int, duration: float):
        """Record API request metrics."""
        if self.enabled:
            self.api_requests_total.labels(endpoint=endpoint, method=method, status_code=str(status_code)).inc()
            self.api_request_duration.labels(endpoint=endpoint, method=method).observe(duration)
    
    def update_circuit_breaker_state(self, service: str, state: int):
        """Update circuit breaker state (0=closed, 1=open, 2=half-open)."""
        if self.enabled:
            self.circuit_breaker_state.labels(service=service).set(state)
    
    def record_circuit_breaker_failure(self, service: str):
        """Record circuit breaker failure."""
        if self.enabled:
            self.circuit_breaker_failures.labels(service=service).inc()


# Global metrics collector
metrics = MetricsCollector()


def timed_operation(operation_name: str, status_on_success: str = "success"):
    """Decorator to time operations and record metrics."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            status = status_on_success
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                status = "error"
                raise
            finally:
                duration = time.time() - start_time
                metrics.record_processing_time(operation_name, duration, status)
        return wrapper
    return decorator


def monitor_api_endpoint(endpoint_name: str):
    """Decorator to monitor API endpoints."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            status_code = 200
            try:
                result = func(*args, **kwargs)
                # Try to extract status code from Flask response
                if hasattr(result, 'status_code'):
                    status_code = result.status_code
                return result
            except Exception as e:
                status_code = 500
                raise
            finally:
                duration = time.time() - start_time
                method = "GET"  # Default, could be extracted from request context
                metrics.record_api_request(endpoint_name, method, status_code, duration)
        return wrapper
    return decorator


class HealthChecker:
    """System health monitoring."""
    
    def __init__(self):
        self.last_check = datetime.utcnow()
        self.health_status = {}
        self.check_interval = 30  # seconds
        self._running = False
        self._thread = None
    
    def start(self):
        """Start health monitoring."""
        if self._running:
            return
        
        self._running = True
        self._thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self._thread.start()
        logger.info("Health checker started")
    
    def stop(self):
        """Stop health monitoring."""
        self._running = False
        if self._thread:
            self._thread.join(timeout=5)
        logger.info("Health checker stopped")
    
    def _health_check_loop(self):
        """Main health check loop."""
        while self._running:
            try:
                self._perform_health_checks()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Health check error: {e}")
                time.sleep(self.check_interval)
    
    def _perform_health_checks(self):
        """Perform all health checks."""
        self.last_check = datetime.utcnow()
        
        # Check database connectivity
        self._check_database_health()
        
        # Check Redis connectivity
        self._check_redis_health()
        
        # Check queue health
        self._check_queue_health()
        
        # Update metrics
        self._update_health_metrics()
    
    def _check_database_health(self):
        """Check database health."""
        try:
            from .database_enhanced import _engine
            if _engine:
                with _engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                self.health_status['database'] = {'status': 'healthy', 'last_check': self.last_check}
            else:
                self.health_status['database'] = {'status': 'degraded', 'last_check': self.last_check, 'reason': 'Using SQLite fallback'}
        except Exception as e:
            self.health_status['database'] = {'status': 'unhealthy', 'last_check': self.last_check, 'error': str(e)}
    
    def _check_redis_health(self):
        """Check Redis health."""
        try:
            from .message_queue import redis_queue
            if redis_queue.is_connected():
                self.health_status['redis'] = {'status': 'healthy', 'last_check': self.last_check}
            else:
                self.health_status['redis'] = {'status': 'unhealthy', 'last_check': self.last_check, 'reason': 'Not connected'}
        except Exception as e:
            self.health_status['redis'] = {'status': 'unhealthy', 'last_check': self.last_check, 'error': str(e)}
    
    def _check_queue_health(self):
        """Check message queue health."""
        try:
            from .message_queue import redis_queue
            queue_size = redis_queue.get_queue_size()
            if queue_size < 1000:  # Threshold for healthy queue
                self.health_status['queue'] = {'status': 'healthy', 'last_check': self.last_check, 'size': queue_size}
            else:
                self.health_status['queue'] = {'status': 'degraded', 'last_check': self.last_check, 'size': queue_size, 'reason': 'Queue size high'}
        except Exception as e:
            self.health_status['queue'] = {'status': 'unhealthy', 'last_check': self.last_check, 'error': str(e)}
    
    def _update_health_metrics(self):
        """Update health-related metrics."""
        # This would update various health metrics
        pass
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status."""
        overall_status = "healthy"
        
        for component, status in self.health_status.items():
            if status.get('status') == 'unhealthy':
                overall_status = "unhealthy"
                break
            elif status.get('status') == 'degraded' and overall_status == "healthy":
                overall_status = "degraded"
        
        return {
            'overall_status': overall_status,
            'last_check': self.last_check.isoformat(),
            'components': self.health_status
        }


# Global health checker
health_checker = HealthChecker()


def start_metrics_server():
    """Start Prometheus metrics server."""
    if not PROMETHEUS_AVAILABLE or not config.monitoring.enabled:
        logger.warning("Metrics server not started - Prometheus not available or monitoring disabled")
        return
    
    try:
        start_http_server(config.monitoring.metrics_port)
        logger.info(f"Metrics server started on port {config.monitoring.metrics_port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")


def start_monitoring():
    """Start all monitoring components."""
    start_metrics_server()
    health_checker.start()
    logger.info("Monitoring system started")


def stop_monitoring():
    """Stop all monitoring components."""
    health_checker.stop()
    logger.info("Monitoring system stopped")
