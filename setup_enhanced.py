#!/usr/bin/env python3
"""
Enhanced SMS Hub Setup Script
Installs dependencies and configures the enhanced SMS Hub application.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description=""):
    """Run a command and handle errors."""
    print(f"Running: {description or command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"Python version: {sys.version}")
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("\n=== Installing Python Dependencies ===")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
        return False
    
    return True

def setup_database():
    """Setup database configuration."""
    print("\n=== Database Setup ===")
    
    db_choice = input("Choose database type (1=SQLite, 2=PostgreSQL, 3=MySQL): ").strip()
    
    if db_choice == "1":
        print("Using SQLite (default) - no additional setup required")
        return True
    elif db_choice == "2":
        return setup_postgresql()
    elif db_choice == "3":
        return setup_mysql()
    else:
        print("Invalid choice, using SQLite")
        return True

def setup_postgresql():
    """Setup PostgreSQL configuration."""
    print("\nPostgreSQL Setup:")
    print("Please ensure PostgreSQL is installed and running")
    
    host = input("PostgreSQL host (localhost): ").strip() or "localhost"
    port = input("PostgreSQL port (5432): ").strip() or "5432"
    username = input("PostgreSQL username: ").strip()
    password = input("PostgreSQL password: ").strip()
    database = input("Database name (smshub): ").strip() or "smshub"
    
    # Test connection
    try:
        import psycopg2
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            database=database
        )
        conn.close()
        print("PostgreSQL connection successful!")
        
        # Update .env file
        update_env_file({
            "DB_TYPE": "postgresql",
            "DB_HOST": host,
            "DB_PORT": port,
            "DB_USERNAME": username,
            "DB_PASSWORD": password,
            "DB_NAME": database
        })
        return True
        
    except ImportError:
        print("Error: psycopg2 not installed. Installing...")
        if run_command(f"{sys.executable} -m pip install psycopg2-binary"):
            return setup_postgresql()  # Retry
        return False
    except Exception as e:
        print(f"PostgreSQL connection failed: {e}")
        return False

def setup_mysql():
    """Setup MySQL configuration."""
    print("\nMySQL Setup:")
    print("Please ensure MySQL is installed and running")
    
    host = input("MySQL host (localhost): ").strip() or "localhost"
    port = input("MySQL port (3306): ").strip() or "3306"
    username = input("MySQL username: ").strip()
    password = input("MySQL password: ").strip()
    database = input("Database name (smshub): ").strip() or "smshub"
    
    # Test connection
    try:
        import pymysql
        conn = pymysql.connect(
            host=host,
            port=int(port),
            user=username,
            password=password,
            database=database
        )
        conn.close()
        print("MySQL connection successful!")
        
        # Update .env file
        update_env_file({
            "DB_TYPE": "mysql",
            "DB_HOST": host,
            "DB_PORT": port,
            "DB_USERNAME": username,
            "DB_PASSWORD": password,
            "DB_NAME": database
        })
        return True
        
    except ImportError:
        print("Error: PyMySQL not installed. Installing...")
        if run_command(f"{sys.executable} -m pip install pymysql"):
            return setup_mysql()  # Retry
        return False
    except Exception as e:
        print(f"MySQL connection failed: {e}")
        return False

def setup_redis():
    """Setup Redis configuration."""
    print("\n=== Redis Setup ===")
    
    use_redis = input("Use Redis for message queuing? (y/n): ").strip().lower()
    if use_redis != 'y':
        print("Skipping Redis setup - will use database fallback")
        return True
    
    host = input("Redis host (localhost): ").strip() or "localhost"
    port = input("Redis port (6379): ").strip() or "6379"
    password = input("Redis password (optional): ").strip()
    
    # Test Redis connection
    try:
        import redis
        r = redis.Redis(host=host, port=int(port), password=password or None)
        r.ping()
        print("Redis connection successful!")
        
        # Update .env file
        update_env_file({
            "REDIS_HOST": host,
            "REDIS_PORT": port,
            "REDIS_PASSWORD": password
        })
        return True
        
    except ImportError:
        print("Error: redis not installed. Installing...")
        if run_command(f"{sys.executable} -m pip install redis"):
            return setup_redis()  # Retry
        return False
    except Exception as e:
        print(f"Redis connection failed: {e}")
        print("You can continue without Redis - the application will use database fallback")
        return True

def update_env_file(config_dict):
    """Update .env file with configuration."""
    env_path = Path("smshub_app/.env")
    env_example_path = Path("smshub_app/.env.example")
    
    # Copy example if .env doesn't exist
    if not env_path.exists() and env_example_path.exists():
        shutil.copy(env_example_path, env_path)
        print(f"Created {env_path} from example")
    
    # Update values
    if env_path.exists():
        with open(env_path, 'r') as f:
            lines = f.readlines()
        
        with open(env_path, 'w') as f:
            for line in lines:
                key = line.split('=')[0] if '=' in line else None
                if key and key in config_dict:
                    f.write(f"{key}={config_dict[key]}\n")
                else:
                    f.write(line)

def setup_smshub_config():
    """Setup SMS Hub API configuration."""
    print("\n=== SMS Hub API Configuration ===")
    
    api_key = input("SMS Hub API Key: ").strip()
    agent_id = input("SMS Hub Agent ID: ").strip()
    protocol_key = input("SMS Hub Protocol Key: ").strip()
    
    update_env_file({
        "SMSHUB_API_KEY": api_key,
        "SMSHUB_AGENT_ID": agent_id,
        "SMSHUB_PROTOCOL_KEY": protocol_key
    })
    
    print("SMS Hub configuration updated")

def create_systemd_service():
    """Create systemd service file for Linux."""
    if os.name != 'posix':
        print("Systemd service creation is only available on Linux")
        return
    
    create_service = input("Create systemd service? (y/n): ").strip().lower()
    if create_service != 'y':
        return
    
    current_dir = os.path.abspath(os.getcwd())
    python_path = sys.executable
    
    service_content = f"""[Unit]
Description=SMS Hub Enhanced Application
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'smshub')}
WorkingDirectory={current_dir}/smshub_app
Environment=PATH={os.path.dirname(python_path)}
ExecStart={python_path} -m app.main
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_path = "/etc/systemd/system/smshub-enhanced.service"
    
    try:
        with open("smshub-enhanced.service", "w") as f:
            f.write(service_content)
        
        print(f"Service file created: smshub-enhanced.service")
        print(f"To install, run: sudo cp smshub-enhanced.service {service_path}")
        print("Then run: sudo systemctl enable smshub-enhanced && sudo systemctl start smshub-enhanced")
        
    except Exception as e:
        print(f"Error creating service file: {e}")

def main():
    """Main setup function."""
    print("=== SMS Hub Enhanced Setup ===")
    print("This script will help you set up the enhanced SMS Hub application")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("Failed to install dependencies")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("Database setup failed")
        sys.exit(1)
    
    # Setup Redis
    if not setup_redis():
        print("Redis setup failed, continuing with database fallback")
    
    # Setup SMS Hub configuration
    setup_smshub_config()
    
    # Create systemd service
    create_systemd_service()
    
    print("\n=== Setup Complete ===")
    print("Enhanced SMS Hub has been configured!")
    print("\nNext steps:")
    print("1. Review the configuration in smshub_app/.env")
    print("2. Start the application: cd smshub_app && python -m app.main")
    print("3. Access the dashboard at: http://localhost:5000/dashboard")
    print("4. Monitor metrics at: http://localhost:8000 (if monitoring enabled)")
    print("\nFor production deployment, consider:")
    print("- Using a production WSGI server (gunicorn, uwsgi)")
    print("- Setting up proper logging")
    print("- Configuring firewall rules")
    print("- Setting up SSL/TLS certificates")

if __name__ == "__main__":
    main()
