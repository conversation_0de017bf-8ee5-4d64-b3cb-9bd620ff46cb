Here's a detailed description of modem_manager.py that an AI agent could use to recreate it:

Purpose:
Manages multiple USB modems (Franklin T9, Novatel 551L, Fibocom L850GL)
Scans for connected modems periodically
Handles SMS sending/receiving
Maintains modem state and connectivity
Key Components:
ModemManager class (main class with these methods):
__init__: Initializes modem tracking dictionaries
_scan_modems: Scans USB ports for supported modems
_add_modem: Initializes and adds a new modem
check_sms: Checks for new SMS messages
handle_sms_received: Processes incoming SMS
start/stop: Manages background scanning thread
Dependencies:
Python packages: serial, logging, threading, re, time
Local modules: config, modem implementations (franklin_t9.py, novatel_551l.py, fibocom_l850gl_v2.py)
Key Functionality:
Automatic modem detection by VID/PID
Thread-safe port access with locking
Network registration monitoring
SMS message parsing and handling
Detailed logging for diagnostics
State management for active/inactive modems
Modem Requirements:
Must have valid IMEI, ICCID, phone number
Must be registered on network (home or roaming)
Must pass signal quality checks
Error Handling:
Comprehensive error logging
Retry mechanisms for network operations
Port locking to prevent conflicts
State preservation during errors
Configuration:
Scan interval (default: 30 seconds)
Logging verbosity
Server integration for SMS handling
Implementation Notes:
Uses serial port communication (AT commands)
Supports multiple modem types through polymorphism
Maintains last known good states
Includes extensive diagnostic logging
Thread-safe design for concurrent operations
