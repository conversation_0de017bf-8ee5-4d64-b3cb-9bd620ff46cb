## High-Level Overview
Build a SMS handling system that acts as an intermediary agent between SMSHUB's server and physical GSM modems. The system should manage phone numbers, and rereivce otp activation codes activations for various services and forward SMS messages to SMSHUB's server. The system should also be able to handle incoming SMS messages and provide real-time phone number availability.

### Managing and monitoring GSM modems
Handling incoming SMS messages
Providing real-time phone number availability
Processing activation requests
Delivering SMS messages to SMSHUB's server
Managing activation statuses and lifecycle
Detailed System Requirements
Core Components
API Server
Implement a HTTP/HTTPS server that handles incoming requests from SMSHUB
Support JSON request/response format with UTF-8 encoding
Implement gzip compression for all requests/responses
Include user-agent header in all requests to SMSHUB server
Handle four main API endpoints: a. GET_SERVICES (quantity request) b. GET_NUMBER (number request) c. FINISH_ACTIVATION (activation status update) d. Outbound PUSH_SMS endpoint for delivering SMS to SMSHUB
Modem Management System
Create an abstraction layer for different modem types
Implement modem detection and initialization
Monitor modem status and health
Handle AT commands for modem communication
Manage SMS reading and processing
Track modem availability and current assignments
Activation Management System
Track active phone number assignments
Maintain activation states
Handle activation lifecycle (request → assignment → completion)
Process activation status updates
Implement idempotency for activation status updates
SMS Processing System
Monitor incoming SMS messages
Parse and validate SMS content
Match SMS to active activations
Queue and retry SMS delivery to SMSHUB
Track SMS delivery status
Detailed API Requirements
1. GET_SERVICES Endpoint
Accept POST requests with action="GET_SERVICES" and key parameter
Return available numbers grouped by:
Country (from approved country list)
Operator (from approved operator list)
Service (from approved service list)
Return actual available numbers count (not theoretical capacity)
Update availability every 10-20 seconds
Handle response format:
{
  "countryList": [
    {
      "country": "country_code",
      "operatorMap": {
        "operator_name": {
          "service_name": count
        }
      }
    }
  ],
  "status": "SUCCESS"
}
2. GET_NUMBER Endpoint
Accept POST requests with parameters:
action="GET_NUMBER"
key
country
operator
service
sum (payment amount)
currency (643 for RUB, 840 for USD)
exceptionPhoneSet (optional)
Validate phone number availability
Handle prefix exclusions
Return response within 3 seconds
Return format:
{
  "number": "phone_number",
  "activationId": "unique_id",
  "status": "SUCCESS"
}
3. FINISH_ACTIVATION Endpoint
Accept POST requests with:
action="FINISH_ACTIVATION"
key
activationId
status (1, 3, 4, or 5)
Implement idempotent processing
Handle different activation statuses:
1: Stop providing number for service
3: Successfully sold
4: Cancelled (can be reused up to 3 more times)
5: Refunded
4. PUSH_SMS Implementation
Send POST requests to SMSHUB with:
action="PUSH_SMS"
key
smsId
phone (with country code)
phoneFrom
text
Implement retry mechanism:
Retry every 10 seconds on non-SUCCESS response
Stop retrying after SUCCESS response
Track delivery status
Technical Requirements
Error Handling
Implement comprehensive error handling
Return appropriate error statuses and messages
Handle network issues gracefully
Implement timeout handling
Data Validation
Validate all incoming requests
Verify phone number formats
Check currency codes
Validate country and operator codes
Performance Requirements
Respond to GET_NUMBER requests within 3 seconds
Update service availability every 10-20 seconds
Process SMS messages immediately upon receipt
Handle multiple concurrent requests
Security
Validate API keys
Implement secure communication
Protect sensitive data
Handle rate limiting
Logging and Monitoring
Log all API requests and responses
Track activation lifecycles
Monitor modem status
Log SMS processing and delivery attempts
Data Storage
Store activation records
Track SMS delivery status
Maintain modem status and assignments
Keep service availability counts
This system should be built to be reliable, scalable, and maintainable, with clear separation of concerns between components. The implementation should focus on robustness and error handling, ensuring that all edge cases are properly managed and that the system can recover gracefully from failures.


# Modem Management Specifications
Modem Communication
Implement serial communication with GSM modems using configurable:
Port settings (COM port)
Baud rate (default 115200)
Timeout settings (1 second default)
Support AT command protocol for modem control
Handle modem initialization sequence:
Basic modem setup (AT, ATE1V1)
SMS text mode configuration (AT+CMGF=1)
Message notification setup (AT+CNMI=2,2,0,0,0)
SIM status verification (AT+CPIN?)
Signal quality check (AT+CSQ)
Network registration status (AT+CREG?)
Phone number retrieval (AT+CNUM)
SMS Reception System
Implement threaded message monitoring
Buffer-based SMS message parsing
Support for direct message forwarding format:
+CMT: "+**********",,"YY/MM/DD,HH:MM:SS-TZ"
Message content here
Handle message metadata extraction
Implement proper message queuing and storage
Modem Status Management
Track connection state
Monitor modem health
Handle modem registration/unregistration
Support status updates
Implement proper cleanup on disconnect
SMS Processing Requirements
Message Parsing
Parse CMT format messages
Extract metadata components:
Sender phone number
Timestamp
Message content
Handle international phone number formats
Support error recovery for malformed messages
Message Storage
Implement persistent SMS storage
Store metadata and content separately
Support message retrieval by:
Modem ID
Time range
Message limit
Track message delivery status
Message Delivery System
Implement retry mechanism:
Maximum 5 retries
10-second delay between attempts
Success/failure tracking
Support idempotent message delivery
Handle delivery confirmations
API Integration Requirements
SMSHUB API Communication
Implement gzip compression
Use custom User-Agent header: 'SMSHubAgent/1.0'
Support JSON request/response format
Handle API authentication via key
Implement timeout handling (10 seconds)
API Endpoints Implementation
GET_SERVICES:

Return available numbers by country/operator/service
Update every 10-20 seconds
Return actual available count
GET_NUMBER:

Handle country, operator, service parameters
Support currency specification (RUB: 643, USD: 840)
Process exception phone sets
Return number and activation ID
FINISH_ACTIVATION:

Support all activation statuses (1,3,4,5)
Implement idempotent processing
Handle activation state tracking
PUSH_SMS:

Include required fields:
smsId (unique identifier)
phone (recipient with country code)
phoneFrom (sender identifier)
text (message content)
Optional fields:
modemId
activationId
Error Handling
Implement comprehensive error logging
Support automatic retry mechanisms
Handle network timeouts
Process API error responses
Maintain error state tracking
Configuration Management
System Configuration
Load configuration from JSON file
Support dynamic configuration updates
Handle missing configuration gracefully
Validate configuration values
API Configuration
Manage API keys securely
Configure endpoint URLs
Set timeout values
Define retry parameters
Modem Configuration
Support multiple modem configurations
Handle port assignments
Configure communication parameters
Set monitoring intervals
Logging Requirements
Comprehensive Logging
Log all API requests/responses
Track modem status changes
Record SMS processing steps
Monitor system health
Support different log levels (INFO, DEBUG, ERROR)
Debug Information
Log raw modem communication
Track message parsing steps
Record API interaction details
Monitor thread status
Error Tracking
Log all exceptions with stack traces
Track failed delivery attempts
Monitor modem errors
Record configuration issues
This technical specification provides a detailed framework for implementing the SMSHUB Agent system, ensuring proper handling of all required functionality while maintaining robustness and reliability.

Let me check if there are any other key files I should examine to ensure the specification is complete.


Roo wants to read this file:


Let me add server implementation details and statistics tracking requirements based on the server implementation I've reviewed.

Here are additional specifications for the server implementation and statistics tracking:

Server Implementation Requirements
HTTP Server Configuration
Implement using Flask framework
Configure for production use:
Disable debug mode
Enable threading
Listen on all interfaces (0.0.0.0)
Default port 5000
Enable CORS with specific configuration:
Allow all origins for POST requests
Allow headers: Content-Type, User-Agent, Accept-Encoding
Enable gzip compression
Disable JSON pretty printing
Maintain consistent key ordering
Tunnel Management
Support localtonet/ngrok tunnel setup
Store tunnel URL in configuration
Handle tunnel setup failures gracefully
Support dynamic tunnel URL updates
API Routes
Implement core SMSHUB protocol endpoints:
POST / - Main API handler
GET /api/modems - Modem status
GET /api/modems/<modem_id>/messages - Modem messages
GET /api/messages - All messages
Response Formatting
Implement standardized response creation
Include required headers:
Content-Type: application/json
User-Agent: SMSHubAgent/1.0
Accept-Encoding: gzip
Handle JSON serialization errors
Provide proper error responses
Activation Management Requirements
Active Number Tracking
Maintain active_numbers dictionary:
{
  phone_number: {
    'service': service_name,
    'activation_id': id,
    'modem_id': modem_id,
    'assigned_at': timestamp
  }
}
Track completed activations set
Handle number assignment/release
Prevent duplicate assignments
Activation Status Management
Track different activation states:
Status 1: Service cancellation
Status 3: Successful sale
Status 4: Cancellation (reusable)
Status 5: Refund
Handle status transitions
Update activation records
Release numbers appropriately
Statistics Tracking
Track key metrics:
Total activations
Completed activations
Cancelled activations
Refunded activations
Total earnings
Service-specific stats
Activation times
Persist statistics
Support statistics retrieval
Track earnings by timeframe
Service Management Requirements
Service Configuration
Load services from config file
Track enabled/disabled states
Support dynamic service updates
Validate service configurations
Service Availability
Track available modems per service
Update availability counts
Handle service capacity
Report accurate availability
Logging Requirements
Activation Logging
Implement ActivationLogger
Log activation events:
Status changes
Completion
Cancellation
Refunds
Track activation history
Support log retrieval
Message Logging
Log all SMS activities
Track message delivery
Store message metadata
Support message queries
System Logging
Log server operations
Track modem status changes
Record API interactions
Monitor system health
Error Handling Requirements
Request Validation
Validate all incoming requests
Check required fields
Verify field types
Handle missing data
Error Responses
Provide detailed error messages
Include error status codes
Handle internal errors
Maintain error consistency
Recovery Mechanisms
Handle initialization failures
Recover from service disruptions
Manage connection issues
Support graceful degradation
Performance Requirements
Resource Management
Implement proper cleanup
Handle thread management
Manage memory usage
Control connection pools
Response Times
Process requests efficiently
Handle concurrent requests
Maintain response SLAs
Monitor performance metrics
Scalability
Support multiple modems
Handle increased load
Manage resource allocation
Support system growth
This specification provides a comprehensive framework for implementing the server component of the SMSHUB Agent system, ensuring proper handling of all required functionality while maintaining robustness, reliability, and scalability.

I should check if there are any additional configuration requirements specified in the config files.


Roo wants to read this file:


Let me add configuration requirements and service support details based on the config.json file.

Here are additional specifications for configuration and service support:

Configuration Requirements
Core Configuration
Support JSON-based configuration file
Required configuration fields:
smshub_api_url: SMSHUB API endpoint
smshub_api_key: Authentication key
modems: Array of modem configurations
services: Service availability map
tunnel: Tunnel configuration
Modem Configuration
Support multiple modem definitions:
{
  "id": "unique_modem_id",
  "port": "COM port name",
  "type": "modem type (e.g., fibocom)"
}
Validate modem configurations
Support dynamic modem addition/removal
Service Configuration
Support extensive service list (150+ services)
Common services include:
wa: WhatsApp
tu: Telegram
vi: Viber
fb: Facebook
mm: Microsoft/Outlook
And many other social, messaging, and verification services
Enable/disable services individually
Track service status
Tunnel Configuration
Store tunnel URL
Support ngrok/localtonet integration
Handle tunnel updates
Validate tunnel connectivity
Service Support Requirements
Service Management
Support all configured services (150+)
Handle service-specific requirements
Track service availability
Monitor service usage
Service Categories
Messaging Services:
WhatsApp (wa)
Telegram (tu)
Viber (vi)
Facebook Messenger (fb)
Email Services:
Microsoft/Outlook (mm)
Mail.ru (ma)
Social Networks:
Facebook (fb)
Instagram (ig)
Twitter/X (tw)
Banking/Financial:
Various bank-specific services
E-commerce:
Amazon (am)
eBay (eb)
And many other categories
Service Status Tracking
Track per-service:
Available numbers
Active activations
Success rates
Usage patterns
Monitor service health
Report service metrics
Service Validation
Verify service availability
Check service compatibility
Validate service requirements
Monitor service performance
Dynamic Service Management
Service Updates
Support dynamic service enabling/disabling
Handle service configuration changes
Update service availability
Maintain service consistency
Service Monitoring
Track service usage
Monitor service performance
Report service statistics
Handle service issues
Service Optimization
Balance service load
Optimize number allocation
Improve service reliability
Enhance service performance
Implementation Guidelines
Configuration Management
Load configuration at startup
Support configuration reloading
Validate configuration changes
Handle configuration errors
Service Integration
Implement service-specific handlers
Support service protocols
Handle service requirements
Maintain service documentation
Monitoring and Reporting
Track service metrics
Generate service reports
Monitor service health
Alert on service issues
Error Handling
Handle service failures
Manage configuration errors
Process service exceptions
Maintain service stability
This specification provides a comprehensive framework for implementing the configuration and service support components of the SMSHUB Agent system, ensuring proper handling of all required services while maintaining flexibility and reliability.

Let me check if there are any additional requirements in the plan file.


Roo wants to read this file:


Let me add API organization and error handling requirements based on the planned improvements in plan.md.

Here are additional specifications for API organization and error handling:

API Organization Requirements
RESTful Endpoint Structure
Implement dedicated endpoints for each action:
POST /api/services      - Handle GET_SERVICES
POST /api/number       - Handle GET_NUMBER
POST /api/activation   - Handle FINISH_ACTIVATION
POST /api/sms         - Handle PUSH_SMS
GET  /api/modems      - Get modem status
GET  /api/modems/{id}/messages - Get modem messages
GET  /api/messages    - Get all messages
Remove catch-all endpoint pattern
Implement proper route handlers
Maintain backward compatibility
API Handler Organization
Implement dedicated handler classes/methods:
ServiceHandler - Handle service-related operations
NumberHandler - Handle number allocation
ActivationHandler - Handle activation lifecycle
MessageHandler - Handle SMS operations
Separate business logic from route handling
Implement proper dependency injection
Maintain clean separation of concerns
Request Processing
Validate request content type
Parse request parameters
Handle query parameters
Process request body
Validate required fields
Check parameter types
Enhanced Error Handling
Error Response Structure
{
  "status": "ERROR",
  "error": {
    "code": "error_code",
    "message": "Human readable message",
    "details": {
      "field": "Additional error context"
    }
  }
}
HTTP Status Codes
Use appropriate status codes:
200 - Successful operation
400 - Bad Request (invalid parameters)
401 - Unauthorized (invalid API key)
404 - Resource not found
409 - Conflict (e.g., number already assigned)
429 - Too Many Requests
500 - Internal Server Error
503 - Service Unavailable
Error Categories
Implementation Errors:
Invalid request format
Missing required fields
Invalid field types
Validation failures
Business Logic Errors:
No numbers available
Invalid activation status
Service not available
Modem offline
System Errors:
Database errors
Network issues
External service failures
Resource constraints
Enhanced Logging System
Logging Levels
ERROR: System errors, API failures
WARNING: Business rule violations
INFO: Normal operations
DEBUG: Detailed operation info
TRACE: Request/response details
Sensitive Data Handling
Never log API keys
Mask phone numbers in logs
Exclude personal information
Sanitize error messages
Control log verbosity by environment
Structured Logging
Include consistent metadata:
{
  "timestamp": "ISO timestamp",
  "level": "log level",
  "event": "event name",
  "component": "component name",
  "request_id": "unique id",
  "details": {
    "operation": "operation name",
    "status": "status",
    "duration": "operation duration"
  }
}
Support log aggregation
Enable log searching
Facilitate debugging
Development Support
Debug Mode Features
Detailed error messages
Stack traces in development
Request/response logging
Performance metrics
Memory usage tracking
Testing Support
Mock API responses
Simulate error conditions
Test different configurations
Validate error handling
Verify logging behavior
Documentation
API endpoint documentation
Error code reference
Configuration guide
Troubleshooting guide
Development setup instructions
Security Enhancements
Request Validation
Validate API keys
Check request origins
Verify content types
Validate input data
Prevent injection attacks
Rate Limiting
Implement per-endpoint limits
Track request frequency
Handle limit exceeded
Provide rate information
Support burst handling
Access Control
Validate permissions
Track API usage
Monitor access patterns
Log security events
Handle unauthorized access
This specification provides a comprehensive framework for implementing a robust, maintainable, and secure API system with proper error handling and logging capabilities. The implementation should follow these guidelines to ensure consistency and reliability across all endpoints while maintaining security and performance requirements.

Let me check if there are any additional requirements in the requirements.txt file that might inform the technical stack specifications.


Roo wants to read this file:


Let me add technical stack requirements based on the dependencies listed in requirements.txt.

Technical Stack Requirements
Core Dependencies
Python 3.x (compatible with typing-extensions 4.7.0+)
Package management using pip/requirements.txt
Serial Communication
pyserial >= 3.5
Support for serial port communication
Modem control via AT commands
Configurable baudrate and timeouts
Cross-platform compatibility
Web Framework
Flask >= 2.0.0
Core web application framework
Route handling
Request processing
Response formatting
Flask Extensions:
flask-cors >= 4.0.0
Cross-Origin Resource Sharing support
Configurable CORS policies
Header management
flask-compress >= 1.13.0
Response compression
gzip support
Configurable compression levels
HTTP Client
requests >= 2.31.0
API communication
SSL/TLS support
Session management
Retry handling
Timeout configuration
Header management
Type System
typing-extensions >= 4.7.0
Static type checking
Type hints
Enhanced code documentation
Better IDE support
Date/Time Handling
python-dateutil >= 2.8.2
Date parsing
Timezone management
Date arithmetic
ISO format support
Logging System
colorlog >= 6.7.0
Colored console output
Log level differentiation
Format customization
Enhanced readability
Development Environment Requirements
IDE Support
VSCode recommended
Python extension
Type checking support
Linting integration
Debugging capabilities
Testing Environment
Unit testing framework
Integration testing support
Mock serial port capability
API testing tools
Documentation
Type hints
Docstrings
API documentation
Setup instructions
Version Control
Git repository
.gitignore configuration
Branch management
Release tagging
Deployment Requirements
System Requirements
Python 3.x runtime
Serial port access
Network connectivity
Sufficient memory for operations
Installation Process
Requirements installation
Configuration setup
Environment preparation
Service configuration
Monitoring
Log collection
Performance monitoring
Error tracking
Resource usage
Maintenance
Package updates
Security patches
Configuration management
Backup procedures