# High-Level Overview

Build an SMS handling system that acts as an intermediary agent between SMSHUB's server and physical GSM modems. The system should manage phone number activations for various services (like VK, WhatsApp, etc.) by:

- Managing and monitoring GSM modems
- Handling incoming SMS messages
- Providing real-time phone number availability
- Processing activation requests
- Delivering SMS messages to SMSHUB's server
- Managing activation statuses and lifecycle

## Detailed System Requirements

### Core Components

#### API Server
- Implement an HTTP/HTTPS server that handles incoming requests from SMSHUB
- Support JSON request/response format with UTF-8 encoding
- Implement gzip compression for all requests/responses
- Include a user-agent header in all requests to SMSHUB server
- Handle four main API endpoints:
  1. `GET_SERVICES` (quantity request)
  2. `GET_NUMBER` (number request)
  3. `FINISH_ACTIVATION` (activation status update)
  4. Outbound `PUSH_SMS` endpoint for delivering SMS to SMSHUB

#### Modem Management System
- Create an abstraction layer for different modem types
- Implement modem detection and initialization
- Monitor modem status and health
- Handle AT commands for modem communication
- Manage SMS reading and processing
- Track modem availability and current assignments

#### Activation Management System
- Track active phone number assignments
- Maintain activation states
- Handle activation lifecycle (request → assignment → completion)
- Process activation status updates
- Implement idempotency for activation status updates

#### SMS Processing System
- Monitor incoming SMS messages
- Parse and validate SMS content
- Match SMS to active activations
- Queue and retry SMS delivery to SMSHUB
- Track SMS delivery status

## Detailed API Requirements

### 1. `GET_SERVICES` Endpoint
- Accept POST requests with action="GET_SERVICES" and key parameter
- Return available numbers grouped by:
  - Country (from approved country list)
  - Operator (from approved operator list)
  - Service (from approved service list)
  - Return actual available numbers count (not theoretical capacity)
  - Update availability every 10-20 seconds
- Response format:

```json
{
  "countryList": [
    {
      "country": "country_code",
      "operatorMap": {
        "operator_name": {
          "service_name": count
        }
      }
    }
  ],
  "status": "SUCCESS"
}
```

### 2. `GET_NUMBER` Endpoint
- Accept POST requests with parameters:
  - action="GET_NUMBER"
  - key
  - country
  - operator
  - service
  - sum (payment amount)
  - currency (643 for RUB, 840 for USD)
  - exceptionPhoneSet (optional)
- Validate phone number availability
- Handle prefix exclusions
- Return response within 3 seconds
- Return format:

```json
{
  "number": "phone_number",
  "activationId": "unique_id",
  "status": "SUCCESS"
}
```

### 3. `FINISH_ACTIVATION` Endpoint
- Accept POST requests with:
  - action="FINISH_ACTIVATION"
  - key
  - activationId
  - status (1, 3, 4, or 5)
- Implement idempotent processing
- Handle different activation statuses:
  - 1: Stop providing number for service
  - 3: Successfully sold
  - 4: Cancelled (can be reused up to 3 more times)
  - 5: Refunded

### 4. `PUSH_SMS` Implementation
- Send POST requests to SMSHUB with:
  - action="PUSH_SMS"
  - key
  - smsId
  - phone (with country code)
  - phoneFrom
  - text
- Implement retry mechanism:
  - Retry every 10 seconds on non-SUCCESS response
  - Stop retrying after SUCCESS response
  - Track delivery status

## Technical Requirements

### Error Handling
- Implement comprehensive error handling
- Return appropriate error statuses and messages
- Handle network issues gracefully
- Implement timeout handling

### Data Validation
- Validate all incoming requests
- Verify phone number formats
- Check currency codes
- Validate country and operator codes

### Performance Requirements
- Respond to `GET_NUMBER` requests within 3 seconds
- Update service availability every 10-20 seconds
- Process SMS messages immediately upon receipt
- Handle multiple concurrent requests

### Security
- Validate API keys
- Implement secure communication
- Protect sensitive data
- Handle rate limiting

### Logging and Monitoring
- Log all API requests and responses
- Track activation lifecycles
- Monitor modem status
- Log SMS processing and delivery attempts

### Data Storage
- Store activation records
- Track SMS delivery status
- Maintain modem status and assignments
- Keep service availability counts

This system should be built to be reliable, scalable, and maintainable, with clear separation of concerns between components. The implementation should focus on robustness and error handling, ensuring that all edge cases are properly managed and that the system can recover gracefully from failures.

---

### Modem Management Specifications

#### Modem Communication
- Implement serial communication with GSM modems using configurable:
  - Port settings (COM port)
  - Baud rate (default 115200)
  - Timeout settings (1 second default)
- Support AT command protocol for modem control
- Handle modem initialization sequence:
  - Basic modem setup (AT, ATE1V1)
  - SMS text mode configuration (AT+CMGF=1)
  - Message notification setup (AT+CNMI=2,2,0,0,0)
  - SIM status verification (AT+CPIN?)
  - Signal quality check (AT+CSQ)
  - Network registration status (AT+CREG?)
  - Phone number retrieval (AT+CNUM)

#### SMS Reception System
- Implement threaded message monitoring
- Buffer-based SMS message parsing
- Support for direct message forwarding format:
  - `+CMT: "+**********",,"YY/MM/DD,HH:MM:SS-TZ"`
  - Message content here
- Handle message metadata extraction
- Implement proper message queuing and storage

#### Modem Status Management
- Track connection state
- Monitor modem health
- Handle modem registration/unregistration
- Support status updates
- Implement proper cleanup on disconnect

### SMS Processing Requirements

#### Message Parsing
- Parse CMT format messages
- Extract metadata components:
  - Sender phone number
  - Timestamp
  - Message content
- Handle international phone number formats
- Support error recovery for malformed messages

#### Message Storage
- Implement persistent SMS storage
- Store metadata and content separately
- Support message retrieval by:
  - Modem ID
  - Time range
  - Message limit
- Track message delivery status

#### Message Delivery System
- Implement retry mechanism:
  - Maximum 5 retries
  - 10-second delay between attempts
  - Success/failure tracking
- Support idempotent message delivery
- Handle delivery confirmations

### API Integration Requirements

#### SMSHUB API Communication
- Implement gzip compression
- Use custom User-Agent header: `SMSHubAgent/1.0`
- Support JSON request/response format
- Handle API authentication via key
- Implement timeout handling (10 seconds)

### Configuration Management

#### System Configuration
- Load configuration from JSON file
- Support dynamic configuration updates
- Handle missing configuration gracefully
- Validate configuration values

#### API Configuration
- Manage API keys securely
- Configure endpoint URLs
- Set timeout values
- Define retry parameters

#### Modem Configuration
- Support multiple modem configurations
- Handle port assignments
- Configure communication parameters
- Set monitoring intervals
