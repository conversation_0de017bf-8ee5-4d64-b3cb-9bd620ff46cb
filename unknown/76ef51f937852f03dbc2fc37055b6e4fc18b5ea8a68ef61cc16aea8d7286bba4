#!/usr/bin/env python3
"""
Test script to verify message filtering functionality.
"""

import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.message_filter import should_ignore_modem_response, should_ignore_log_message, is_valid_sms_content

def test_modem_response_filtering():
    """Test modem response filtering."""
    print("Testing modem response filtering...")
    
    # Test cases that should be ignored
    ignore_cases = [
        "OK",
        "ERROR", 
        "",
        "   ",  # whitespace only
        "AT+CMGF=1",
        "^RSSI: 15",
        "+CREG: 0,1",
        "+CSQ: 15,99",
        "RING",
        "NO CARRIER",
        "BUSY"
    ]
    
    # Test cases that should NOT be ignored (valid SMS content)
    valid_cases = [
        "Hello world",
        "Your verification code is 123456",
        "Meeting at 3pm today",
        "Call me when you get this",
        "Thanks for your message!"
    ]
    
    print("Testing cases that should be IGNORED:")
    for case in ignore_cases:
        result = should_ignore_modem_response(case)
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {status}: '{case}' -> ignored={result}")
    
    print("\nTesting cases that should be ALLOWED:")
    for case in valid_cases:
        result = should_ignore_modem_response(case)
        status = "✓ PASS" if not result else "✗ FAIL"
        print(f"  {status}: '{case}' -> ignored={result}")

def test_log_message_filtering():
    """Test log message filtering."""
    print("\nTesting log message filtering...")
    
    # Test cases that should be ignored
    ignore_cases = [
        "Reader for COM3: readline() timed out",
        "Reader for COM3: Top of main loop",
        "Reader for COM3: Attempting readline()",
        "Reader for COM3: Processing line: ''",
        "Reader for COM3: Processing line: 'OK'",
        "Reader for COM3: received 0 bytes",
        "Reader for COM3: decoded line (utf-8): 'OK'"
    ]
    
    # Test cases that should NOT be ignored
    valid_cases = [
        "Reader for COM3: Successfully parsed SMS message",
        "Reader for COM3: New SMS received from +1234567890",
        "Reader for COM3: Error connecting to modem",
        "Reader for COM3: SMS forwarded successfully"
    ]
    
    print("Testing log cases that should be IGNORED:")
    for case in ignore_cases:
        result = should_ignore_log_message(case)
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {status}: '{case}' -> ignored={result}")
    
    print("\nTesting log cases that should be ALLOWED:")
    for case in valid_cases:
        result = should_ignore_log_message(case)
        status = "✓ PASS" if not result else "✗ FAIL"
        print(f"  {status}: '{case}' -> ignored={result}")

def test_sms_content_validation():
    """Test SMS content validation."""
    print("\nTesting SMS content validation...")
    
    # Test cases that should be valid SMS content
    valid_cases = [
        "Hello world",
        "Your verification code is 123456",
        "Meeting at 3pm today",
        "Call me when you get this",
        "Thanks for your message!",
        "Order #12345 has been shipped"
    ]
    
    # Test cases that should NOT be valid SMS content
    invalid_cases = [
        "OK",
        "ERROR",
        "",
        "   ",
        "AT+CMGF=1",
        "123",     # short numbers (likely not verification codes)
        "...",     # just punctuation
        "+CREG: 0,1"
    ]
    
    print("Testing cases that should be VALID SMS content:")
    for case in valid_cases:
        result = is_valid_sms_content(case)
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {status}: '{case}' -> valid={result}")
    
    print("\nTesting cases that should be INVALID SMS content:")
    for case in invalid_cases:
        result = is_valid_sms_content(case)
        status = "✓ PASS" if not result else "✗ FAIL"
        print(f"  {status}: '{case}' -> valid={result}")

if __name__ == "__main__":
    print("Message Filter Test Suite")
    print("=" * 50)
    
    try:
        test_modem_response_filtering()
        test_log_message_filtering()
        test_sms_content_validation()
        
        print("\n" + "=" * 50)
        print("Test suite completed!")
        print("\nThe filtering should now prevent noisy debug lines from:")
        print("1. Being logged (if configured in ignore_log_patterns)")
        print("2. Being saved to the database as SMS messages")
        print("\nCheck your config.json for the new filtering options:")
        print("- ignore_log_patterns: Controls which log messages are suppressed")
        print("- ignore_message_patterns: Controls which modem responses are ignored")
        print("- quiet_mode: General flag for quieter operation")
        
    except Exception as e:
        print(f"\nError during testing: {e}")
        print("Make sure you're running this from the smshub_app directory")
        sys.exit(1)
