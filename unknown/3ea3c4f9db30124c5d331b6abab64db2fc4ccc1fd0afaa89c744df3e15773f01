Okay, AI Coding Agent. Your mission is to rebuild the "proxysmart" system based on the provided file list. Follow these detailed, step-by-step instructions precisely.

You are capable of creating subtasks for research or specific coding modules and waiting for their results before continuing the main sequence. Use this capability whenever instructed.

**Project Goal:** Rebuild the functionality of "proxysmart", a modem-centric proxy management system, based on the provided file list.

**Output:** A complete set of source code files and configuration instructions replicating the functionality identified for each file.

---

**BUILD GUIDE START**

**PHASE 0: System Environment Setup**

**STEP 0.1: Target System & Dependencies**
*   **INSTRUCTION:** Define the target operating system environment for the rebuild. Assume a recent Debian-based Linux distribution (like Ubuntu or Debian) for package management and system tools compatibility, unless research indicates a different primary target OS for the original proxysmart.
*   **ACTION:** Install core system dependencies using the package manager (`apt`).

    *   **REQUIRED PACKAGES:** `curl`, `jq`, `perl`, `python3`, `python3-pip`, `usb-modeswitch`, `modemmanager`, `libqmi-utils`, `libmbim-utils`, `iptables`, `nftables`, `uhubctl`, `sg3-utils`, `microcom` (or equivalent serial tool).
    *   **ACTION:** EXECUTE: `sudo apt update && sudo apt install -y curl jq perl python3 python3-pip usb-modeswitch modemmanager libqmi-utils libmbim-utils iptables nftables uhubctl sg3-utils microcom`

**STEP 0.2: Install Python Libraries**
*   **ACTION:** Install necessary Python libraries using pip.

    *   **REQUIRED LIBRARIES:** `pyserial`, `requests`. (Others may be identified during research).
    *   **ACTION:** EXECUTE: `pip install pyserial requests`

**STEP 0.3: Install Gost**
*   **INSTRUCTION:** Obtain and install the `gost` and `gost3` binaries. Assume `gost3` refers to a specific version or build of Gost.
*   **ACTION:**
    *   **CREATE SUBTASK: Research "gost proxy download binary".** OBJECTIVE: Find the official download location for pre-compiled Gost binaries (including potentially older versions if `gost3` suggests that) for the target Linux architecture. Deliverable: URLs to download `gost` and `gost3` binaries.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** DOWNLOAD the identified binaries.
    *   **ACTION:** PLACE the downloaded binaries in a directory within the system's PATH (e.g., `/usr/local/bin/`).
    *   **ACTION:** MAKE the binaries executable (`chmod +x`).

**STEP 0.4: Install V2Ray**
*   **INSTRUCTION:** Obtain and install the `v2ray` and `v2ctl` binaries.
*   **ACTION:**
    *   **CREATE SUBTASK: Research "v2ray proxy download binary".** OBJECTIVE: Find the official download location for the Project V distribution package containing `v2ray` and `v2ctl` for the target Linux architecture. Deliverable: URL to download the V2Ray distribution package.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** DOWNLOAD and EXTRACT the V2Ray distribution package.
    *   **ACTION:** PLACE the `v2ray` and `v2ctl` binaries in a directory within the system's PATH (e.g., `/usr/local/bin/`).
    *   **ACTION:** MAKE the binaries executable (`chmod +x`).

**STEP 0.5: Install shell2http**
*   **INSTRUCTION:** Obtain and install the `shell2http` binary.
*   **ACTION:**
    *   **CREATE SUBTASK: Research "shell2http download binary".** OBJECTIVE: Find the official download location for pre-compiled `shell2http` binaries for the target Linux architecture. Deliverable: URL to download the `shell2http` binary.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** DOWNLOAD the identified binary.
    *   **ACTION:** PLACE the downloaded binary in a directory within the system's PATH (e.g., `/usr/local/bin/`).
    *   **ACTION:** MAKE the binary executable (`chmod +x`).

**STEP 0.6: Install json_xs**
*   **INSTRUCTION:** Ensure `json_xs` is installed. It's typically part of Perl's JSON-XS library.
*   **ACTION:** EXECUTE: `sudo apt install -y libjson-xs-perl`

**PHASE 1: Foundational System Interaction (USB & Serial)**

**STEP 1.1: Implement USB Reset Utility (`usbreset1`)**
*   **INSTRUCTION:** Create a simple utility to perform a software reset of a USB device by path.
*   **ACTION:**
    *   WRITE a C program named `usbreset1.c` that takes the device path (e.g., `/dev/bus/usb/001/005`) as an argument and uses the `ioctl(fd, USBDEVFS_RESET, 0)` call.
    *   COMPILE the C program: `gcc usbreset1.c -o usbreset1`.
    *   PLACE the `usbreset1` executable in a directory within the system's PATH (e.g., `/usr/local/bin/`).
    *   MAKE the binary executable (`chmod +x`).

**STEP 1.2: Implement Serial AT Command Sender (`sendat.pl`)**
*   **INSTRUCTION:** Create a Perl script to send AT commands over a serial port and read the response.
*   **ACTION:**
    *   WRITE a Perl script named `sendat.pl`.
    *   The script MUST accept two command-line arguments: the serial port device path (e.g., `/dev/ttyUSB0`) and the AT command string.
    *   The script MUST open the specified serial port, configure it (e.g., 115200 baud, 8N1), send the AT command followed by a carriage return (`\r`), wait for a response, and print the response to standard output.
    *   The script SHOULD include a timeout mechanism for reading the response.
    *   PLACE `sendat.pl` in the project's script directory (define a standard directory, e.g., `/opt/proxysmart/scripts/`).
    *   MAKE the script executable (`chmod +x`).

**STEP 1.3: Implement USB Ignore Script (`usb_ignore.sh`)**
*   **INSTRUCTION:** Create a script to prevent specific USB interfaces (like storage or CD-ROM) on modems from being claimed by standard kernel drivers. This is crucial for `usb_modeswitch` or custom control.
*   **ACTION:**
    *   WRITE a shell script named `usb_ignore.sh`.
    *   The script MUST take the USB device path or identifier as an argument.
    *   The script MUST use mechanisms like writing to `/sys/bus/usb/drivers/.../unbind` or generating temporary `udev` rules to detach unwanted drivers (e.g., `usb-storage`, `uas`).
    *   **CREATE SUBTASK: Research common methods to unbind USB drivers from specific devices in Linux.** OBJECTIVE: Identify reliable shell commands or patterns for detaching kernel modules from a given USB device identifier (Vendor ID, Product ID, or device path). Deliverable: Example shell commands for unbinding drivers.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** IMPLEMENT the driver unbinding logic in `usb_ignore.sh` based on research results.
    *   PLACE `usb_ignore.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 1.4: Implement SCSI Interaction Script (`SCSI.sh`)**
*   **INSTRUCTION:** Create a script to interact with modem interfaces that expose themselves as SCSI devices, often used for mode switching or diagnostics.
*   **ACTION:**
    *   WRITE a shell script named `SCSI.sh`.
    *   The script MUST take the SCSI device path (e.g., `/dev/sg1`) and a specific command/action as arguments.
    *   The script MUST use tools from `sg3-utils` (like `sg_modes`, `sg_raw`) to send commands.
    *   **CREATE SUBTASK: Research common SCSI commands used by modems for mode switching (e.g., Huawei, ZTE).** OBJECTIVE: Find specific `sg_raw` command examples or sequences used to switch modems from storage/CD-ROM mode to modem/ethernet mode. Deliverable: Example `sg_raw` commands for mode switching.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** IMPLEMENT the SCSI command sending logic in `SCSI.sh` based on research results.
    *   PLACE `SCSI.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 1.5: Implement Udev Hook (`udevhook.sh`)**
*   **INSTRUCTION:** Create a script triggered by `udev` rules to identify and initialize newly connected modems.
*   **ACTION:**
    *   WRITE a shell script named `udevhook.sh`.
    *   The script MUST accept `udev` environment variables (like `$ACTION`, `$DEVTYPE`, `$ID_VENDOR_ID`, `$ID_MODEL_ID`, `$DEVNAME`, `$DEVPATH`).
    *   When `$ACTION` is "add" and `$DEVTYPE` is relevant (e.g., "usb_device"), the script MUST:
        *   Identify the specific modem model based on `$ID_VENDOR_ID` and `$ID_MODEL_ID`.
        *   Log the detection event (device details).
        *   Execute `usb_ignore.sh` for any unwanted interfaces detected on the device (requires identifying those interfaces based on research/rules).
        *   Execute `usb_modeswitch` if the device is in the wrong mode (requires `usb_modeswitch` data file setup).
        *   Wait for the modem interfaces (`/dev/ttyUSB*`, `/dev/cdc-wdm*`, `wwan*`, `ppp*`) to appear.
        *   Call the appropriate model-specific helper script (to be created later in Phase 7) to perform initial setup.
    *   PLACE `udevhook.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 1.6: Create Udev Rules**
*   **INSTRUCTION:** Create `udev` rules that trigger `udevhook.sh` when relevant USB devices (modems) are connected.
*   **ACTION:**
    *   **CREATE SUBTASK: Research standard udev rules for matching USB modems by Vendor/Product ID and triggering a script.** OBJECTIVE: Find examples of `udev` rules that match specific USB device attributes and execute an external script, passing environment variables. Deliverable: Example `udev` rule syntax.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** CREATE a file named `99-proxysmart.rules` (or similar) in `/etc/udev/rules.d/`.
    *   **ACTION:** WRITE `udev` rules in this file. The rules MUST match common modem Vendor/Product IDs (add more as needed for supported models) and execute `udevhook.sh` with the appropriate environment when `ACTION=="add"`.
    *   **ACTION:** Reload `udev` rules: `sudo udevadm control --reload-rules && sudo udevadm trigger`.

**PHASE 2: Modem Management Framework**

**STEP 2.1: Implement Modem Name Lister (`modem_names.sh`)**
*   **INSTRUCTION:** Create a script to list connected modems and potentially identify their types or states.
*   **ACTION:**
    *   WRITE a shell script named `modem_names.sh`.
    *   The script MUST use tools like `mmcli -L`, `uqmi --list-devices`, and/or parse the output of `lsusb` combined with `udevadm info` to list connected devices that are identified as modems or have known modem Vendor/Product IDs.
    *   The output format SHOULD be a simple list, perhaps with a unique identifier for each modem instance.
    *   PLACE `modem_names.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 2.2: Implement Generic Modem Helper Interface**
*   **INSTRUCTION:** Define a standard command-line interface and expected functionality for all model-specific helper scripts (`*_hlp.sh`, `*_helper.sh`, etc.). This ensures the main orchestrator can interact with any modem helper consistently.
*   **ACTION:**
    *   **DEFINE STANDARD HELPER COMMANDS:** `status`, `signal`, `connect`, `disconnect`, `reboot`, `mode_switch [MODE]`, `send_sms [NUMBER] [MESSAGE]`, `read_sms`, `ussd [CODE]`, `info`.
    *   **DEFINE STANDARD ARGUMENTS:** All helper scripts MUST accept at least `[MODEM_IDENTIFIER]` as the first argument (this could be a system path, a ModemManager ID, or a custom ID defined by the orchestrator). The second argument MUST be the command (`status`, `signal`, etc.). Subsequent arguments are command-specific.
    *   **DEFINE STANDARD EXIT CODES:** `0` for success, non-zero for failure.
    *   **DEFINE STANDARD OUTPUT FORMATS:** `status` might output "CONNECTED", "DISCONNECTED", etc. `signal` might output a percentage or dBm value. `read_sms` might output a structured format (JSON?).

**STEP 2.3: Create a Helper Script Template**
*   **INSTRUCTION:** Create a template file for model-specific helper scripts based on the interface defined in STEP 2.2.
*   **ACTION:**
    *   CREATE a file named `modem_helper_template.sh`.
    *   WRITE a shell script structure with a `case` statement (or similar) that handles the standard commands (`status`, `signal`, etc.).
    *   Include placeholder functions or comments for where the model-specific logic for each command will go.
    *   Include basic argument parsing to get the modem identifier and command.

**PHASE 3: Networking and Routing**

**STEP 3.1: Implement IP Forwarding and NAT**
*   **INSTRUCTION:** Configure the system to allow IP forwarding and set up basic NAT rules so traffic can pass through the modem interfaces.
*   **ACTION:**
    *   ENABLE IP forwarding permanently: `sudo sysctl net.ipv4.ip_forward=1` and ensure `net.ipv4.ip_forward = 1` is in `/etc/sysctl.conf`.
    *   **CREATE SUBTASK: Research iptables/nftables rules for masquerading traffic originating from internal networks/users out through dynamic interfaces (like wwan0, ppp0).** OBJECTIVE: Find the correct `iptables` or `nftables` commands to set up SNAT/MASQUERADE for traffic leaving the system via modem interfaces. Deliverable: Example `iptables`/`nftables` commands.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** WRITE a shell script named `setup_nat.sh` that applies the necessary `iptables`/`nftables` rules based on research results. This script will need to identify the active modem interfaces.
    *   PLACE `setup_nat.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 3.2: Implement Port Forwarding (`port-forwarder.py`)**
*   **INSTRUCTION:** Create a Python script to set up port forwarding rules using `iptables` or `nftables`.
*   **ACTION:**
    *   WRITE a Python script named `port-forwarder.py`.
    *   The script MUST accept arguments specifying the input interface/port, destination IP/port, and the forwarding type (TCP/UDP).
    *   The script MUST use `subprocess` to execute `iptables` or `nftables` commands to create DNAT rules.
    *   The script SHOULD include options to add and delete rules.
    *   PLACE `port-forwarder.py` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 3.3: Implement Gost-Specific Port Forwarding (`port-forwarder_gost.py`)**
*   **INSTRUCTION:** Create a Python script specifically for setting up `iptables`/`nftables` rules that redirect traffic to a local `gost` listener port.
*   **ACTION:**
    *   WRITE a Python script named `port-forwarder_gost.py`.
    *   The script MUST accept arguments specifying the input interface/port and the local `gost` listener IP/port.
    *   The script MUST use `subprocess` to execute `iptables` or `nftables` commands to create REDIRECT or DNAT rules that send incoming traffic to the local `gost` address/port.
    *   The script SHOULD include options to add and delete rules.
    *   PLACE `port-forwarder_gost.py` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**PHASE 4: Proxy Integration**

**STEP 4.1: Configure Gost Instances**
*   **INSTRUCTION:** Define a method for configuring `gost` instances.
*   **ACTION:**
    *   **CREATE SUBTASK: Research Gost configuration file formats and common command-line options for running SOCKS, HTTP, or direct tunnel listeners.** OBJECTIVE: Understand how to configure `gost` to listen on specific IPs/ports, chain proxies, and potentially route traffic. Deliverable: Example Gost configuration file snippets and command lines.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** DEFINE a standard configuration file format or command-line parameter structure that the orchestrator will use to launch `gost` processes based on research results. Assume `gost3` is just another `gost` instance potentially with a different config.

**STEP 4.2: Configure V2Ray Instances**
*   **INSTRUCTION:** Define a method for configuring `v2ray` instances.
*   **ACTION:**
    *   **CREATE SUBTASK: Research V2Ray configuration file formats and command-line options for running proxy listeners (like SOCKS, HTTP, VMess).** OBJECTIVE: Understand how to configure `v2ray` to listen on specific IPs/ports, define inbound/outbound protocols, and use its routing capabilities. Deliverable: Example V2Ray configuration file snippets and command lines.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** DEFINE a standard configuration file format or command-line parameter structure that the orchestrator will use to launch `v2ray` processes based on research results. Use `v2ctl` as needed for configuration generation or testing if indicated by research.

**PHASE 5: Orchestration (`proxysmart.sh`)**

**STEP 5.1: Implement the Main Orchestration Script**
*   **INSTRUCTION:** Create the central script that manages the entire system.
*   **ACTION:**
    *   WRITE a shell script named `proxysmart.sh`.
    *   The script MUST include functions for:
        *   Reading a main configuration file (DEFINE a simple format, e.g., INI or YAML-like, mapping modems to proxy types/configurations).
        *   Identifying available modems (calling `modem_names.sh` or similar).
        *   Launching modem helpers (`*_hlp.sh`) for initialization.
        *   Launching `gost` and `v2ray` processes based on the configuration, managing their process IDs (PIDs).
        *   Applying necessary networking rules (`setup_nat.sh`, `port-forwarder*.py`) after modems and proxies are ready.
        *   Monitoring the health of modems (e.g., periodically calling helper `status`/`signal` commands).
        *   Monitoring the health of proxy processes.
        *   Implementing recovery actions (e.g., calling helper `reboot` or using `uhubctl` for power cycle if a modem fails, restarting proxy processes).
        *   Providing command-line arguments for actions like `start`, `stop`, `restart`, `status`, `list_modems`, `list_proxies`.
    *   The script MUST handle multiple modems and proxy instances if the configuration requires it.
    *   The script MUST use proper signal handling (e.g., catching SIGINT, SIGTERM) to cleanly shut down processes.
    *   PLACE `proxysmart.sh` in the project's root directory.
    *   MAKE the script executable (`chmod +x`).

**PHASE 6: Utilities and Enhancements**

**STEP 6.1: Implement Cached Execution (`run_cached`)**
*   **INSTRUCTION:** Create a script that runs a command and caches its output for a specified duration.
*   **ACTION:**
    *   WRITE a shell script named `run_cached`.
    *   The script MUST accept at least two arguments: the cache duration in seconds and the command to run.
    *   The script MUST use a temporary file (based on a hash of the command string) to store the output and the timestamp of the last execution.
    *   If the cache file exists and the timestamp is within the duration, read and print the cached output.
    *   Otherwise, execute the command, capture its output, store the output and current timestamp in the cache file, and print the output.
    *   PLACE `run_cached` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 6.2: Implement Speed Test (`speedtest-curl`, `speedtest-cli`)**
*   **INSTRUCTION:** Provide tools for measuring internet speed. `speedtest-cli` is a standard tool, `speedtest-curl` implies a custom implementation.
*   **ACTION:**
    *   **For `speedtest-cli`:** Verify it was installed in Phase 0. It requires no further coding unless a specific wrapper is needed.
    *   **For `speedtest-curl`:**
        *   WRITE a shell script named `speedtest-curl`.
        *   The script MUST use `curl` to download a large file (specify a reliable test file URL) and measure the download speed using `curl -w '%{speed_download}\n'`.
        *   The script MAY attempt to measure upload speed by uploading a generated file.
        *   The script SHOULD output the speed in a parseable format.
        *   PLACE `speedtest-curl` in the project's script directory.
        *   MAKE the script executable (`chmod +x`).

**STEP 6.3: Implement Log Analysis (`proxy_log_analyze.sh`, `proxy_log_dump.sh`)**
*   **INSTRUCTION:** Create scripts to process proxy logs.
*   **ACTION:**
    *   DEFINE a standard log file location and format for the proxy logs (Gost, V2Ray). Assume text-based logs.
    *   WRITE a shell script named `proxy_log_dump.sh`. This script MUST filter and display relevant log entries based on arguments (e.g., time range, proxy instance, source IP). Use `grep`, `sed`, `awk`.
    *   WRITE a shell script named `proxy_log_analyze.sh`. This script MUST perform analysis on the logs (e.g., count connections per modem/proxy, identify errors, summarize traffic). Use `grep`, `awk`, `sort`, `uniq`, `jq` (if logs become JSON).
    *   PLACE both scripts in the project's script directory.
    *   MAKE the scripts executable (`chmod +x`).

**STEP 6.4: Implement Imgur Upload (`imgur.sh`)**
*   **INSTRUCTION:** Create a script to upload an image file to Imgur.
*   **ACTION:**
    *   **CREATE SUBTASK: Research the Imgur API for anonymous image uploads.** OBJECTIVE: Find the API endpoint and method (usually POST) for uploading an image, and how to authenticate (usually a Client-ID for anonymous uploads). Deliverable: API endpoint URL, required headers (especially Client-ID), and expected POST body format.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** WRITE a shell script named `imgur.sh`.
    *   The script MUST accept the path to an image file as an argument.
    *   The script MUST use `curl` to send a POST request to the Imgur API endpoint identified in research, including the necessary headers and the image file data.
    *   The script MUST parse the JSON response (using `jq`) to extract the uploaded image URL.
    *   The script MUST print the resulting Imgur URL to standard output.
    *   PLACE `imgur.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 6.5: Implement OpenVPN User Creator (`openvpn_create_user`)**
*   **INSTRUCTION:** Create a script to automate the process of generating OpenVPN client configurations. Assume the system might also act as an OpenVPN client or server.
*   **ACTION:**
    *   **CREATE SUBTASK: Research the standard procedure and tools (easy-rsa) for generating OpenVPN client certificates, keys, and configuration files.** OBJECTIVE: Understand the steps involved with `easy-rsa` to set up a CA, issue client certificates, and combine them with the CA certificate and server details into a client `.ovpn` file. Deliverable: Key `easy-rsa` commands and the structure of a client `.ovpn` file.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** WRITE a shell script named `openvpn_create_user`.
    *   The script MUST accept a username as an argument.
    *   The script MUST use `easy-rsa` commands (assuming `easy-rsa` is installed or placed in a known location) to generate a client certificate and key for the given username based on research results.
    *   The script MUST generate a `.ovpn` client configuration file including the generated certificate, key, and CA certificate, along with placeholder server address details (these details would need to be part of the script's configuration or arguments).
    *   The script MUST output the generated `.ovpn` file.
    *   PLACE `openvpn_create_user` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 6.6: Implement OS Fooler (`osfooler-ng`)**
*   **INSTRUCTION:** Implement a tool to modify system characteristics for OS fingerprinting evasion. This is a complex, potentially low-level task.
*   **ACTION:**
    *   **CREATE SUBTASK: Research common OS fingerprinting techniques (passive and active) and methods to alter system identifiers, TCP/IP stack parameters, and user agents in Linux.** OBJECTIVE: Identify specific `sysctl` parameters, kernel module options, environment variables, or network tools that can be used to modify an OS's detectable characteristics. Discover if open-source tools exist for this specific purpose. Deliverable: A report on fingerprinting methods and potential modification techniques/tools.
    *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
    *   **ACTION:** Based on research, DETERMINE the scope of `osfooler-ng`.
        *   IF research reveals a standard open-source tool exists, INTEGRATE that tool.
        *   IF research identifies specific sysctl/command-line methods, WRITE a script (`osfooler-ng.sh`) that applies these modifications.
        *   IF research indicates a complex, novel approach is required, CREATE a placeholder script (`osfooler-ng.sh`) that logs a message indicating its intended function but does not implement it fully, and add a note that significant further development is required for this component.
    *   PLACE `osfooler-ng` (or `osfooler-ng.sh`) in the project's script directory.
    *   MAKE it executable (`chmod +x`).

**STEP 6.7: Implement USSD Sender/Reader (`ussd.sh`)**
*   **INSTRUCTION:** Create a script to send USSD codes to a modem and read the response.
*   **ACTION:**
    *   WRITE a shell script named `ussd.sh`.
    *   The script MUST accept the modem identifier and the USSD code (e.g., `*100#`) as arguments.
    *   The script MUST send the USSD command using an appropriate method:
        *   PREFERRED: Use `mmcli` (`mmcli -m [MODEM_ID] --messaging-create-ussd="[CODE]"` and then check for responses).
        *   FALLBACK: Use the `sendat.pl` script with the appropriate AT command (`AT+CUSD=1,"[CODE]",15`) on the modem's primary serial port and parse the response.
    *   The script MUST wait for and print the USSD response received from the modem.
    *   PLACE `ussd.sh` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**STEP 6.8: Implement Two-Bar Signal Display (`2bars.py`)**
*   **INSTRUCTION:** Create a Python script to get modem signal strength and display it visually using characters.
*   **ACTION:**
    *   WRITE a Python script named `2bars.py`.
    *   The script MUST accept the modem identifier as an argument.
    *   The script MUST get the current signal strength for the specified modem.
        *   PREFERRED: Execute `mmcli -m [MODEM_ID] --signal-get` and parse the output.
        *   FALLBACK: Execute the model-specific helper script with the `signal` command (`[HELPER_SCRIPT] [MODEM_ID] signal`).
    *   The script MUST translate the signal strength value (e.g., RSSI, percentage) into a simple character representation (e.g., 0-5 bars using `#` or `|`).
    *   The script MUST print the visual representation to standard output.
    *   PLACE `2bars.py` in the project's script directory.
    *   MAKE the script executable (`chmod +x`).

**PHASE 7: Model-Specific Modem Implementations**

**STEP 7.1: Implement Specific Modem Helpers**
*   **INSTRUCTION:** Create the individual helper scripts for each specific modem model identified in the file list, following the interface defined in STEP 2.2. This is the most repetitive step.
*   **ACTION:**
    *   ITERATE through each model-specific helper filename from the original list (e.g., `alcatel_HH71_hlp.sh`, `Fibocom_L850_hlp.sh`, `zte_MF79UV_hlp.sh`, etc.).
    *   For *each* model filename (`[HELPER_FILENAME]`) and corresponding model name (`[MODEM_MODEL]`):
        *   CREATE a new file with the name `[HELPER_FILENAME]`.
        *   COPY the content of `modem_helper_template.sh` into the new file.
        *   **CREATE SUBTASK: Research modem interaction methods for [MODEM_MODEL].** OBJECTIVE: Identify the primary way to communicate with this specific modem model (AT commands via serial, QMI via `/dev/cdc-wdm` with `uqmi`/`mmcli`, MBIM via `/dev/cdc-wdm` with `mmcli`/`mbimcli`, web UI API via `curl`, vendor-specific tools). Find the *exact* commands/API calls needed to perform the standard helper commands (`status`, `signal`, `connect`, `disconnect`, `reboot`, `mode_switch`, `send_sms`, `read_sms`, `ussd`, `info`). Deliverable: A report detailing the interaction method and the specific commands/API calls for each standard helper function for [MODEM_MODEL].
        *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
        *   **ACTION:** IMPLEMENT the logic for each standard helper command (`status`, `signal`, etc.) within `[HELPER_FILENAME]` using the interaction methods and commands identified in the research results. Use tools like `sendat.pl`, `microcom`, `mmcli`, `uqmi`, `curl`, `SCSI.sh`, `usb_modeswitch`, `uhubctl`, `usbreset1` as appropriate for the modem model and command.
        *   PLACE `[HELPER_FILENAME]` in the project's script directory.
        *   MAKE the script executable (`chmod +x`).
    *   HANDLE the Python helpers (`CPE_helper.py`, `H112-370_hlp.py`) similarly, writing Python scripts that implement the standard helper commands using Python libraries (`pyserial`, `requests`, `subprocess`).

**STEP 7.2: Implement Special Case Helpers (`huawei_e3131_mode.sh`, `zte_MF688V3T_s_r.sh`, `zte_MF93D_sms_r.sh`)**
*   **INSTRUCTION:** Implement helpers with more specific names, likely focusing on a subset of the standard commands or specific device quirks.
*   **ACTION:**
    *   For each special helper filename (`[SPECIAL_HELPER_FILENAME]`) and implied model (`[MODEM_MODEL]`):
        *   CREATE a new file named `[SPECIAL_HELPER_FILENAME]`.
        *   **CREATE SUBTASK: Research the specific functionality indicated by the filename for [MODEM_MODEL].** OBJECTIVE: For `*_mode.sh`, find commands to switch modes. For `*_s_r.sh`, find commands for status and reboot. For `*_sms_r.sh`, find commands to read SMS. Deliverable: Specific commands/methods for the named function(s).
        *   **WAIT FOR SUBTASK COMPLETION and REVIEW RESULTS.**
        *   **ACTION:** WRITE a shell script (`[SPECIAL_HELPER_FILENAME]`) that implements the specific functionality identified in the research. This script might be called *by* the main model-specific helper script for that modem (`[MODEM_MODEL]_hlp.sh`) or directly by the orchestrator for specific tasks.
        *   PLACE the script in the project's script directory.
        *   MAKE the script executable (`chmod +x`).

**PHASE 8: Testing and Refinement**

**STEP 8.1: Basic Script Testing**
*   **INSTRUCTION:** Test individual scripts to ensure they function correctly.
*   **ACTION:**
    *   TEST `sendat.pl` with a simple command like `AT` on a connected modem serial port.
    *   TEST `usbreset1` on a non-critical USB device.
    *   TEST `modem_names.sh` with modems connected.
    *   TEST a few implemented model-specific helper commands (e.g., `status`, `signal`) for modems you have available.

**STEP 8.2: Networking Testing**
*   **INSTRUCTION:** Test NAT and port forwarding rules.
*   **ACTION:**
    *   RUN `setup_nat.sh`.
    *   VERIFY IP forwarding is enabled.
    *   VERIFY NAT rules are loaded (`iptables -t nat -L` or `nft list ruleset`).
    *   TEST internet connectivity through a configured modem interface from the system itself.
    *   USE `port-forwarder.py` and `port-forwarder_gost.py` to set up test rules and verify traffic redirection using tools like `netcat` or connecting from another machine.

**STEP 8.3: Proxy Testing**
*   **INSTRUCTION:** Test Gost and V2Ray instances.
*   **ACTION:**
    *   MANUALLY LAUNCH Gost and V2Ray instances with simple configurations.
    *   TEST connecting to these proxy listeners from a client using a proxy client (e.g., `curl --socks5` or browser proxy settings).
    *   VERIFY traffic routes correctly through the modem connected to the proxy's interface.

**STEP 8.4: Orchestration Testing**
*   **INSTRUCTION:** Test the main `proxysmart.sh` orchestrator script.
*   **ACTION:**
    *   CREATE a basic `proxysmart.conf` file listing your available modems and desired proxy configurations.
    *   RUN `proxysmart.sh start`.
    *   VERIFY that modems are initialized by checking logs and using helper `status` commands.
    *   VERIFY that proxy processes are running.
    *   VERIFY that networking rules are applied.
    *   TEST proxy connectivity via the full `proxysmart` setup.
    *   TEST `proxysmart.sh status`, `list_modems`, `list_proxies`.
    *   TEST `proxysmart.sh stop` and verify processes are terminated cleanly.
    *   TEST modem failure simulation (e.g., unplug/replug a modem) and observe if the orchestrator detects it and attempts recovery (requires adding monitoring/recovery logic to `proxysmart.sh`).

**STEP 8.5: Utility Testing**
*   **INSTRUCTION:** Test the utility scripts.
*   **ACTION:**
    *   TEST `run_cached` with a simple command and verify caching behavior.
    *   TEST `speedtest-cli` and `speedtest-curl` through a configured proxy/modem.
    *   GENERATE some test proxy logs and TEST `proxy_log_dump.sh` and `proxy_log_analyze.sh`.
    *   TEST `imgur.sh` with a test image file.
    *   TEST `openvpn_create_user` to generate a test client config.
    *   TEST `ussd.sh` with a common USSD code on a connected modem.
    *   TEST `2bars.py` with a connected modem.
    *   (If implemented beyond placeholder) TEST `osfooler-ng` and verify system changes.
    *   TEST `shell2http` by configuring it to expose a simple script and accessing it via `curl`.

**BUILD GUIDE END**

---

**AI Agent Notes:**

*   For each research subtask, prioritize official documentation, open-source project repositories (for tools like `mmcli`, `uqmi`, `uhubctl`, `gost`, `v2ray`, `shell2http`, `easy-rsa`), and reputable Linux modem guides/forums.
*   When implementing model-specific helpers (Phase 7), be aware that some modems may support multiple interaction methods (e.g., AT commands AND QMI). Choose the most robust or common method based on your research, or implement support for multiple if necessary.
*   Log all significant actions and errors within the scripts, especially in `proxysmart.sh` and `udevhook.sh`. Define a standard log file location and format.
*   Use clear variable names and add comments to the scripts, explaining complex logic, especially in `proxysmart.sh` and the helpers.
*   Store configuration files (like `proxysmart.conf`, `udev` rules, Gost/V2Ray configs) in standard locations (e.g., `/etc/proxysmart/` or within the project directory if not installing system-wide initially).

Proceed with **PHASE 0**. Report progress and ask for clarification if any instruction is genuinely ambiguous *after* attempting research.