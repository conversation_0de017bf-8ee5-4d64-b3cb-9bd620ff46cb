SMS Hub Protocol System: Architecture and Implementation Guide
Overview
This document provides comprehensive instructions for building an SMS Hub Protocol system from scratch. The system integrates multiple SMS sources (physical cellular modems and VoIP.ms API) into a unified platform that processes, stores, and forwards SMS messages to a central hub.

System Architecture
The SMS Hub Protocol system consists of the following core components:

Modem Management: Handles physical cellular modems connected via serial ports
VoIP.ms Integration: Connects to the VoIP.ms API to retrieve SMS messages
SMS Storage: Provides a unified database for storing messages from all sources
SMS Hub Integration: Forwards messages to a central SMS Hub API
Web Server: Provides HTTP endpoints for status and control
Dashboard GUI: Offers a visual interface for monitoring system status
Technology Stack
Language: Python 3.x
Database: SQLite
GUI Framework: Tkinter
Serial Communication: PySerial
HTTP Client: Requests
Web Server: Flask
Logging: Python's built-in logging module
Implementation Steps
1. Project Structure
Create the following directory structure:
smshub-protocol/
├── modems/
│   ├── __init__.py
│   ├── fibocom_modem.py
│   └── novatel_modem.py
├── storage/
│   ├── __init__.py
│   └── sms_storage.py
├── config.py
├── main.py
├── modem_manager.py
├── voipms_manager.py
├── smshub_api.py
├── smshub_server.py
├── simple_gui.py
└── standalone_dashboard.py

2. Configuration System
Create  config.py to manage system-wide configuration:
import os
import json
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default configuration
config = {
    "voipms": {
        "api_username": "",
        "api_password": "",
        "dids": []
    },
    "smshub": {
        "base_url": "https://agent.unerio.com/agent/api/sms",
        "api_key": ""
    },
    "server": {
        "host": "0.0.0.0",
        "port": 5000
    },
    "modem_scan_interval": 60,
    "voipms_check_interval": 60
}

# Load configuration from file if exists
def load_config(config_file="config.json"):
    global config
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                loaded_config = json.load(f)
                # Update config with loaded values
                for key, value in loaded_config.items():
                    if key in config:
                        if isinstance(value, dict) and isinstance(config[key], dict):
                            config[key].update(value)
                        else:
                            config[key] = value
            logger.info(f"Configuration loaded from {config_file}")
        else:
            logger.warning(f"Configuration file {config_file} not found, using defaults")
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")

# Save configuration to file
def save_config(config_file="config.json"):
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=4)
        logger.info(f"Configuration saved to {config_file}")
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")

# Initialize configuration
load_config()


3. SMS Storage Module
Create  storage/sms_storage.py to handle message storage:

import sqlite3
import logging
import time
import os

logger = logging.getLogger(__name__)

class SMSStorage:
    def __init__(self, db_path="sms_storage.db"):
        self.db_path = db_path
        self.conn = None
        self.initialize_db()
    
    def initialize_db(self):
        """Initialize the database with required tables."""
        try:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            cursor = self.conn.cursor()
            
            # Create SMS messages table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sms_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                source TEXT NOT NULL,
                source_id TEXT,
                from_number TEXT NOT NULL,
                to_number TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp REAL NOT NULL,
                processed INTEGER DEFAULT 0,
                forwarded INTEGER DEFAULT 0,
                error TEXT
            )
            ''')
            
            # Create index on source and source_id
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_source_source_id ON sms_messages(source, source_id)
            ''')
            
            # Create index on processed status
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_processed ON sms_messages(processed)
            ''')
            
            self.conn.commit()
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            if self.conn:
                self.conn.close()
            raise
    
    def store_message(self, source, from_number, to_number, message, source_id=None, timestamp=None):
        """Store a new SMS message in the database."""
        if timestamp is None:
            timestamp = time.time()
        
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
            INSERT INTO sms_messages (source, source_id, from_number, to_number, message, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (source, source_id, from_number, to_number, message, timestamp))
            self.conn.commit()
            message_id = cursor.lastrowid
            logger.info(f"Stored message from {from_number} to {to_number} (ID: {message_id})")
            return message_id
        except Exception as e:
            logger.error(f"Error storing message: {e}")
            return None
    
    def mark_as_processed(self, message_id, processed=True, error=None):
        """Mark a message as processed."""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
            UPDATE sms_messages SET processed = ?, error = ? WHERE id = ?
            ''', (1 if processed else 0, error, message_id))
            self.conn.commit()
            logger.info(f"Marked message {message_id} as {'processed' if processed else 'unprocessed'}")
            return True
        except Exception as e:
            logger.error(f"Error marking message as processed: {e}")
            return False
    
    def mark_as_forwarded(self, message_id, forwarded=True, error=None):
        """Mark a message as forwarded to SMS Hub."""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
            UPDATE sms_messages SET forwarded = ?, error = ? WHERE id = ?
            ''', (1 if forwarded else 0, error, message_id))
            self.conn.commit()
            logger.info(f"Marked message {message_id} as {'forwarded' if forwarded else 'not forwarded'}")
            return True
        except Exception as e:
            logger.error(f"Error marking message as forwarded: {e}")
            return False
    
    def get_unprocessed_messages(self, limit=100):
        """Get unprocessed messages."""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
            SELECT id, source, source_id, from_number, to_number, message, timestamp
            FROM sms_messages
            WHERE processed = 0
            ORDER BY timestamp ASC
            LIMIT ?
            ''', (limit,))
            messages = cursor.fetchall()
            return [
                {
                    'id': row[0],
                    'source': row[1],
                    'source_id': row[2],
                    'from_number': row[3],
                    'to_number': row[4],
                    'message': row[5],
                    'timestamp': row[6]
                }
                for row in messages
            ]
        except Exception as e:
            logger.error(f"Error getting unprocessed messages: {e}")
            return []
    
    def get_unforwarded_messages(self, limit=100):
        """Get messages not yet forwarded to SMS Hub."""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
            SELECT id, source, source_id, from_number, to_number, message, timestamp
            FROM sms_messages
            WHERE forwarded = 0 AND processed = 1
            ORDER BY timestamp ASC
            LIMIT ?
            ''', (limit,))
            messages = cursor.fetchall()
            return [
                {
                    'id': row[0],
                    'source': row[1],
                    'source_id': row[2],
                    'from_number': row[3],
                    'to_number': row[4],
                    'message': row[5],
                    'timestamp': row[6]
                }
                for row in messages
            ]
        except Exception as e:
            logger.error(f"Error getting unforwarded messages: {e}")
            return []
    
    def close(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")



4. Modem Implementation
4.1 Fibocom Modem Implementation
Create  modems/fibocom_modem.py:

import serial
import logging
import time
import sqlite3
import threading
import os
import re
from config import config
from smshub_api import SmsHubAPI, SmsHubConfig
from storage.sms_storage import SMSStorage

logger = logging.getLogger(__name__)

class FibocomModem:
    """Class for handling Fibocom modems."""
    
    def __init__(self, port):
        """Initialize the modem with the specified port."""
        self.port = port
        self.ser = None
        self.running = False
        self.sms_thread = None
        self.conn = None
        self.storage = SMSStorage()
        self.smshub_config = SmsHubConfig(
            base_url=config['smshub']['base_url'],
            api_key=config['smshub']['api_key']
        )
        self.smshub_api = SmsHubAPI(self.smshub_config)
    
    def connect(self, max_retries=3, retry_delay=2):
        """
        Connect to the modem with retry mechanism.
        
        Args:
            max_retries: Maximum number of connection attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        baud_rate = 115200  # Common baud rate for modems
        timeout = 1  # 1-second timeout for reading

        # First, try to release the port if it might be in use
        self._release_port()
        
        # Try to connect with retries
        for attempt in range(max_retries):
            try:
                logger.info(f"Connecting to {self.port} (Attempt {attempt+1}/{max_retries})...")
                
                # Try to open the port with exclusive access
                self.ser = serial.Serial(
                    port=self.port,
                    baudrate=baud_rate,
                    bytesize=serial.EIGHTBITS,
                    parity=serial.PARITY_NONE,
                    stopbits=serial.STOPBITS_ONE,
                    timeout=timeout,
                    xonxoff=False,  # Disable software flow control
                    rtscts=False,   # Disable hardware flow control
                    exclusive=True  # Try to get exclusive access to the port
                )
                
                # Check if connection is successful
                if self.ser.is_open:
                    logger.info(f"Successfully connected to {self.ser.port}")
                    
                    # Test the connection by sending a simple AT command
                    try:
                        self.ser.write(b'AT\r\n')
                        response = self.ser.read(128)
                        logger.info(f"AT command response: {response.decode(errors='ignore').strip()}")
                        
                        # If we got a response, the connection is working
                        if b'OK' in response:
                            logger.info(f"Modem on {self.port} responded to AT command")
                            return True
                        else:
                            logger.warning(f"Modem on {self.port} did not respond properly to AT command")
                            # Continue anyway, as some modems might not respond to AT with OK
                            return True
                    except Exception as cmd_error:
                        logger.warning(f"Error testing connection on {self.port}: {cmd_error}")
                        # Continue anyway, as the port is open
                        return True
                else:
                    logger.warning(f"Serial port {self.port} opened but is_open is False")
                    self._close_port()
            
            except serial.SerialException as e:
                if "PermissionError" in str(e) or "Access is denied" in str(e):
                    logger.error(f"Permission error connecting to {self.port}: {e}")
                    logger.info(f"Port {self.port} may be in use by another process")
                elif "FileNotFoundError" in str(e):
                    logger.error(f"Port {self.port} not found: {e}")
                    # No point in retrying if the port doesn't exist
                    return False
                else:
                    logger.error(f"Error connecting to {self.port}: {e}")
                
                # Close the port if it was partially opened
                self._close_port()
                
                # If this is not the last attempt, wait before retrying
                if attempt < max_retries - 1:
                    logger.info(f"Retrying connection to {self.port} in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Failed to connect to {self.port} after {max_retries} attempts")
            
            except Exception as e:
                logger.error(f"Unexpected error connecting to {self.port}: {e}")
                self._close_port()
                
                # If this is not the last attempt, wait before retrying
                if attempt < max_retries - 1:
                    logger.info(f"Retrying connection to {self.port} in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Failed to connect to {self.port} after {max_retries} attempts")
        
        # If we get here, all connection attempts failed
        return False
        
    def _release_port(self):
        """Attempt to release the port if it might be in use."""
        try:
            # Try to close the port if it's already open
            self._close_port()
            
            # On Windows, sometimes we need to open and close the port to release it
            try:
                temp_ser = serial.Serial(self.port, 9600, timeout=0.1)
                temp_ser.close()
                logger.info(f"Successfully released port {self.port}")
                time.sleep(0.5)  # Give the OS time to fully release the port
            except Exception:
                # Ignore errors here, as we're just trying to release the port
                pass
        except Exception as e:
            logger.debug(f"Error releasing port {self.port}: {e}")
    
    def _close_port(self):
        """Safely close the serial port."""
        try:
            if self.ser:
                if self.ser.is_open:
                    self.ser.close()
                    logger.info(f"Closed serial port {self.port}")
                self.ser = None
        except Exception as e:
            logger.debug(f"Error closing port {self.port}: {e}")
            self.ser = None
    
    def setup_modem(self, max_retries=3):
        """
        Set up modem for SMS reception with retry mechanism.
        
        Args:
            max_retries: Maximum number of retries for each command
            
        Returns:
            bool: True if setup was successful, False otherwise
        """
        if not self.ser or not self.ser.is_open:
            logger.error(f"Cannot set up modem on {self.port}: Serial port not open")
            return False
            
        setup_commands = [
            b'AT\r\n',          # Reset modem
            b'ATE1V1\r\n',      # Enable echo
            b'AT+CMGF=1\r\n',   # Set text mode
            b'AT+CNMI=2,2,0,0,0\r\n'  # Configure message forwarding to terminal
        ]

        success = True
        for command in setup_commands:
            command_success = False
            
            for attempt in range(max_retries):
                try:
                    # Clear any pending data
                    if self.ser.in_waiting > 0:
                        self.ser.read(self.ser.in_waiting)
                        
                    # Send command
                    self.ser.write(command)
                    
                    # Read response with timeout
                    start_time = time.time()
                    response = b''
                    while time.time() - start_time < 2:  # 2-second timeout
                        if self.ser.in_waiting > 0:
                            chunk = self.ser.read(self.ser.in_waiting)
                            response += chunk
                            if b'OK' in response or b'ERROR' in response:
                                break
                        time.sleep(0.1)
                    
                    # Log response
                    logger.info(f"Command: {command.decode(errors='ignore').strip()}, Response: {response.decode(errors='ignore').strip()}")
                    
                    # Check if command was successful
                    if b'OK' in response:
                        command_success = True
                        break
                    elif b'ERROR' in response:
                        logger.warning(f"Command {command.decode(errors='ignore').strip()} returned ERROR")
                        # Some modems might not support all commands, so continue anyway
                        command_success = True
                        break
                    else:
                        logger.warning(f"No OK/ERROR response for command {command.decode(errors='ignore').strip()}")
                        # Try again if this is not the last attempt
                        if attempt < max_retries - 1:
                            logger.info(f"Retrying command {command.decode(errors='ignore').strip()}...")
                            time.sleep(0.5)
                        
                except Exception as e:
                    logger.error(f"Error sending command {command.decode(errors='ignore').strip()}: {e}")
                    if attempt < max_retries - 1:
                        logger.info(f"Retrying command {command.decode(errors='ignore').strip()}...")
                        time.sleep(0.5)
            
            # Update overall success status
            success = success and command_success
        
        if success:
            logger.info(f"Modem on {self.port} successfully set up for SMS reception")
        else:
            logger.warning(f"Modem on {self.port} setup completed with some errors")
            
        return success
    
    def start_sms_monitoring(self):
        """Start monitoring for incoming SMS messages."""
        if not self.ser or not self.ser.is_open:
            logger.error(f"Cannot start SMS monitoring on {self.port}: Serial port not open")
            return False
        
        logger.info(f"Modem on port {self.port} set up for SMS reception")
        logger.info(f"Listening for incoming messages on {self.port}...")
        
        # Start SMS monitoring thread
        self.running = True
        self.sms_thread = threading.Thread(target=self._sms_monitor_thread)
        self.sms_thread.daemon = True
        self.sms_thread.start()
        logger.info(f"SMS monitoring thread started for {self.port}")
        return True
    
    def _sms_monitor_thread(self):
        """Thread function to monitor for incoming SMS messages."""
        while self.running:
            try:
                if self.ser and self.ser.is_open and self.ser.in_waiting > 0:
                    # Read incoming data
                    data = self.ser.read(self.ser.in_waiting).decode(errors='ignore')
                    
                    # Check for incoming SMS notification
                    if '+CMT:' in data:
                        logger.info(f"Received SMS notification on {self.port}: {data}")
                        self._process_incoming_sms(data)
            except Exception as e:
                logger.error(f"Error in SMS monitoring thread for {self.port}: {e}")
            
            # Sleep briefly to avoid high CPU usage
            time.sleep(0.1)
    
    def _process_incoming_sms(self, data):
        """Process an incoming SMS message."""
        try:
            # Parse the SMS notification
            # Format: +CMT: "+1234567890",,"YYYY/MM/DD,HH:MM:SS+TZ"
            # Message text follows on next lines
            
            lines = data.strip().split('\n')
            header = None
            message = ""
            
            for i, line in enumerate(lines):
                if '+CMT:' in line:
                    header = line
                    # Message text starts from the next line
                    message = '\n'.join(lines[i+1:]).strip()
                    break
            
            if not header:
                logger.warning(f"Could not find SMS header in data: {data}")
                return
            
            # Extract sender phone number
            # +CMT: "+1234567890",,"YYYY/MM/DD,HH:MM:SS+TZ"
            header_parts = header.split(',')
            if len(header_parts) < 1:
                logger.warning(f"Invalid SMS header format: {header}")
                return
            
            # Extract phone number from first part
            phone_part = header_parts[0].split(':')[1].strip()
            from_number = phone_part.strip('"+ ')
            
            # Get the modem's phone number (if available)
            to_number = self._get_modem_phone_number()
            if not to_number:
                to_number = self.port  # Use port as fallback
            
            # Store the message
            message_id = self.storage.store_message(
                source="modem",
                from_number=from_number,
                to_number=to_number,
                message=message,
                source_id=self.port
            )
            
            if message_id:
                logger.info(f"Stored SMS from {from_number} to {to_number} with ID {message_id}")
                
                # Mark as processed
                self.storage.mark_as_processed(message_id)
                
                # Forward to SMS Hub
                self._forward_to_smshub(message_id, from_number, to_number, message)
            
        except Exception as e:
            logger.error(f"Error processing incoming SMS on {self.port}: {e}")
    
    def _get_modem_phone_number(self):
        """Get the phone number of the modem."""
        try:
            if not self.ser or not self.ser.is_open:
                return None
            
            # Clear any pending data
            if self.ser.in_waiting > 0:
                self.ser.read(self.ser.in_waiting)
            
            # Send command to get SIM phone number
            self.ser.write(b'AT+CNUM\r\n')
            
            # Read response with timeout
            start_time = time.time()
            response = b''
            while time.time() - start_time < 2:  # 2-second timeout
                if self.ser.in_waiting > 0:
                    chunk = self.ser.read(self.ser.in_waiting)
                    response += chunk
                    if b'OK' in response or b'ERROR' in response:
                        break
                time.sleep(0.1)
            
            response_str = response.decode(errors='ignore')
            
            # Parse response to extract phone number
            # Format: +CNUM: "","<phone_number>",<type>
            if '+CNUM:' in response_str:
                for line in response_str.split('\n'):
                    if '+CNUM:' in line:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            phone = parts[1].strip('"')
                            return phone
            
            return None
        except Exception as e:
            logger.error(f"Error getting modem phone number: {e}")
            return None
    
    def _forward_to_smshub(self, message_id, from_number, to_number, message):
        """Forward a message to the SMS Hub."""
        try:
            # Prepare message data
            message_data = {
                "from": from_number,
                "to": to_number,
                "message": message,
                "source": f"modem:{self.port}"
            }
            
            # Send to SMS Hub
            success = self.smshub_api.push_sms(
                from_number=from_number,
                to_number=to_number,
                message=message,
                source=f"modem:{self.port}"
            )
            
            if success:
                logger.info(f"Successfully forwarded message {message_id} to SMS Hub")
                self.storage.mark_as_forwarded(message_id, True)
            else:
                logger.error(f"Failed to forward message {message_id} to SMS Hub")
                self.storage.mark_as_forwarded(message_id, False, "SMS Hub API error")
            
            return success
        except Exception as e:
            logger.error(f"Error forwarding message to SMS Hub: {e}")
            self.storage.mark_as_forwarded(message_id, False, str(e))
            return False
    
    def send_sms(self, to_number, message):
        """Send an SMS message."""
        if not self.ser or not self.ser.is_open:
            logger.error(f"Cannot send SMS from {self.port}: Serial port not open")
            return False
        
        try:
            # Clear any pending data
            if self.ser.in_waiting > 0:
                self.ser.read(self.ser.in_waiting)
            
            # Set SMS text mode
            self.ser.write(b'AT+CMGF=1\r\n')
            time.sleep(0.5)
            
            # Send command to send SMS
            cmd = f'AT+CMGS="{to_number}"\r'
            self.ser.write(cmd.encode())
            time.sleep(0.5)
            
            # Send message content followed by Ctrl+Z (ASCII 26)
            self.ser.write(message.encode() + b'\x1A')
            
            # Read response with timeout
            start_time = time.time()
            response = b''
            while time.time() - start_time < 10:  # 10-second timeout for sending
                if self.ser.in_waiting > 0:
                    chunk = self.ser.read(self.ser.in_waiting)
                    response += chunk
                    if b'OK' in response or b'ERROR' in response:
                        break
                time.sleep(0.1)
            
            response_str = response.decode(errors='ignore')
            
            if 'OK' in response_str:
                logger.info(f"Successfully sent SMS to {to_number} from {self.port}")
                return True
            else:
                logger.error(f"Failed to send SMS to {to_number} from {self.port}: {response_str}")
                return False
            
        except Exception as e:
            logger.error(f"Error sending SMS from {self.port}: {e}")
            return False
    
    def get_signal_quality(self):
        """Get the signal quality of the modem."""
        try:
            if not self.ser or not self.ser.is_open:
                return "Unknown"
            
            # Clear any pending data
            if self.ser.in_waiting > 0:
                self.ser.read(self.ser.in_waiting)
            
            # Send command to get signal quality
            self.ser.write(b'AT+CSQ\r\n')
            
            # Read response with timeout
            start_time = time.time()
            response = b''
            while time.time() - start_time < 2:  # 2-second timeout
                if self.ser.in_waiting > 0:
                    chunk = self.ser.read(self.ser.in_waiting)
                    response += chunk
                    if b'OK' in response or b'ERROR' in response:
                        break
                time.sleep(0.1)
            
            return response.decode(errors='ignore')
        except Exception as e:
            logger.error(f"Error getting signal quality: {e}")
            return "Error"
    
    def get_network_registration_status(self):
        """Get the network registration status of the modem."""
        try:
            if not self.ser or not self.ser.is_open:
                return "Unknown"
            
            # Clear any pending data
            if self.ser.in_waiting > 0:
                self.ser.read(self.ser.in_waiting)
            
            # Send command to get network registration status
            self.ser.write(b'AT+CGREG?\r\n')
            
            # Read response with timeout
            start_time = time.time()
            response = b''
            while time.time() - start_time < 2:  # 2-second timeout
                if self.ser.in_waiting > 0:
                    chunk = self.ser.read(self.ser.in_waiting)
                    response += chunk
                    if b'OK' in response or b'ERROR' in response:
                        break
                time.sleep(0.1)
            
            return response.decode(errors='ignore')
        except Exception as e:
            logger.error(f"Error getting network registration status: {e}")
            return "Error"
    
    def get_sim_status(self):
        """Get the SIM card status of the modem."""
        try:
            if not self.ser or not self.ser.is_open:
                return "Unknown"
            
            # Clear any pending data
            if self.ser.in_waiting > 0:
                self.ser.read(self.ser.in_waiting)
            
            # Send command to get SIM status
            self.ser.write(b'AT+CPIN?\r\n')
            
            # Read response with timeout
            start_time = time.time()
            response = b''
            while time.time() - start_time < 2:  # 2-second timeout
                if self.ser.in_waiting > 0:
                    chunk = self.ser.read(self.ser.in_waiting)
                    response += chunk
                    if b'OK' in response or b'ERROR' in response:
                        break
                time.sleep(0.1)
            
            return response.decode(errors='ignore')
        except Exception as e:
            logger.error(f"Error getting SIM status: {e}")
            return "Error"
    
    def identify_modem(self):
        """Get identification information from the modem."""
        modem_info = {}
        
        commands = {
            'AT+CGMI': 'Manufacturer',
            'AT+CGMM': 'Model',
            'AT+CGSN': 'Serial Number (IMEI)'
        }
        
        try:
            if not self.ser or not self.ser.is_open:
                return modem_info
            
            for cmd, desc in commands.items():
                try:
                    # Clear any pending data
                    if self.ser.in_waiting > 0:
                        self.ser.read(self.ser.in_waiting)
                    
                    # Send command
                    self.ser.write(f"{cmd}\r\n".encode())
                    
                    # Read response with timeout
                    start_time = time.time()
                    response = b''
                    while time.time() - start_time < 2:  # 2-second timeout
                        if self.ser.in_waiting > 0:
                            chunk = self.ser.read(self.ser.in_waiting)
                            response += chunk
                            if b'OK' in response or b'ERROR' in response:
                                break
                        time.sleep(0.1)
                    
                    modem_info[cmd] = response.decode(errors='ignore')
                except Exception as e:
                    logger.error(f"Error getting {desc}: {e}")
                    modem_info[cmd] = f"Error: {e}"
            
            return modem_info
        except Exception as e:
            logger.error(f"Error identifying modem: {e}")
            return modem_info
    
    def get_info(self):
        """Get modem information in a dictionary format for the GUI."""
        try:
            # Initialize basic modem information
            modem_info = {
                'port': self.port,
                'status': 'unknown',
                'type': 'Fibocom Modem',
                'iccid': 'Unknown',
                'network_status': 'Unknown',
                'signal_quality': 'Unknown',
                'phone': 'Unknown',
                'carrier': 'Unknown',
                'manufacturer': 'Unknown',
                'model': 'Unknown',
                'imei': 'Unknown',
                'sim_status': 'Unknown',
                'last_seen': time.time()
            }
            
            # Check if modem is connected
            if not self.ser:
                modem_info['status'] = 'disconnected'
                return modem_info
                
            if not self.ser.is_open:
                modem_info['status'] = 'port_closed'
                return modem_info
                
            # Modem is connected, set status to active
            modem_info['status'] = 'active'
            
            # Try to get modem identification
            try:
                modem_identification = self.identify_modem()
                
                # Process model information
                model_response = modem_identification.get('AT+CGMM', '')
                if model_response:
                    # Extract model from response
                    if '+CGMM:' in model_response:
                        model_parts = model_response.split('+CGMM:')[1].strip()
                        if '"' in model_parts:
                            # Extract text between quotes
                            model_match = re.search(r'"([^"]+)"', model_parts)
                            if model_match:
                                modem_info['model'] = model_match.group(1)
                        else:
                            modem_info['model'] = model_parts.split('\n')[0].strip()
                    else:
                        modem_info['model'] = model_response.split('\n')[0].strip()
                
                # Process manufacturer information
                mfg_response = modem_identification.get('AT+CGMI', '')
                if mfg_response:
                    # Extract manufacturer from response
                    if '+CGMI:' in mfg_response:
                        mfg_parts = mfg_response.split('+CGMI:')[1].strip()
                        if '"' in mfg_parts:
                            # Extract text between quotes
                            mfg_match = re.search(r'"([^"]+)"', mfg_parts)
                            if mfg_match:
                                modem_info['manufacturer'] = mfg_match.group(1)
                        else:
                            modem_info['manufacturer'] = mfg_parts.split('\n')[0].strip()
                    else:
                        modem_info['manufacturer'] = mfg_response.split('\n')[0].strip()
                
                # Process IMEI information
                imei_response = modem_identification.get('AT+CGSN', '')
                if imei_response:
                    # Extract IMEI from response
                    if '+CGSN:' in imei_response:
                        imei_parts = imei_response.split('+CGSN:')[1].strip()
                        if '"' in imei_parts:
                            # Extract text between quotes
                            imei_match = re.search(r'"([^"]+)"', imei_parts)
                            if imei_match:
                                modem_info['imei'] = imei_match.group(1)
                        else:
                            modem_info['imei'] = imei_parts.split('\n')[0].strip()
                    else:
                        # Try to find a number that looks like an IMEI
                        imei_match = re.search(r'\d{15}', imei_response)
                        if imei_match:
                            modem_info['imei'] = imei_match.group(0)
                        else:
                            modem_info['imei'] = imei_response.split('\n')[0].strip()
                
                # Set modem type based on manufacturer and model
                if modem_info['manufacturer'] and modem_info['model']:
                    if 'fibocom' in modem_info['manufacturer'].lower():
                        modem_info['type'] = f"Fibocom {modem_info['model']}"
                    elif 'novatel' in modem_info['manufacturer'].lower():
                        modem_info['type'] = f"Novatel {modem_info['model']}"
                    else:
                        modem_info['type'] = f"{modem_info['manufacturer']} {modem_info['model']}"
                
            except Exception as e:
                logger.error(f"Error getting modem identification: {e}")

            # Try to get signal quality
            try:
                signal_response = self.get_signal_quality()
                if "+CSQ:" in signal_response:
                    # Parse signal quality (format: +CSQ: XX,YY)
                    signal_parts = signal_response.split("+CSQ:")[1].strip().split(",")
                    if len(signal_parts) > 0:
                        signal_value = int(signal_parts[0].strip())
                        # Convert to percentage (0-31 scale, where 31 is -51dBm or stronger)
                        if signal_value < 99:  # 99 means unknown
                            signal_percent = min(100, int((signal_value / 31) * 100))
                            modem_info['signal_quality'] = f"{signal_percent}%"
            except Exception as e:
                logger.error(f"Error getting signal quality: {e}")

            # Try to get network status
            try:
                network_response = self.get_network_registration_status()
                if "+CGREG:" in network_response:
                    # Parse network status (format: +CGREG: X,Y)
                    status_parts = network_response.split("+CGREG:")[1].strip().split(",")
                    if len(status_parts) > 1:
                        status_code = status_parts[1].strip()
                        status_map = {
                            "0": "Not Registered",
                            "1": "Registered (Home)",
                            "2": "Searching",
                            "3": "Registration Denied",
                            "4": "Unknown",
                            "5": "Registered (Roaming)"
                        }
                        modem_info['network_status'] = status_map.get(status_code, "Unknown")
                        if status_code in ["1", "5"]:
                            modem_info['carrier'] = "Home Network" if status_code == "1" else "Roaming"
            except Exception as e:
                logger.error(f"Error getting network status: {e}")

            # Try to get SIM status
            try:
                sim_response = self.get_sim_status()
                if "+CPIN: READY" in sim_response:
                    modem_info['sim_status'] = "Ready"
                elif "ERROR" in sim_response:
                    modem_info['sim_status'] = "No SIM"
                else:
                    modem_info['sim_status'] = "Unknown"
            except Exception as e:
                logger.error(f"Error getting SIM status: {e}")

            return modem_info

        except Exception as e:
            logger.error(f"Error getting modem info: {e}")
            return {
                'port': self.port,
                'status': 'error',
                'type': 'Fibocom Modem',
                'error': str(e),
                'last_seen': time.time()
            }
    
    def disconnect(self):
        """Disconnect from the modem and close serial connection."""
        # Use the safe port closing method
        self._close_port()
        
        # Close database connections
        try:
            if self.conn:
                self.conn.close()
                logger.info(f"Legacy database connection closed for {self.port}")
                self.conn = None
        except Exception as e:
            logger.error(f"Error closing database connection for {self.port}: {e}")
        
        logger.info(f"Modem on {self.port} disconnected")

    def close(self):
        """Alias for disconnect() for backward compatibility."""
        self.disconnect()


4.2 Novatel Modem Implementation
Create modems/novatel_modem.py:

import serial
import logging
import time
import threading
import re
from config import config
from smshub_api import SmsHubAPI, SmsHubConfig
from storage.sms_storage import SMSStorage

logger = logging.getLogger(__name__)

class NovatelModem:
    """Class for handling Novatel modems."""
    
    def __init__(self, port):
        """Initialize the modem with the specified port."""
        self.port = port
        self.ser = None
        self.running = False
        self.sms_thread = None
        self.storage = SMSStorage()
        self.smshub_config = SmsHubConfig(
            base_url=config['smshub']['base_url'],
            api_key=config['smshub']['api_key']
        )
        self.smshub_api = SmsHubAPI(self.smshub_config)
    
    # Most methods are similar to FibocomModem with minor differences
    # for Novatel-specific AT commands and responses
    
    def connect(self, max_retries=3, retry_delay=2):
        """Connect to the modem with retry mechanism."""
        # Implementation similar to FibocomModem.connect()
        # with Novatel-specific adjustments if needed
        
        baud_rate = 115200
        timeout = 1

        # First, try to release the port if it might be in use
        self._release_port()
        
        # Try to connect with retries
        for attempt in range(max_retries):
            try:
                logger.info(f"Connecting to {self.port} (Attempt {attempt+1}/{max_retries})...")
                
                self.ser = serial.Serial(
                    port=self.port,
                    baudrate=baud_rate,
                    bytesize=serial.EIGHTBITS,
                    parity=serial.PARITY_NONE,
                    stopbits=serial.STOPBITS_ONE,
                    timeout=timeout,
                    xonxoff=False,
                    rtscts=False,
                    exclusive=True
                )
                
                if self.ser.is_open:
                    logger.info(f"Successfully connected to {self.ser.port}")
                    
                    try:
                        self.ser.write(b'AT\r\n')
                        response = self.ser.read(128)
                        logger.info(f"AT command response: {response.decode(errors='ignore').strip()}")
                        
                        if b'OK' in response:
                            logger.info(f"Modem on {self.port} responded to AT command")
                            return True
                        else:
                            logger.warning(f"Modem on {self.port} did not respond properly to AT command")
                            return True
                    except Exception as cmd_error:
                        logger.warning(f"Error testing connection on {self.port}: {cmd_error}")
                        return True
                else:
                    logger.warning(f"Serial port {self.port} opened but is_open is False")
                    self._close_port()
            
            except Exception as e:
                logger.error(f"Error connecting to {self.port}: {e}")
                self._close_port()
                
                if attempt < max_retries - 1:
                    logger.info(f"Retrying connection to {self.port} in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Failed to connect to {self.port} after {max_retries} attempts")
        
        return False
    
    def _release_port(self):
        """Attempt to release the port if it might be in use."""
        # Implementation similar to FibocomModem._release_port()
        try:
            self._close_port()
            
            try:
                temp_ser = serial.Serial(self.port, 9600, timeout=0.1)
                temp_ser.close()
                logger.info(f"Successfully released port {self.port}")
                time.sleep(0.5)
            except Exception:
                pass
        except Exception as e:
            logger.debug(f"Error releasing port {self.port}: {e}")
    
    def _close_port(self):
        """Safely close the serial port."""
        # Implementation similar to FibocomModem._close_port()
        try:
            if self.ser:
                if self.ser.is_open:
                    self.ser.close()
                    logger.info(f"Closed serial port {self.port}")
                self.ser = None
        except Exception as e:
            logger.debug(f"Error closing port {self.port}: {e}")
            self.ser = None
    
    def setup_modem(self, max_retries=3):
        """Set up modem for SMS reception."""
        # Implementation similar to FibocomModem.setup_modem()
        # with Novatel-specific commands if needed
        
        if not self.ser or not self.ser.is_open:
            logger.error(f"Cannot set up modem on {self.port}: Serial port not open")
            return False
            
        setup_commands = [
            b'AT\r\n',
            b'ATE1V1\r\n',
            b'AT+CMGF=1\r\n',
            b'AT+CNMI=2,2,0,0,0\r\n'
        ]

        success = True
        for command in setup_commands:
            command_success = False
            
            for attempt in range(max_retries):
                try:
                    if self.ser.in_waiting > 0:
                        self.ser.read(self.ser.in_waiting)
                        
                    self.ser.write(command)
                    
                    start_time = time.time()
                    response = b''
                    while time.time() - start_time < 2:
                        if self.ser.in_waiting > 0:
                            chunk = self.ser.read(self.ser.in_waiting)
                            response += chunk
                            if b'OK' in response or b'ERROR' in response:
                                break
                        time.sleep(0.1)
                    
                    logger.info(f"Command: {command.decode(errors='ignore').strip()}, Response: {response.decode(errors='ignore').strip()}")
                    
                    if b'OK' in response:
                        command_success = True
                        break
                    elif b'ERROR' in response:
                        logger.warning(f"Command {command.decode(errors='ignore').strip()} returned ERROR")
                        command_success = True
                        break
                    else:
                        logger.warning(f"No OK/ERROR response for command {command.decode(errors='ignore').strip()}")
                        if attempt < max_retries - 1:
                            logger.info(f"Retrying command {command.decode(errors='ignore').strip()}...")
                            time.sleep(0.5)
                        
                except Exception as e:
                    logger.error(f"Error sending command {command.decode(errors='ignore').strip()}: {e}")
                    if attempt < max_retries - 1:
                        logger.info(f"Retrying command {command.decode(errors='ignore').strip()}...")
                        time.sleep(0.5)
            
            success = success and command_success
        
        if success:
            logger.info(f"Modem on {self.port} successfully set up for SMS reception")
        else:
            logger.warning(f"Modem on {self.port} setup completed with some errors")
            
        return success
    
    # Implement other methods similar to FibocomModem
    # with Novatel-specific adjustments as needed
    
    def get_info(self):
        """Get modem information in a dictionary format for the GUI."""
        # Implementation similar to FibocomModem.get_info()
        # with Novatel-specific adjustments
        
        modem_info = {
            'port': self.port,
            'status': 'unknown',
            'type': 'Novatel Modem',
            'iccid': 'Unknown',
            'network_status': 'Unknown',
            'signal_quality': 'Unknown',
            'phone': 'Unknown',
            'carrier': 'Unknown',
            'manufacturer': 'Novatel',
            'model': 'Unknown',
            'imei': 'Unknown',
            'sim_status': 'Unknown',
            'last_seen': time.time()
        }
        
        if not self.ser:
            modem_info['status'] = 'disconnected'
            return modem_info
            
        if not self.ser.is_open:
            modem_info['status'] = 'port_closed'
            return modem_info
            
        modem_info['status'] = 'active'
        
        # Get modem identification and other info
        # Similar to FibocomModem but with Novatel-specific parsing
        
        return modem_info

5. SMS Hub API Integration
Create  smshub_api.py:

import requests
import logging
import time
import json
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SmsHubConfig:
    base_url: str
    api_key: str

class SmsHubAPI:
    """Class for interacting with the SMS Hub API."""
    
    def __init__(self, config):
        """Initialize with the provided configuration."""
        self.config = config
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.config.api_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        logger.info(f"SMS Hub integration initialized with URL: {self.config.base_url}")
    
    def push_sms(self, from_number, to_number, message, source=None, timestamp=None):
        """
        Push an SMS message to the SMS Hub.
        
        Args:
            from_number: Sender phone number
            to_number: Recipient phone number
            message: SMS message content
            source: Source identifier (e.g., 'modem:COM3', 'voipms:4085551234')
            timestamp: Message timestamp (defaults to current time)
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not from_number or not to_number or not message:
            logger.error("Missing required parameters for push_sms")
            return False
        
        # Ensure phone numbers are in E.164 format
        from_number = self._format_phone_number(from_number)
        to_number = self._format_phone_number(to_number)
        
        if timestamp is None:
            timestamp = int(time.time())
        
        payload = {
            'from': from_number,
            'to': to_number,
            'message': message,
            'timestamp': timestamp
        }
        
        if source:
            payload['source'] = source
        
        try:
            url = f"{self.config.base_url}/push"
            
            # Set timeout to prevent hanging requests
            response = self.session.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('success'):
                        logger.info(f"Successfully pushed SMS from {from_number} to {to_number}")
                        return True
                    else:
                        logger.error(f"SMS Hub API returned error: {result.get('message', 'Unknown error')}")
                        return False
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON response from SMS Hub API: {response.text}")
                    return False
            else:
                logger.error(f"SMS Hub API returned status code {response.status_code}: {response.text}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"Error pushing SMS to SMS Hub: {e}")
            return False
    
    def _format_phone_number(self, phone_number):
        """Format phone number to E.164 format."""
        # Remove any non-digit characters
        digits_only = ''.join(filter(str.isdigit, str(phone_number)))
        
        # If it's a US/Canada number without country code, add +1
        if len(digits_only) == 10:
            return f"+1{digits_only}"
        
        # If it already has a country code (assuming it's at least 11 digits)
        if len(digits_only) >= 11:
            return f"+{digits_only}"
        
        # Return as is if we can't determine the format
        return phone_number
    
    def check_status(self):
        """Check the status of the SMS Hub API."""
        try:
            url = f"{self.config.base_url}/status"
            
            response = self.session.get(url, timeout=5)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    return result.get('status', 'unknown'), result.get('message', 'No message')
                except json.JSONDecodeError:
                    return 'error', 'Invalid JSON response'
            else:
                return 'error', f"Status code {response.status_code}"
                
        except requests.RequestException as e:
            return 'error', str(e)


6. VoIP.ms Integration
Create  voipms_manager.py:

import requests
import logging
import time
import threading
import json
from datetime import datetime, timedelta
from config import config
from storage.sms_storage import SMSStorage
from smshub_api import SmsHubAPI, SmsHubConfig

logger = logging.getLogger(__name__)

class VoIPmsManager:
    """Class for managing VoIP.ms SMS integration."""
    
    def __init__(self, api_username=None, api_password=None, dids=None):
        """Initialize with API credentials and DIDs to monitor."""
        self.api_username = api_username or config['voipms']['api_username']
        self.api_password = api_password or config['voipms']['api_password']
        self.dids = dids or config['voipms']['dids']
        self.base_url = "https://voip.ms/api/v1/rest.php"
        self.check_interval = config.get('voipms_check_interval', 60)  # seconds
        self.running = False
        self.check_thread = None
        self.last_check_time = {}  # Store last check time for each DID
        self.storage = SMSStorage()
        self.smshub_config = SmsHubConfig(
            base_url=config['smshub']['base_url'],
            api_key=config['smshub']['api_key']
        )
        self.smshub_api = SmsHubAPI(self.smshub_config)
        
        logger.info(f"Using VoIP.ms API with username: {self.api_username}")
        logger.info(f"Monitoring DIDs: {self.api_username if not self.dids else self.dids}")
    
    def start(self):
        """Start the VoIP.ms SMS check thread."""
        if self.running:
            logger.warning("VoIP.ms SMS check thread is already running")
            return
        
        # Initialize last check time for each DID
        now = datetime.now()
        for did in self.dids:
            self.last_check_time[did] = now - timedelta(minutes=5)  # Start by checking last 5 minutes
        
        # If no DIDs specified, try to fetch them from the API
        if not self.dids:
            self.fetch_dids()
        
        # Start the check thread
        self.running = True
        self.check_thread = threading.Thread(target=self._check_loop)
        self.check_thread.daemon = True
        self.check_thread.start()
        logger.info("VoIP.ms SMS check thread started successfully")
    
    def fetch_dids(self):
        """Fetch DIDs from VoIP.ms API."""
        logger.info("Attempting to fetch all DIDs from API...")
        logger.info("Attempting to fetch all DIDs from VoIP.ms API - Initiating getDIDsInfo API call...")
        
        try:
            # Make API request to get DIDs
            params = {
                'api_username': self.api_username,
                'api_password': self.api_password,
                'method': 'getDIDsInfo',
                'content_type': 'json'
            }
            
            logger.info("Making getDIDsInfo API request with empty parameters")
            
            response = self._make_api_request(params)
            
            if response and response.get('status') == 'success':
                dids_data = response.get('dids', [])
                logger.info(f"Successfully received DIDs response with status: {response.get('status')}")
                logger.info(f"Processing {len(dids_data)} DIDs from API response")
                
                # Filter DIDs with SMS enabled
                sms_dids = []
                for did_info in dids_data:
                    did = did_info.get('did')
                    sms_available = did_info.get('sms_available') == '1'
                    sms_enabled = did_info.get('sms_enabled') == '1'
                    
                    if sms_available and sms_enabled and did:
                        sms_dids.append(did)
                        logger.info(f"Added DID {did} to monitoring list (SMS available and enabled)")
                
                if sms_dids:
                    self.dids = sms_dids
                    logger.info(f"Successfully processed {len(sms_dids)} DIDs with SMS enabled")
                    logger.info(f"Automatically fetched VoIP.ms DIDs to monitor: {sms_dids}")
                else:
                    logger.warning("No DIDs with SMS enabled found")
            else:
                logger.error(f"Failed to fetch DIDs: {response.get('status')}, {response.get('message')}")
        
        except Exception as e:
            logger.error(f"Error fetching DIDs: {e}")
    
    def _check_loop(self):
        """Main loop for checking for new SMS messages."""
        logger.info(f"Starting VoIP.ms SMS check loop (Interval: {self.check_interval}s)")
        
        while self.running:
            try:
                self.check_for_new_messages()
                
                # Calculate time to wait until next check
                wait_time = max(1, self.check_interval - (time.time() % self.check_interval))
                logger.info(f"Waiting {wait_time:.1f} seconds until next check")
                
                # Wait for the specified interval
                time.sleep(wait_time)
                
            except Exception as e:
                logger.error(f"Error in VoIP.ms SMS check loop: {e}")
                time.sleep(10)  # Wait a bit before retrying after an error
    
    def check_for_new_messages(self):
        """Check for new SMS messages for all monitored DIDs."""
        if not self.dids:
            logger.warning("No DIDs configured for monitoring")
            return
        
        now = datetime.now()
        
        for did in self.dids:
            try:
                last_check = self.last_check_time.get(did, now - timedelta(minutes=5))
                logger.info(f"Checking for new VoIP.ms SMS messages since {last_check}...")
                
                # Format date for API request
                from_date = last_check.strftime('%Y-%m-%d')
                to_date = now.strftime('%Y-%m-%d')
                
                logger.info(f"Fetching messages for DID: {did}")
                
                # Make API request to get SMS messages
                params = {
                    'api_username': self.api_username,
                    'api_password': self.api_password,
                    'method': 'getSMS',
                    'content_type': 'json',
                    'from': from_date,
                    'to': to_date,
                    'type': '3',  # All messages (sent and received)
                    'did': did
                }
                
                response = self._make_api_request(params)
                
                if response:
                    if response.get('status') == 'success':
                        messages = response.get('sms', [])
                        self._process_sms_messages(messages, did)
                    elif response.get('status') == 'no_sms':
                        logger.info(f"No new messages for DID {did}")
                    elif response.get('status') == 'invalid_type':
                        # Some VoIP.ms API versions don't support type=3, try with type=1 (received only)
                        logger.error(f"Invalid type parameter. API message: {response.get('message')}")
                        logger.info("Retrying with type='1' (received messages only)")
                        
                        params['type'] = '1'
                        response = self._make_api_request(params)
                        
                        if response and response.get('status') == 'success':
                            messages = response.get('sms', [])
                            self._process_sms_messages(messages, did)
                        elif response and response.get('status') == 'no_sms':
                            logger.info(f"No new messages for DID {did}")
                        else:
                            logger.error(f"Error checking SMS for DID {did}: {response}")
                    else:
                        logger.error(f"Error checking SMS for DID {did}: {response}")
                
                # Update last check time
                self.last_check_time[did] = now
                
            except Exception as e:
                logger.error(f"Error checking SMS for DID {did}: {e}")
    
    def _process_sms_messages(self, messages, did):
        """Process SMS messages from VoIP.ms API response."""
        if not messages:
            return
        
        logger.info(f"Processing {len(messages)} messages for DID {did}")
        
        for msg in messages:
            try:
                # Extract message details
                message_id = msg.get('id')
                date = msg.get('date')
                contact = msg.get('contact')
                message = msg.get('message')
                type_code = msg.get('type')
                
                # Skip if missing required fields
                if not all([message_id, contact, message]):
                    logger.warning(f"Skipping message with missing fields: {msg}")
                    continue
                
                # Determine direction (inbound/outbound)
                is_inbound = type_code == '1'
                
                # Set from/to based on direction
                from_number = contact if is_inbound else did
                to_number = did if is_inbound else contact
                
                # Check if message already exists in storage
                # (This would require a method to check by source_id, not implemented here)
                
                # Store message in database
                stored_id = self.storage.store_message(
                    source="voipms",
                    source_id=message_id,
                    from_number=from_number,
                    to_number=to_number,
                    message=message,
                    timestamp=self._parse_date(date) if date else time.time()
                )
                
                if stored_id:
                    logger.info(f"Stored VoIP.ms message {message_id} as ID {stored_id}")
                    
                    # Mark as processed
                    self.storage.mark_as_processed(stored_id)
                    
                    # Forward to SMS Hub
                    success = self.smshub_api.push_sms(
                        from_number=from_number,
                        to_number=to_number,
                        message=message,
                        source=f"voipms:{did}",
                        timestamp=self._parse_date(date) if date else None
                    )
                    
                    if success:
                        logger.info(f"Successfully forwarded VoIP.ms message {message_id} to SMS Hub")
                        self.storage.mark_as_forwarded(stored_id, True)
                    else:
                        logger.error(f"Failed to forward VoIP.ms message {message_id} to SMS Hub")
                        self.storage.mark_as_forwarded(stored_id, False, "SMS Hub API error")
                
            except Exception as e:
                logger.error(f"Error processing VoIP.ms message: {e}")
    
    def _make_api_request(self, params):
        """Make a request to the VoIP.ms API."""
        try:
            # Log the API request (with sensitive info masked)
            log_params = params.copy()
            if 'api_password' in log_params:
                log_params['api_password'] = '***'
            if 'api_username' in log_params:
                log_params['api_username'] = '***'
            logger.info(f"Making VoIP.ms API request - Method: {params.get('method')}, Parameters: {log_params}")
            
            # Make the request
            response = requests.get(self.base_url, params=params, timeout=10)
            
            # Parse the response
            try:
                result = response.json()
                
                # Check for API errors
                if result.get('status') in ['invalid_credentials', 'invalid_parameters', 'error']:
                    logger.error(f"VoIP.ms API error: Status: {result.get('status')}, Message: {result.get('message')}, Content: {response.content}")
                    return result
                
                # Log success for no_sms status
                if result.get('status') == 'no_sms':
                    logger.info("VoIP.ms API returned 'no_sms' status")
                
                return result
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON response from VoIP.ms API: {response.text}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"Error making VoIP.ms API request: {e}")
            return None
    
    def _parse_date(self, date_str):
        """Parse date string from VoIP.ms API to timestamp."""
        try:
            # VoIP.ms date format: YYYY-MM-DD HH:MM:SS
            dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            return dt.timestamp()
        except Exception as e:
            logger.error(f"Error parsing date '{date_str}': {e}")
            return time.time()
    
    def send_sms(self, did, dst, message):
        """Send an SMS message through VoIP.ms API."""
        if not did or not dst or not message:
            logger.error("Missing required parameters for send_sms")
            return False
        
        try:
            # Make API request to send SMS
            params = {
                'api_username': self.api_username,
                'api_password': self.api_password,
                'method': 'sendSMS',
                'content_type': 'json',
                'did': did,
                'dst': dst,
                'message': message
            }
            
            response = self._make_api_request(params)
            
            if response and response.get('status') == 'success':
                sms_id = response.get('sms')
                logger.info(f"Successfully sent SMS to {dst} from DID {did} (ID: {sms_id})")
                return True
            else:
                logger.error(f"Failed to send SMS: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending SMS: {e}")
            return False
    
    def stop(self):
        """Stop the VoIP.ms SMS check thread."""
        self.running = False
        logger.info("VoIP.ms SMS check thread stopped")

7. Modem Manager
Create  modem_manager.py:

import serial.tools.list_ports
import logging
import time
import threading
from config import config
from modems.fibocom_modem import FibocomModem
from modems.novatel_modem import NovatelModem

logger = logging.getLogger(__name__)

class ModemManager:
    """Class for managing multiple modems."""
    
    def __init__(self):
        """Initialize the modem manager."""
        self.modems = {}  # Dictionary of port -> modem object
        self.connected_modems = set()  # Set of connected modem ports
        self.running = False
        self.scan_thread = None
        self.scan_interval = config.get('modem_scan_interval', 60)  # seconds
    
    def start(self):
        """Start the modem scanning thread."""
        if self.running:
            logger.warning("Modem Manager is already running")
            return
        
        logger.info("Starting Modem Manager...")
        self.running = True
        self.scan_thread = threading.Thread(target=self._scan_loop)
        self.scan_thread.daemon = True
        self.scan_thread.start()
        logger.info("Modem Manager is running and scanning for modems.")
    
    def _scan_loop(self):
        """Main loop for scanning for modems."""
        while self.running:
            try:
                self.scan_for_modems()
                time.sleep(self.scan_interval)
            except Exception as e:
                logger.error(f"Error in modem scan loop: {e}")
                time.sleep(10)  # Wait a bit before retrying after an error
    
    def scan_for_modems(self):
        """Scan for available modems and initialize them."""
        logger.info("Scanning for modems...")
        
        # Get list of available serial ports
        available_ports = list(serial.tools.list_ports.comports())
        logger.info(f"Available ports: {available_ports}")
        
        # Track newly found modems
        new_modems = []
        
        # Check each port
        for port_info in available_ports:
            port = port_info.device
            
            # Log port details
            logger.info(f"Found port: {port} - Description: {port_info.description} - VID: {port_info.vid}, PID: {port_info.pid}")
            
            # Skip if already connected
            if port in self.modems:
                continue
            
            # Determine modem type based on description or VID/PID
            if "Fibocom" in port_info.description or port_info.vid == 32903:  # 0x8087
                logger.info(f"Initializing Fibocom modem on port {port}")
                modem = FibocomModem(port)
                if modem.connect():
                    self.modems[port] = modem
                    self.connected_modems.add(port)
                    new_modems.append(port)
                    logger.info(f"Fibocom modem on {port} initialized and listening for SMS.")
                else:
                    logger.error(f"Failed to connect to Fibocom modem on {port}")
            
            elif "Novatel" in port_info.description or port_info.vid == 5136:  # 0x1410
                logger.info(f"Initializing Novatel modem on port {port}")
                modem = NovatelModem(port)
                if modem.connect():
                    self.modems[port] = modem
                    self.connected_modems.add(port)
                    new_modems.append(port)
                    logger.info(f"Novatel modem on {port} initialized and listening for SMS.")
                else:
                    logger.error(f"Failed to connect to Novatel modem on {port}")
        
        # Set up and start SMS monitoring for new modems
        for port in new_modems:
            modem = self.modems[port]
            if modem.setup_modem():
                modem.start_sms_monitoring()
        
        logger.info(f"Modem scan completed. Connected modems: {list(self.connected_modems)}")
    
    def get_modem(self, port):
        """Get a modem by port."""
        return self.modems.get(port)
    
    def get_all_modems(self):
        """Get all connected modems."""
        return {port: modem for port, modem in self.modems.items() if port in self.connected_modems}
    
    def send_sms(self, port, to_number, message):
        """Send an SMS message through a specific modem."""
        modem = self.get_modem(port)
        if not modem:
            logger.error(f"Modem not found for port {port}")
            return False
        
        return modem.send_sms(to_number, message)
    
    def stop(self):
        """Stop the modem manager and disconnect all modems."""
        self.running = False
        
        # Disconnect all modems
        for port, modem in self.modems.items():
            try:
                modem.disconnect()
                logger.info(f"Disconnected modem on {port}")
            except Exception as e:
                logger.error(f"Error disconnecting modem on {port}: {e}")
        
        self.modems = {}
        self.connected_modems = set()
        logger.info("Modem Manager stopped")

8. Web Server
Create  smshub_server.py

from flask import Flask, request, jsonify
import logging
import threading
from config import config
from storage.sms_storage import SMSStorage

logger = logging.getLogger(__name__)

class SMSHubServer:
    """Flask server for SMS Hub API."""
    
    def __init__(self, modem_manager=None, voipms_manager=None):
        """Initialize the server with optional managers."""
        self.app = Flask('smshub_server')
        self.modem_manager = modem_manager
        self.voipms_manager = voipms_manager
        self.storage = SMSStorage()
        self.server_thread = None
        self.host = config['server']['host']
        self.port = config['server']['port']
        
        # Set up routes
        self._setup_routes()
    
    def _setup_routes(self):
        """Set up Flask routes."""
        
        @self.app.route('/status', methods=['GET'])
        def status():
            """Return server status."""
            status_info = {
                'status': 'running',
                'modems': len(self.modem_manager.connected_modems) if self.modem_manager else 0,
                'voipms_enabled': self.voipms_manager is not None,
                'server_version': '1.0.0'
            }
            return jsonify(status_info)
        
        @self.app.route('/modems', methods=['GET'])
        def list_modems():
            """List all connected modems."""
            if not self.modem_manager:
                return jsonify({'error': 'Modem manager not available'}), 503
            
            modems = []
            for port, modem in self.modem_manager.get_all_modems().items():
                modem_info = modem.get_info()
                modems.append(modem_info)
            
            return jsonify({'modems': modems})
        
        @self.app.route('/send', methods=['POST'])
        def send_sms():
            """Send an SMS message."""
            data = request.json
            
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            
            # Check required fields
            required_fields = ['to', 'message']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return jsonify({'error': f"Missing required fields: {', '.join(missing_fields)}"}), 400
            
            # Get parameters
            to_number = data['to']
            message = data['message']
            modem_port = data.get('modem')
            did = data.get('did')
            
            # Determine sending method
            if modem_port and self.modem_manager:
                # Send via modem
                success = self.modem_manager.send_sms(modem_port, to_number, message)
                if success:
                    return jsonify({'success': True, 'method': 'modem', 'modem': modem_port})
                else:
                    return jsonify({'error': 'Failed to send SMS via modem'}), 500
            
            elif did and self.voipms_manager:
                # Send via VoIP.ms
                success = self.voipms_manager.send_sms(did, to_number, message)
                if success:
                    return jsonify({'success': True, 'method': 'voipms', 'did': did})
                else:
                    return jsonify({'error': 'Failed to send SMS via VoIP.ms'}), 500
            
            else:
                # No valid sending method specified
                return jsonify({'error': 'No valid sending method specified (modem or did)'}), 400
    
    def start(self):
        """Start the Flask server in a separate thread."""
        logger.info(f"Starting server on http://{self.host}:{self.port}")
        
        def run_server():
            self.app.run(host=self.host, port=self.port)
        
        self.server_thread = threading.Thread(target=run_server)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        logger.info(f"Server started on http://{self.host}:{self.port}")
    
    def stop(self):
        """Stop the Flask server."""
        # Flask doesn't provide a clean way to stop the server from another thread
        # In a production environment, you would use a proper WSGI server like Gunicorn
        # that can be stopped gracefully
        logger.info("Server stop requested (note: Flask development server doesn't support clean shutdown)")

9. Simple GUI
Create  simple_gui.py:

import tkinter as tk
from tkinter import ttk
import logging
import threading
import time
import queue

logger = logging.getLogger(__name__)

class SimpleModemGUI:
    """Simple GUI for monitoring modems and SMS messages."""
    
    def __init__(self, modem_manager=None, voipms_manager=None):
        """Initialize the GUI with optional managers."""
        self.modem_manager = modem_manager
        self.voipms_manager = voipms_manager
        self.root = None
        self.modem_tree = None
        self.message_tree = None
        self.status_var = None
        self.update_interval = 1000  # ms
        self.message_queue = queue.Queue()
    
    def create_gui(self):
        """Create the GUI elements."""
        logger.info("Creating simplified GUI...")
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("SMS Hub Protocol")
        self.root.geometry("800x600")
        logger.info("Creating simplified GUI window...")
        
        # Create status bar
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="Status: Initializing...")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create Modems tab
        modems_frame = ttk.Frame(notebook)
        notebook.add(modems_frame, text="Modems")
        
        # Create modem controls
        modem_control_frame = ttk.Frame(modems_frame)
        modem_control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        scan_button = ttk.Button(modem_control_frame, text="Scan for Modems", command=self.scan_modems)
        scan_button.pack(side=tk.LEFT, padx=5)
        
        # Create modem treeview
        modem_tree_frame = ttk.Frame(modems_frame)
        modem_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.modem_tree = ttk.Treeview(modem_tree_frame, columns=("Port", "Status", "Type"), show="headings")
        self.modem_tree.heading("Port", text="Port")
        self

        self.modem_tree = ttk.Treeview(modem_tree_frame, columns=("Port", "Status", "Type"), show="headings")
        self.modem_tree.heading("Port", text="Port")
        self.modem_tree.heading("Status", text="Status")
        self.modem_tree.heading("Type", text="Type")
        self.modem_tree.column("Port", width=100)
        self.modem_tree.column("Status", width=100)
        self.modem_tree.column("Type", width=200)
        self.modem_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        modem_scrollbar = ttk.Scrollbar(modem_tree_frame, orient=tk.VERTICAL, command=self.modem_tree.yview)
        modem_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.modem_tree.configure(yscrollcommand=modem_scrollbar.set)
        
        # Create Messages tab
        messages_frame = ttk.Frame(notebook)
        notebook.add(messages_frame, text="Messages")
        
        # Create message controls
        message_control_frame = ttk.Frame(messages_frame)
        message_control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        refresh_button = ttk.Button(message_control_frame, text="Refresh Messages", command=self.refresh_messages)
        refresh_button.pack(side=tk.LEFT, padx=5)
        
        # Create message treeview
        message_tree_frame = ttk.Frame(messages_frame)
        message_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.message_tree = ttk.Treeview(message_tree_frame, 
                                         columns=("ID", "From", "To", "Message", "Time", "Source"),
                                         show="headings")
        self.message_tree.heading("ID", text="ID")
        self.message_tree.heading("From", text="From")
        self.message_tree.heading("To", text="To")
        self.message_tree.heading("Message", text="Message")
        self.message_tree.heading("Time", text="Time")
        self.message_tree.heading("Source", text="Source")
        self.message_tree.column("ID", width=50)
        self.message_tree.column("From", width=120)
        self.message_tree.column("To", width=120)
        self.message_tree.column("Message", width=200)
        self.message_tree.column("Time", width=120)
        self.message_tree.column("Source", width=100)
        self.message_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        message_scrollbar = ttk.Scrollbar(message_tree_frame, orient=tk.VERTICAL, command=self.message_tree.yview)
        message_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.message_tree.configure(yscrollcommand=message_scrollbar.set)
        
        # Set up periodic updates
        self.root.after(self.update_interval, self.update_gui)
        
        # Set up message queue checker
        def check_queue():
            try:
                while True:
                    message = self.message_queue.get_nowait()
                    self.add_message_to_tree(message)
                    self.message_queue.task_done()
            except queue.Empty:
                pass
            self.root.after(500, check_queue)
        
        # Start queue checker
        self.root.after(1000, check_queue)
    
    def update_modem_list(self):
        """Update the modem list display."""
        try:
            # Clear existing items
            for item in self.modem_tree.get_children():
                self.modem_tree.delete(item)
            
            # Get modems from the modem manager
            modems = self.modem_manager.modems
            connected_ports = self.modem_manager.connected_modems
            
            # Update status
            self.status_var.set(f"Status: {len(connected_ports)} modems connected")
            
            # Log the connected modems for debugging
            logger.info(f"Connected modems in GUI update: {list(connected_ports)}")
            
            # Add modems to tree
            for port, modem in modems.items():
                try:
                    # Get basic info
                    if hasattr(modem, 'get_info') and callable(modem.get_info):
                        # Log that we're getting info for this modem
                        logger.info(f"Getting info for modem on port {port}")
                        
                        # Get the modem info
                        info = modem.get_info()
                        
                        # Log the info we got
                        logger.info(f"Modem info for {port}: status={info.get('status', 'Unknown')}, type={info.get('type', 'Unknown')}")
                        
                        # Extract status and type
                        status = info.get('status', 'Unknown')
                        modem_type = info.get('type', 'Unknown')
                    else:
                        logger.warning(f"Modem on port {port} does not have get_info method")
                        status = "Unknown"
                        modem_type = "Unknown"
                    
                    # Insert into tree with appropriate status
                    if status == 'active':
                        self.modem_tree.insert('', 'end', values=(port, "Connected", modem_type), tags=('active',))
                    else:
                        self.modem_tree.insert('', 'end', values=(port, status.capitalize(), modem_type))
                    
                except Exception as e:
                    logger.error(f"Error getting info for modem {port}: {e}")
                    # Add with error status
                    self.modem_tree.insert('', 'end', values=(port, "Error", str(e)[:50]))
            
            # Configure tag colors
            self.modem_tree.tag_configure('active', foreground='green')
            
        except Exception as e:
            logger.error(f"Error updating modem list: {e}", exc_info=True)
    
    def scan_modems(self):
        """Trigger a modem scan."""
        try:
            if self.modem_manager:
                self.modem_manager.scan_for_modems()
                self.update_modem_list()
            else:
                logger.error("Modem manager not available")
        except Exception as e:
            logger.error(f"Error scanning for modems: {e}", exc_info=True)
    
    def refresh_messages(self):
        """Refresh the message list."""
        try:
            # Clear existing items
            for item in self.message_tree.get_children():
                self.message_tree.delete(item)
            
            # Get recent messages from storage
            storage = self.modem_manager.modems[list(self.modem_manager.modems.keys())[0]].storage if self.modem_manager and self.modem_manager.modems else None
            
            if storage:
                # Get both processed and unprocessed messages
                messages = storage.get_unprocessed_messages(100)
                
                # Add messages to tree
                for msg in messages:
                    self.add_message_to_tree(msg)
            else:
                logger.error("Storage not available")
        except Exception as e:
            logger.error(f"Error refreshing messages: {e}", exc_info=True)
    
    def add_message_to_tree(self, message):
        """Add a message to the message tree."""
        try:
            # Format timestamp
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(message['timestamp']))
            
            # Insert into tree
            self.message_tree.insert('', 0, values=(
                message['id'],
                message['from_number'],
                message['to_number'],
                message['message'][:50] + ('...' if len(message['message']) > 50 else ''),
                timestamp,
                message['source']
            ))
        except Exception as e:
            logger.error(f"Error adding message to tree: {e}", exc_info=True)
    
    def update_gui(self):
        """Update the GUI elements."""
        try:
            # Update modem list
            if self.modem_manager:
                self.update_modem_list()
            
            # Schedule next update
            self.root.after(self.update_interval, self.update_gui)
        except Exception as e:
            logger.error(f"Error updating GUI: {e}", exc_info=True)
    
    def run(self):
        """Run the GUI main loop."""
        logger.info("Simple GUI initialized successfully")
        logger.info("Simple GUI run method called")
        
        # Create GUI if not already created
        if not self.root:
            self.create_gui()
        
        logger.info("Simple GUI initialization complete")
        logger.info("Starting GUI main loop")
        
        # Start the main loop
        self.root.mainloop()
    
    def stop(self):
        """Stop the GUI."""
        if self.root:
            self.root.quit()
            logger.info("GUI stopped")

10. Main Application
Create  main.py:


import logging
import threading
import time
import sys
from config import config, logger
from modem_manager import ModemManager
from voipms_manager import VoIPmsManager
from smshub_server import SMSHubServer
from simple_gui import SimpleModemGUI
from smshub_api import SmsHubAPI, SmsHubConfig

def initialize_voipms():
    """Initialize the VoIP.ms manager."""
    try:
        # Create VoIP.ms manager
        voipms_manager = VoIPmsManager(
            api_username=config['voipms']['api_username'],
            api_password=config['voipms']['api_password'],
            dids=config['voipms']['dids']
        )
        
        # Start VoIP.ms manager
        voipms_manager.start()
        
        return voipms_manager
    except Exception as e:
        logger.error(f"Error initializing VoIP.ms manager: {e}")
        return None

def main():
    """Main application entry point."""
    try:
        # Set logging level from config
        log_level = config.get('log_level', 'INFO')
        logging.getLogger().setLevel(getattr(logging, log_level))
        logger.info(f"Logging level set to: {log_level}")
        
        # Initialize SMS Hub integration
        logger.info("Initializing SMS Hub integration...")
        smshub_config = SmsHubConfig(
            base_url=config['smshub']['base_url'],
            api_key=config['smshub']['api_key']
        )
        smshub_api = SmsHubAPI(smshub_config)
        
        # Start SMS Hub Agent server
        logger.info("Starting SMS Hub Agent server...")
        server = SMSHubServer()
        server.start()
        
        # Initialize modem manager
        logger.info("Initializing modem manager...")
        modem_manager = ModemManager()
        
        # Initialize VoIP.ms manager
        logger.info("Initializing VoIPms manager...")
        voipms_manager = initialize_voipms()
        
        # Update server with managers
        server.modem_manager = modem_manager
        server.voipms_manager = voipms_manager
        
        # Start modem scanning
        logger.info("Starting modem scanning...")
        modem_manager.start()
        
        # Start GUI
        logger.info("Starting GUI...")
        gui = SimpleModemGUI(modem_manager, voipms_manager)
        gui.run()
        
        # Keep the main thread alive
        while True:
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, shutting down...")
    except Exception as e:
        logger.error(f"Error in main application: {e}", exc_info=True)
    finally:
        # Clean up resources
        try:
            if 'modem_manager' in locals():
                modem_manager.stop()
            
            if 'voipms_manager' in locals() and voipms_manager:
                voipms_manager.stop()
            
            if 'server' in locals():
                server.stop()
            
            logger.info("Application shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

if __name__ == "__main__":
    main()

11. Standalone Dashboard
Create  standalone_dashboard.py:

import tkinter as tk
from tkinter import ttk
import logging
import threading
import time
import requests
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class StandaloneDashboard:
    """Standalone dashboard for monitoring SMS Hub Protocol system."""
    
    def __init__(self, server_url="http://localhost:5000"):
        """Initialize the dashboard with server URL."""
        self.server_url = server_url
        self.root = None
        self.modem_tree = None
        self.message_tree = None
        self.status_var = None
        self.update_interval = 5000  # ms
        self.session = requests.Session()
    
    def create_gui(self):
        """Create the GUI elements."""
        # Create main window
        self.root = tk.Tk()
        self.root.title("SMS Hub Protocol Dashboard")
        self.root.geometry("900x700")
        
        # Create status bar
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar(value="Status: Connecting to server...")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # Create server URL entry
        server_frame = ttk.Frame(self.root)
        server_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(server_frame, text="Server URL:").pack(side=tk.LEFT, padx=5)
        
        self.server_url_var = tk.StringVar(value=self.server_url)
        server_entry = ttk.Entry(server_frame, textvariable=self.server_url_var, width=40)
        server_entry.pack(side=tk.LEFT, padx=5)
        
        connect_button = ttk.Button(server_frame, text="Connect", command=self.connect_to_server)
        connect_button.pack(side=tk.LEFT, padx=5)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create Modems tab
        modems_frame = ttk.Frame(notebook)
        notebook.add(modems_frame, text="Modems")
        
        # Create modem treeview
        modem_tree_frame = ttk.Frame(modems_frame)
        modem_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.modem_tree = ttk.Treeview(modem_tree_frame, 
                                       columns=("Port", "Status", "Type", "Signal", "Network"),
                                       show="headings")
        self.modem_tree.heading("Port", text="Port")
        self.modem_tree.heading("Status", text="Status")
        self.modem_tree.heading("Type", text="Type")
        self.modem_tree.heading("Signal", text="Signal")
        self.modem_tree.heading("Network", text="Network")
        self.modem_tree.column("Port", width=80)
        self.modem_tree.column("Status", width=100)
        self.modem_tree.column("Type", width=200)
        self.modem_tree.column("Signal", width=80)
        self.modem_tree.column("Network", width=150)
        self.modem_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        modem_scrollbar = ttk.Scrollbar(modem_tree_frame, orient=tk.VERTICAL, command=self.modem_tree.yview)
        modem_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.modem_tree.configure(yscrollcommand=modem_scrollbar.set)
        
        # Create Send SMS tab
        send_frame = ttk.Frame(notebook)
        notebook.add(send_frame, text="Send SMS")
        
        # Create send SMS form
        form_frame = ttk.Frame(send_frame)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # To Number
        ttk.Label(form_frame, text="To Number:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.to_number_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.to_number_var, width=30).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Modem/DID selection
        ttk.Label(form_frame, text="Send via:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.send_via_var = tk.StringVar(value="modem")
        ttk.Radiobutton(form_frame, text="Modem", variable=self.send_via_var, value="modem").grid(row=1, column=1, sticky=tk.W)
        ttk.Radiobutton(form_frame, text="VoIP.ms", variable=self.send_via_var, value="voipms").grid(row=1, column=2, sticky=tk.W)
        
        # Modem/DID selection
        ttk.Label(form_frame, text="Modem/DID:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.modem_did_var = tk.StringVar()
        self.modem_did_combo = ttk.Combobox(form_frame, textvariable=self.modem_did_var, width=30)
        self.modem_did_combo.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Message
        ttk.Label(form_frame, text="Message:").grid(row=3, column=0, sticky=tk.NW, pady=5)
        self.message_text = tk.Text(form_frame, width=50, height=10)
        self.message_text.grid(row=3, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # Send button
        send_button = ttk.Button(form_frame, text="Send SMS", command=self.send_sms)
        send_button.grid(row=4, column=1, pady=10)
        
        # Status label
        self.send_status_var = tk.StringVar()
        ttk.Label(form_frame, textvariable=self.send_status_var).grid(row=5, column=0, columnspan=3, pady=5)
        
        # Set up periodic updates
        self.root.after(self.update_interval, self.update_gui)
    
    def connect_to_server(self):
        """Connect to the server and update status."""
        self.server_url = self.server_url_var.get().strip()
        self.update_status()
        self.update_modem_list()
        self.update_modem_did_combo()
    
    def update_status(self):
        """Update the server status display."""
        try:
            response = self.session.get(f"{self.server_url}/status", timeout=5)
            
            if response.status_code == 200:
                status_data = response.json()
                self.status_var.set(f"Status: Connected - Modems: {status_data.get('modems', 0)}, VoIP.ms: {'Enabled' if status_data.get('voipms_enabled') else 'Disabled'}")
            else:
                self.status_var.set(f"Status: Error - HTTP {response.status_code}")
        except Exception as e:
            self.status_var.set(f"Status: Connection error - {str(e)}")
    
    def update_modem_list(self):
        """Update the modem list display."""
        try:
            # Clear existing items
            for item in self.modem_tree.get_children():
                self.modem_tree.delete(item)
            
            # Get modems from server
            response = self.session.get(f"{self.server_url}/modems", timeout=5)
            
            if response.status_code == 200:
                modems_data = response.json().get('modems', [])
                
                #
                # Add modems to tree
                for modem in modems_data:
                    port = modem.get('port', 'Unknown')
                    status = modem.get('status', 'Unknown')
                    modem_type = modem.get('type', 'Unknown')
                    signal = modem.get('signal_quality', 'Unknown')
                    network = modem.get('network_status', 'Unknown')
                    
                    # Insert into tree with appropriate status
                    if status == 'active':
                        self.modem_tree.insert('', 'end', values=(port, "Connected", modem_type, signal, network), tags=('active',))
                    else:
                        self.modem_tree.insert('', 'end', values=(port, status.capitalize(), modem_type, signal, network))
                
                # Configure tag colors
                self.modem_tree.tag_configure('active', foreground='green')
            else:
                logger.error(f"Error getting modems: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"Error updating modem list: {e}")
    
    def update_modem_did_combo(self):
        """Update the modem/DID combo box."""
        try:
            # Get modems from server
            response = self.session.get(f"{self.server_url}/modems", timeout=5)
            
            if response.status_code == 200:
                modems_data = response.json().get('modems', [])
                
                # Get active modem ports
                modem_ports = [modem.get('port') for modem in modems_data if modem.get('status') == 'active']
                
                # Update combo box values
                self.modem_did_combo['values'] = modem_ports
                
                # Select first value if available
                if modem_ports and not self.modem_did_var.get():
                    self.modem_did_var.set(modem_ports[0])
            else:
                logger.error(f"Error getting modems: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"Error updating modem/DID combo: {e}")
    
    def send_sms(self):
        """Send an SMS message."""
        try:
            # Get form values
            to_number = self.to_number_var.get().strip()
            send_via = self.send_via_var.get()
            modem_did = self.modem_did_var.get()
            message = self.message_text.get("1.0", tk.END).strip()
            
            # Validate inputs
            if not to_number:
                self.send_status_var.set("Error: To number is required")
                return
            
            if not modem_did:
                self.send_status_var.set("Error: Modem/DID selection is required")
                return
            
            if not message:
                self.send_status_var.set("Error: Message is required")
                return
            
            # Prepare request data
            data = {
                'to': to_number,
                'message': message
            }
            
            if send_via == 'modem':
                data['modem'] = modem_did
            else:  # voipms
                data['did'] = modem_did
            
            # Send request
            response = self.session.post(f"{self.server_url}/send", json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.send_status_var.set(f"Success: Message sent via {result.get('method')}")
                    # Clear form
                    self.message_text.delete("1.0", tk.END)
                else:
                    self.send_status_var.set(f"Error: {result.get('error', 'Unknown error')}")
            else:
                self.send_status_var.set(f"Error: HTTP {response.status_code}")
        except Exception as e:
            self.send_status_var.set(f"Error: {str(e)}")
            logger.error(f"Error sending SMS: {e}")
    
    def update_gui(self):
        """Update the GUI elements."""
        try:
            # Update status
            self.update_status()
            
            # Update modem list
            self.update_modem_list()
            
            # Schedule next update
            self.root.after(self.update_interval, self.update_gui)
        except Exception as e:
            logger.error(f"Error updating GUI: {e}")
    
    def run(self):
        """Run the dashboard main loop."""
        # Create GUI if not already created
        if not self.root:
            self.create_gui()
        
        # Start the main loop
        self.root.mainloop()

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Create and run dashboard
    dashboard = StandaloneDashboard()
    dashboard.run()

12. Package Requirements
Create  requirements.txt:


pyserial>=3.5
requests>=2.27.1
flask>=2.0.1



This SMS Hub Protocol system provides a robust platform for integrating multiple SMS sources (physical cellular modems and VoIP.ms API) into a unified system. The system processes, stores, and forwards SMS messages to a central SMS Hub API, making it easy to manage SMS communications across different channels.

Key features of the system include:

Modular Architecture: The system is built with a modular architecture, making it easy to add support for new modem types or SMS sources.
Robust Error Handling: The system includes comprehensive error handling to ensure stability and reliability.
Unified Storage: All SMS messages, regardless of source, are stored in a unified SQLite database.
Web API: The system provides a RESTful API for monitoring and control.
User Interface: The system includes both a simple GUI for direct interaction and a standalone dashboard for remote monitoring.
Service Integration: The system can be deployed as a Windows service or Linux systemd service for production use.
By following this guide, you can build a complete SMS Hub Protocol system from scratch, customizing it to your specific needs and requirements.


