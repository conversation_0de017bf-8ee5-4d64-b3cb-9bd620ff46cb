
# AT Commands Cheat Sheet for 4G Modems

## Basic Modem Information
- `AT`: Test the modem is responsive.
- `ATI`: Display modem information (model, firmware version, etc.).
- `AT+CGMM`: Request the modem model.
- `AT+CGMI`: Request the modem manufacturer.
- `AT+CGMR`: Request the firmware version.
- `AT+CSQ`: Check signal quality.

## Network Registration and Configuration
- `AT+CREG?`: Check network registration status.
- `AT+COPS?`: Check the current network operator.
- `AT+COPS=<mode>,<format>,<operator>`: Manually set the network operator.
- `AT+CGREG?`: Check GPRS network registration status.
- `AT+CGDCONT=<cid>,<PDP_type>,<APN>`: Set PDP context (e.g., APN).

## SIM Card Commands
- `AT+CPIN?`: Check if the SIM card is inserted and ready.
- `AT+CPIN=<PIN>`: Enter the SIM PIN code.
- `AT+CSIM`: Perform a SIM card operation.

## SMS Commands
- `AT+CMGF=<mode>`: Set SMS mode (0: PDU, 1: Text).
- `AT+CMGS="<number>"`: Send an SMS message (use CTRL+Z to end input).
- `AT+CMGR=<index>`: Read an SMS message from storage.
- `AT+CMGD=<index>`: Delete an SMS message.
- `AT+CNMI=<mode>`: Set new SMS notification.

## Internet Connection
- `AT+CGACT?`: Check PDP context activation status.
- `AT+CGACT=<state>,<cid>`: Activate or deactivate a PDP context.
- `AT+CGATT?`: Check GPRS attach status.
- `AT+CGPADDR`: Show the IP address of the PDP context.

## Call Handling
- `ATD<number>;`: Dial a voice call to `<number>`.
- `ATH`: Hang up the call.
- `ATA`: Answer an incoming call.
- `AT+CLCC`: Check call status.

## GPS Commands (if supported)
- `AT+CGPSPWR=<state>`: Turn GPS power on or off.
- `AT+CGPSINF=<type>`: Get GPS information.
- `AT+CGPSRST`: Reset GPS.

## Modem Settings
- `AT&F`: Restore factory settings.
- `AT&W`: Save the current settings to memory.
- `AT+IPR=<rate>`: Set the baud rate.

## Debugging and Diagnostics
- `AT+CMEE=<mode>`: Enable or disable verbose error messages.
- `AT+GMI`: Display the manufacturer.
- `AT+GMM`: Display the model.
- `AT+GMR`: Display the revision information.
- `AT+GSN`: Display the IMEI number.

## Miscellaneous
- `AT+CFUN?`: Check the current functionality level.
- `AT+CFUN=<mode>`: Set the functionality level (e.g., full functionality, flight mode).
- `AT+CSCS="<character_set>"`: Set the character set.
- `AT+CGSN`: Display the IMEI of the modem.

This cheat sheet provides a quick reference to essential AT commands for 4G modems, enabling configuration, testing, and debugging of modem operations.
