#!/usr/bin/env python3
"""
Enhanced SMS Hub Application Launcher
This script properly handles imports and starts the enhanced SMS Hub application.
"""

import sys
import os
import logging

# Add the app directory to Python path
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Set up basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """Main application entry point."""
    try:
        logger.info("Starting Enhanced SMS Hub Application...")
        
        # Import and run the main application
        import main as app_main
        
        # The main module will handle everything from here
        logger.info("Application module loaded successfully")
        
    except ImportError as e:
        logger.error(f"Failed to import application modules: {e}")
        logger.error("Please ensure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
