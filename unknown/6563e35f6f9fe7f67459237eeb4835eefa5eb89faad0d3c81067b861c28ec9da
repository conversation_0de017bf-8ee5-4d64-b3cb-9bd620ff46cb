
#!/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
# vim: filetype=bash

# proxysmart.org (V2) installation script 

sudo apt install gpg wget

wget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | \
    gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg

source /etc/os-release
ARCH=$(dpkg --print-architecture)

case $VERSION_CODENAME in
focal|jammy|noble|bullseye)
    true ;;
*)
    echo "= Unsupported OS version $NAME / $VERSION_CODENAME"
    exit 1
    ;;
esac

echo "deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main" \
    | sudo tee /etc/apt/sources.list.d/proxysmart.list

case $VERSION_CODENAME in 
focal)
    openvpn_version="release/2.5"
    echo "deb  [trusted=yes]  http://build.openvpn.net/debian/openvpn/$openvpn_version $VERSION_CODENAME main" | sudo tee /etc/apt/sources.list.d/openvpn-aptrepo.list
    sudo apt update
    sudo apt install software-properties-common
    sudo add-apt-repository -y ppa:vbernat/haproxy-2.2
    ;;
esac

sudo apt update && \
    sudo env DEBIAN_FRONTEND=noninteractive apt -y install \
        -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold" proxysmart && \
    sudo /usr/lib/proxysmart/install_pkgs.sh && \
    sudo /usr/lib/proxysmart/install_webapp.sh && \
    sudo /usr/lib/proxysmart/install_openvpn.sh