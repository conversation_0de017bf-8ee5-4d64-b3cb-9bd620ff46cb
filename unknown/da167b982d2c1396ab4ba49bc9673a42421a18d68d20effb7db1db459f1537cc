"""
Message queue system for SMS processing using Redis and Celery.
"""
import json
import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

try:
    import redis
    from celery import Celery
    from celery.result import AsyncResult
except ImportError:
    # Fallback for when dependencies are not installed yet
    redis = None
    Celery = None
    AsyncResult = None

from .config import config

logger = logging.getLogger(__name__)


@dataclass
class SMSMessage:
    """SMS message data structure."""
    id: Optional[str] = None
    modem_port: str = ""
    sender: str = ""
    recipient: str = ""
    message_body: str = ""
    timestamp_device: str = ""
    timestamp_received: str = ""
    status: str = "PENDING"
    attempts: int = 0
    max_attempts: int = 5
    priority: int = 1  # 1=high, 2=normal, 3=low
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SMSMessage':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class ModemHealthStatus:
    """Modem health status data structure."""
    port: str
    status: str  # HEALTHY, DEGRADED, FAILED
    last_check: str
    error_count: int = 0
    response_time_ms: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class RedisQueue:
    """Redis-based message queue implementation."""
    
    def __init__(self, redis_config=None):
        self.config = redis_config or config.redis
        self.redis_client = None
        self._connect()
    
    def _connect(self):
        """Connect to Redis."""
        if redis is None:
            logger.warning("Redis not available - message queue disabled")
            return
        
        try:
            self.redis_client = redis.Redis(
                host=self.config.host,
                port=self.config.port,
                password=self.config.password or None,
                db=self.config.db,
                max_connections=self.config.max_connections,
                socket_timeout=self.config.socket_timeout,
                decode_responses=True
            )
            # Test connection
            self.redis_client.ping()
            logger.info(f"Connected to Redis at {self.config.host}:{self.config.port}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
    
    def is_connected(self) -> bool:
        """Check if Redis is connected."""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except:
            return False
    
    def push_sms(self, message: SMSMessage, queue_name: str = None) -> bool:
        """Push SMS message to queue."""
        if not self.is_connected():
            logger.error("Redis not connected - cannot push SMS")
            return False
        
        queue_name = queue_name or config.sms.queue_name
        
        try:
            # Add timestamp if not present
            if not message.timestamp_received:
                message.timestamp_received = datetime.utcnow().isoformat()
            
            # Generate ID if not present
            if not message.id:
                message.id = f"sms_{datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Push to priority queue based on priority
            priority_queue = f"{queue_name}:priority_{message.priority}"
            
            message_data = json.dumps(message.to_dict())
            result = self.redis_client.lpush(priority_queue, message_data)
            
            # Also add to main queue for monitoring
            self.redis_client.lpush(queue_name, message_data)
            
            logger.debug(f"Pushed SMS {message.id} to queue {priority_queue}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to push SMS to queue: {e}")
            return False
    
    def pop_sms(self, queue_name: str = None, timeout: int = 1) -> Optional[SMSMessage]:
        """Pop SMS message from queue (blocking)."""
        if not self.is_connected():
            return None
        
        queue_name = queue_name or config.sms.queue_name
        
        try:
            # Try priority queues first (1=high, 2=normal, 3=low)
            priority_queues = [f"{queue_name}:priority_{i}" for i in [1, 2, 3]]
            
            result = self.redis_client.brpop(priority_queues, timeout=timeout)
            if result:
                queue, message_data = result
                message_dict = json.loads(message_data)
                return SMSMessage.from_dict(message_dict)
            
        except Exception as e:
            logger.error(f"Failed to pop SMS from queue: {e}")
        
        return None
    
    def get_queue_size(self, queue_name: str = None) -> int:
        """Get queue size."""
        if not self.is_connected():
            return 0
        
        queue_name = queue_name or config.sms.queue_name
        
        try:
            total_size = 0
            # Sum all priority queues
            for priority in [1, 2, 3]:
                priority_queue = f"{queue_name}:priority_{priority}"
                total_size += self.redis_client.llen(priority_queue)
            return total_size
        except Exception as e:
            logger.error(f"Failed to get queue size: {e}")
            return 0
    
    def push_to_dead_letter_queue(self, message: SMSMessage, error: str = "") -> bool:
        """Push failed message to dead letter queue."""
        if not self.is_connected():
            return False
        
        try:
            dlq_name = config.sms.dead_letter_queue
            
            # Add failure information
            failure_data = {
                **message.to_dict(),
                "failed_at": datetime.utcnow().isoformat(),
                "failure_reason": error,
                "final_attempt": message.attempts
            }
            
            self.redis_client.lpush(dlq_name, json.dumps(failure_data))
            logger.warning(f"Moved SMS {message.id} to dead letter queue: {error}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to push to dead letter queue: {e}")
            return False
    
    def set_modem_health(self, health_status: ModemHealthStatus) -> bool:
        """Set modem health status."""
        if not self.is_connected():
            return False
        
        try:
            key = f"modem_health:{health_status.port}"
            data = json.dumps(health_status.to_dict())
            
            # Set with expiration (health data expires after 5 minutes)
            self.redis_client.setex(key, 300, data)
            return True
            
        except Exception as e:
            logger.error(f"Failed to set modem health: {e}")
            return False
    
    def get_modem_health(self, port: str) -> Optional[ModemHealthStatus]:
        """Get modem health status."""
        if not self.is_connected():
            return None
        
        try:
            key = f"modem_health:{port}"
            data = self.redis_client.get(key)
            
            if data:
                health_dict = json.loads(data)
                return ModemHealthStatus(**health_dict)
            
        except Exception as e:
            logger.error(f"Failed to get modem health: {e}")
        
        return None
    
    def get_all_modem_health(self) -> List[ModemHealthStatus]:
        """Get health status for all modems."""
        if not self.is_connected():
            return []
        
        try:
            keys = self.redis_client.keys("modem_health:*")
            health_statuses = []
            
            for key in keys:
                data = self.redis_client.get(key)
                if data:
                    health_dict = json.loads(data)
                    health_statuses.append(ModemHealthStatus(**health_dict))
            
            return health_statuses
            
        except Exception as e:
            logger.error(f"Failed to get all modem health: {e}")
            return []


class CeleryApp:
    """Celery application for background task processing."""
    
    def __init__(self):
        self.app = None
        self._initialize()
    
    def _initialize(self):
        """Initialize Celery app."""
        if Celery is None:
            logger.warning("Celery not available - background tasks disabled")
            return
        
        try:
            broker_url = config.redis.connection_string
            result_backend = config.redis.connection_string
            
            self.app = Celery(
                'smshub_tasks',
                broker=broker_url,
                backend=result_backend,
                include=['smshub_app.app.tasks']
            )
            
            # Configure Celery
            self.app.conf.update(
                task_serializer='json',
                accept_content=['json'],
                result_serializer='json',
                timezone='UTC',
                enable_utc=True,
                task_routes={
                    'smshub_app.app.tasks.process_sms': {'queue': 'sms_processing'},
                    'smshub_app.app.tasks.forward_sms': {'queue': 'sms_forwarding'},
                    'smshub_app.app.tasks.health_check': {'queue': 'health_checks'},
                },
                task_default_queue='default',
                task_default_exchange='default',
                task_default_routing_key='default',
            )
            
            logger.info("Celery app initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Celery: {e}")
            self.app = None
    
    def is_available(self) -> bool:
        """Check if Celery is available."""
        return self.app is not None


# Global instances
redis_queue = RedisQueue()
celery_app = CeleryApp()


# Backward compatibility functions
def push_sms_to_queue(message_data: Dict[str, Any]) -> bool:
    """Push SMS to processing queue."""
    message = SMSMessage.from_dict(message_data)
    return redis_queue.push_sms(message)


def get_queue_stats() -> Dict[str, Any]:
    """Get queue statistics."""
    return {
        "sms_queue_size": redis_queue.get_queue_size(),
        "dead_letter_queue_size": redis_queue.get_queue_size(config.sms.dead_letter_queue),
        "redis_connected": redis_queue.is_connected(),
        "celery_available": celery_app.is_available(),
    }
