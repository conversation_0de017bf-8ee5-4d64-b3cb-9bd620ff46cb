"""
Enhanced database manager with SQLAlchemy support for PostgreSQL and connection pooling.
"""
import logging
import os
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from contextlib import contextmanager

try:
    from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, DateTime, <PERSON>olean
    from sqlalchemy.orm import sessionmaker, Session
    from sqlalchemy.pool import QueuePool, StaticPool
    from sqlalchemy.exc import SQLAlchemyError
    from sqlalchemy.dialects.postgresql import insert as pg_insert
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

from .config import config
from .database_manager import get_db_connection as get_sqlite_connection, init_db as init_sqlite_db

logger = logging.getLogger(__name__)

# Global database engine and session factory
_engine = None
_SessionFactory = None


def initialize_database_engine():
    """Initialize the database engine based on configuration."""
    global _engine, _SessionFactory
    
    if not SQLALCHEMY_AVAILABLE:
        logger.warning("SQLAlchemy not available - falling back to SQLite")
        return False
    
    try:
        db_config = config.database
        connection_string = db_config.connection_string
        
        # Configure engine based on database type
        if db_config.type == "sqlite":
            _engine = create_engine(
                connection_string,
                poolclass=StaticPool,
                connect_args={"check_same_thread": False},
                echo=config.debug_mode
            )
        else:
            # PostgreSQL or MySQL
            _engine = create_engine(
                connection_string,
                poolclass=QueuePool,
                pool_size=db_config.pool_size,
                max_overflow=db_config.max_overflow,
                pool_timeout=db_config.pool_timeout,
                pool_pre_ping=True,  # Verify connections before use
                echo=config.debug_mode
            )
        
        # Create session factory
        _SessionFactory = sessionmaker(bind=_engine)
        
        # Test connection
        with _engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        logger.info(f"Database engine initialized: {db_config.type} at {db_config.host if db_config.type != 'sqlite' else 'local'}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize database engine: {e}")
        _engine = None
        _SessionFactory = None
        return False


@contextmanager
def get_db_session():
    """Get database session with automatic cleanup."""
    if not _SessionFactory:
        # Fallback to SQLite
        logger.warning("SQLAlchemy session not available - using SQLite fallback")
        yield None
        return
    
    session = _SessionFactory()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()


def execute_query(query: str, params: Dict[str, Any] = None, fetch_all: bool = True) -> List[Dict[str, Any]]:
    """Execute a raw SQL query and return results."""
    if not _engine:
        logger.warning("Database engine not available")
        return []
    
    try:
        with _engine.connect() as conn:
            result = conn.execute(text(query), params or {})
            
            if fetch_all:
                rows = result.fetchall()
                return [dict(row._mapping) for row in rows]
            else:
                row = result.fetchone()
                return [dict(row._mapping)] if row else []
                
    except SQLAlchemyError as e:
        logger.error(f"Query execution error: {e}")
        return []


def init_enhanced_db():
    """Initialize database with enhanced schema."""
    if not initialize_database_engine():
        logger.warning("Enhanced database initialization failed - falling back to SQLite")
        init_sqlite_db()
        return
    
    try:
        # Create tables using raw SQL for compatibility
        create_tables_sql = """
        -- SMS Messages Table
        CREATE TABLE IF NOT EXISTS sms_messages (
            id SERIAL PRIMARY KEY,
            modem_port VARCHAR(50) NOT NULL,
            modem_message_index INTEGER,
            sender VARCHAR(20),
            timestamp_device TIMESTAMP,
            timestamp_received_app TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            message_body TEXT,
            status_on_modem VARCHAR(20),
            is_read_in_app BOOLEAN DEFAULT FALSE,
            forwarding_status VARCHAR(20) DEFAULT 'PENDING',
            forwarding_attempts INTEGER DEFAULT 0,
            last_forwarding_error TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            priority INTEGER DEFAULT 2,
            UNIQUE(modem_port, sender, timestamp_device, message_body)
        );
        
        -- Activations Table
        CREATE TABLE IF NOT EXISTS activations (
            id SERIAL PRIMARY KEY,
            modem_port VARCHAR(50) NOT NULL,
            phone_number VARCHAR(20) NOT NULL,
            service VARCHAR(10) NOT NULL,
            country VARCHAR(50),
            operator VARCHAR(50),
            smshub_reported_status INTEGER,
            status_in_our_system VARCHAR(50) NOT NULL DEFAULT 'PENDING_ISSUE',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP
        );
        
        -- Modem Health Table (new)
        CREATE TABLE IF NOT EXISTS modem_health (
            id SERIAL PRIMARY KEY,
            port VARCHAR(50) NOT NULL,
            status VARCHAR(20) NOT NULL,
            last_check TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            error_count INTEGER DEFAULT 0,
            response_time_ms FLOAT DEFAULT 0.0,
            details JSONB,
            UNIQUE(port)
        );
        
        -- Task Queue Table (for fallback when Redis is not available)
        CREATE TABLE IF NOT EXISTS task_queue (
            id SERIAL PRIMARY KEY,
            task_type VARCHAR(50) NOT NULL,
            payload JSONB NOT NULL,
            status VARCHAR(20) DEFAULT 'PENDING',
            priority INTEGER DEFAULT 2,
            attempts INTEGER DEFAULT 0,
            max_attempts INTEGER DEFAULT 5,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP,
            error_message TEXT
        );
        """
        
        # For SQLite, adjust the SQL
        if config.database.type == "sqlite":
            create_tables_sql = create_tables_sql.replace("SERIAL", "INTEGER")
            create_tables_sql = create_tables_sql.replace("JSONB", "TEXT")
            create_tables_sql = create_tables_sql.replace("BOOLEAN", "INTEGER")
        
        with _engine.connect() as conn:
            # Execute each statement separately
            statements = [stmt.strip() for stmt in create_tables_sql.split(';') if stmt.strip()]
            for statement in statements:
                if statement:
                    conn.execute(text(statement))
            conn.commit()
        
        # Create indexes
        create_indexes()
        
        logger.info("Enhanced database schema initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize enhanced database: {e}")
        # Fallback to SQLite
        init_sqlite_db()


def create_indexes():
    """Create database indexes for performance."""
    indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_sms_modem_port ON sms_messages (modem_port);",
        "CREATE INDEX IF NOT EXISTS idx_sms_timestamp_received ON sms_messages (timestamp_received_app);",
        "CREATE INDEX IF NOT EXISTS idx_sms_forwarding_status ON sms_messages (forwarding_status);",
        "CREATE INDEX IF NOT EXISTS idx_sms_priority ON sms_messages (priority, created_at);",
        "CREATE INDEX IF NOT EXISTS idx_activations_status ON activations (status_in_our_system);",
        "CREATE INDEX IF NOT EXISTS idx_activations_expires ON activations (expires_at);",
        "CREATE INDEX IF NOT EXISTS idx_activations_modem_service ON activations (modem_port, service, status_in_our_system);",
        "CREATE INDEX IF NOT EXISTS idx_modem_health_port ON modem_health (port);",
        "CREATE INDEX IF NOT EXISTS idx_modem_health_status ON modem_health (status, last_check);",
        "CREATE INDEX IF NOT EXISTS idx_task_queue_status ON task_queue (status, priority, scheduled_at);",
        "CREATE INDEX IF NOT EXISTS idx_task_queue_type ON task_queue (task_type, status);"
    ]
    
    try:
        with _engine.connect() as conn:
            for index_sql in indexes_sql:
                conn.execute(text(index_sql))
            conn.commit()
        logger.info("Database indexes created successfully")
    except Exception as e:
        logger.error(f"Failed to create indexes: {e}")


def add_message_enhanced(message_data: Dict[str, Any]) -> Optional[int]:
    """Add SMS message using enhanced database."""
    if not _engine:
        # Fallback to SQLite
        from .database_manager import add_message
        return add_message(message_data)
    
    required_keys = ["modem_port", "sender", "timestamp_device", "timestamp_received_app", "message_body", "status_on_modem"]
    for key in required_keys:
        if key not in message_data:
            logger.error(f"Missing required key '{key}' in message_data")
            return None
    
    try:
        with get_db_session() as session:
            if session is None:
                # Fallback
                from .database_manager import add_message
                return add_message(message_data)
            
            # Use upsert for PostgreSQL, INSERT OR IGNORE for SQLite
            if config.database.type == "postgresql":
                insert_stmt = pg_insert(text("sms_messages")).values(
                    modem_port=message_data.get("modem_port"),
                    modem_message_index=message_data.get("modem_message_index"),
                    sender=message_data.get("sender"),
                    timestamp_device=message_data.get("timestamp_device"),
                    timestamp_received_app=message_data.get("timestamp_received_app"),
                    message_body=message_data.get("message_body"),
                    status_on_modem=message_data.get("status_on_modem"),
                    is_read_in_app=message_data.get("is_read_in_app", False),
                    forwarding_status=message_data.get("forwarding_status", "PENDING"),
                    forwarding_attempts=message_data.get("forwarding_attempts", 0),
                    last_forwarding_error=message_data.get("last_forwarding_error"),
                    priority=message_data.get("priority", 2)
                )
                
                # On conflict, do nothing (ignore duplicates)
                insert_stmt = insert_stmt.on_conflict_do_nothing(
                    index_elements=['modem_port', 'sender', 'timestamp_device', 'message_body']
                )
                
                result = session.execute(insert_stmt)
                message_id = result.inserted_primary_key[0] if result.inserted_primary_key else None
                
            else:
                # SQLite fallback
                query = text("""
                    INSERT OR IGNORE INTO sms_messages (
                        modem_port, modem_message_index, sender, timestamp_device,
                        timestamp_received_app, message_body, status_on_modem,
                        is_read_in_app, forwarding_status, forwarding_attempts,
                        last_forwarding_error, priority
                    ) VALUES (
                        :modem_port, :modem_message_index, :sender, :timestamp_device,
                        :timestamp_received_app, :message_body, :status_on_modem,
                        :is_read_in_app, :forwarding_status, :forwarding_attempts,
                        :last_forwarding_error, :priority
                    )
                """)
                
                result = session.execute(query, {
                    **message_data,
                    "is_read_in_app": message_data.get("is_read_in_app", 0),
                    "forwarding_status": message_data.get("forwarding_status", "PENDING"),
                    "forwarding_attempts": message_data.get("forwarding_attempts", 0),
                    "priority": message_data.get("priority", 2)
                })
                
                message_id = result.lastrowid
            
            if message_id:
                logger.info(f"Message from {message_data.get('sender')} stored with ID: {message_id}")
                return message_id
            else:
                logger.info(f"Message from {message_data.get('sender')} likely duplicate, not stored")
                return None
                
    except Exception as e:
        logger.error(f"Error adding message to enhanced database: {e}")
        return None


# Backward compatibility - use enhanced functions when available
def get_db_connection():
    """Backward compatibility function."""
    if _engine:
        return _engine.connect()
    else:
        return get_sqlite_connection()


def init_db():
    """Backward compatibility function."""
    init_enhanced_db()


def add_message(message_data: Dict[str, Any]) -> Optional[int]:
    """Backward compatibility function."""
    return add_message_enhanced(message_data)


# Initialize on import
if SQLALCHEMY_AVAILABLE:
    initialize_database_engine()
