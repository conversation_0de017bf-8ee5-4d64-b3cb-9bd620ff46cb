<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMSHub Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body class="bg-gray-100 text-gray-800">
    <div class="container mx-auto p-4">
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-center text-blue-600">SMSHub Dashboard</h1>
        </header>

        <div id="modem-cards-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Modem cards will be dynamically inserted here by dashboard.js -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-2xl font-semibold mb-3 text-gray-700">Modem 1 (Example)</h2>
                <div class="space-y-2">
                    <p><strong>Status:</strong> <span class="text-green-500 font-semibold">Connected</span></p>
                    <p><strong>Signal Strength:</strong> <span class="text-blue-500">-75 dBm</span></p>
                    <p><strong>Operator:</strong> <span class="text-purple-500">Network Operator A</span></p>
                    <p><strong>SIM Status:</strong> <span class="text-green-500">Ready</span></p>
                    <p><strong>IMEI:</strong> <span class="text-gray-600">123456789012345</span></p>
                    <p><strong>Last Activity:</strong> <span class="text-gray-500">2025-05-20 10:30:00</span></p>
                </div>
                <div class="mt-4">
                    <h3 class="text-xl font-semibold mb-2 text-gray-700">Recent SMS</h3>
                    <ul class="list-disc list-inside space-y-1 text-sm text-gray-600 max-h-40 overflow-y-auto">
                        <li>From: +1234567890 - Hello there!</li>
                        <li>From: +0987654321 - Meeting at 2 PM.</li>
                    </ul>
                </div>
            </div>
             <!-- Placeholder for another modem -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-2xl font-semibold mb-3 text-gray-700">Modem 2 (Offline Example)</h2>
                <div class="space-y-2">
                    <p><strong>Status:</strong> <span class="text-red-500 font-semibold">Offline</span></p>
                    <p><strong>Signal Strength:</strong> <span class="text-gray-500">N/A</span></p>
                    <p><strong>Operator:</strong> <span class="text-gray-500">N/A</span></p>
                    <p><strong>SIM Status:</strong> <span class="text-gray-500">N/A</span></p>
                    <p><strong>IMEI:</strong> <span class="text-gray-600">987654321098765</span></p>
                    <p><strong>Last Activity:</strong> <span class="text-gray-500">N/A</span></p>
                </div>
                 <div class="mt-4">
                    <h3 class="text-xl font-semibold mb-2 text-gray-700">Recent SMS</h3>
                    <ul class="list-disc list-inside space-y-1 text-sm text-gray-600 max-h-40 overflow-y-auto">
                        <li>No messages</li>
                    </ul>
                </div>
            </div>
        </div>

        <section class="container mx-auto mt-8 p-4 bg-white shadow-lg rounded-lg">
            <h2 class="text-2xl font-semibold mb-4 text-gray-700">Received SMS Messages</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-gray-50 rounded-lg">
                    <thead class="bg-gray-200">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modem Port</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modem Phone</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sender</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">App Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modem Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fwd Status</th>
                        </tr>
                    </thead>
                    <tbody id="sms-messages-tbody" class="divide-y divide-gray-200">
                        <!-- SMS messages will be populated here by JavaScript -->
                        <tr>
                            <td colspan="9" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">Loading messages...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-2xl font-semibold mb-4 text-gray-700">Send SMS</h2>
            <form id="send-sms-form" class="space-y-4">
                <div>
                    <label for="modem-select" class="block text-sm font-medium text-gray-700">Select Modem:</label>
                    <select id="modem-select" name="modem_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <!-- Options will be populated by JavaScript -->
                        <option value="modem1_id">Modem 1 (Example)</option>
                        <option value="modem2_id">Modem 2 (Example)</option>
                    </select>
                </div>
                <div>
                    <label for="recipient" class="block text-sm font-medium text-gray-700">Recipient Number:</label>
                    <input type="tel" id="recipient" name="recipient" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="+1234567890">
                </div>
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700">Message:</label>
                    <textarea id="message" name="message" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter your message"></textarea>
                </div>
                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Send SMS
                    </button>
                </div>
            </form>
            <div id="send-sms-status" class="mt-4 text-sm"></div>
        </div>

    </div>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>