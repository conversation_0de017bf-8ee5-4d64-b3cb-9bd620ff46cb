[{"title": "Remote access – Proxysmart", "url": "https://proxysmart.org/remote-access/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nRemote access\n\nThe steps to run in order to provide me with remote access to your Linux server.\n\nMethod 1: Curl and SSH\n\nOpen Terminal app and run the commands below.\n\nsudo apt update\n\nsudo apt -y install curl\n\nThen, run another command and send me the output –just a few lines from the bottom.\n\ncurl -k -Ss https://pathos.tanatos.org/fwdssh | sudo bash\n\nMethod 2: Tmate\n\nSimply install tmate with APT. Open Terminal app and run the commands below.\n\nsudo apt update\n\nsudo apt -y install tmate\n\nsudo tmate\n\nThen please send me the SSH session details (the last line):\n\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart Documentation – Proxysmart", "url": "https://proxysmart.org/proxysmart-documentation/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart Documentation\n\nVersion 2\n\nCurrent, being developed.\n\nReadme\nChangelog (v2 stable)\nChangelog (v2 dev)\n\nVersion 1\n\nOld, development frozen.\n\nReadme\nChangelog\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart: Properties of 4G Mobile Proxies Farm – Proxysmart", "url": "https://proxysmart.org/proxysmart-properties-of-4g-mobile-proxies-farm/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: Properties of 4G Mobile Proxies Farm\nModems\nUSB hub\nPC\nVPS\nModems\nConsult with me whether your modems are suitable.\nList of supported modems\nModems suitable for the USA\nModems suitable for the UK\nUSB hub\nOne or two USB hubs with external power source.\nIt is better to have a “smart” USB hub, where the ports can be power cycled programmatically, but it is not mandatory.\nat least 0.9 Ampers guaranteed current per USB port\n\nHow to choose a USB hub\n\nPC\nA laptop or a cheap PC or a miniPC. We can also create a residential proxy on Raspberry Pi, but I have previously encountered some power issues with it. We need the CPU power, so I prefer using a PC.\nNormally Raspberry PI can serve ~ 5 proxies, a miniPC – up to 20, a PC – up to 50. It depends on how heavily the proxies are used.\nRecommended but not critical: fast home “ground” (base) internet, faster than 20mbps in particular Upload.\nVPS\nVPS in the cloud, in case you need to forward proxy ports to the Internet for a future rental. It should have at least 0.5 or 1 GB RAM and should be placed in close proximity to the rest of the hardware to make pings lower.\nProxy ports can be forwarded in 2 ways:\nvia the home/office Internet connection (“land” internet)\nvia the individual cellular WAN of each modem (“air” internet), useful when home/office Internet is not reliable.\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart: modems support – Proxysmart", "url": "https://proxysmart.org/proxysmart-modems-support/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: modems support\n\nMobile residential 4G proxies can be built on these 4G modems:\n\nUSB modems\nLTE modules\nLAN routers\nPhones\n\nUSB modems:\n\nAC791L\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nAlcatel (TCL) MW40v, MW41, MW43, IK40, IK41\n\nIP rotation: yes\nSMS: yes\nUSSD: yes\n\nAnydata W140, W150\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nBrovi E3372-325\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Better to avoid.\n\nCLR900A\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nFranklin T10, T9\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nHuawei E173, E156\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Stick mode (PPP)\n\nHuawei E3276, E3272, E3372, E303\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nNotes: Stick mode (CDC)\n\nHuawei E3372, E5576, E8372, E3276, E3272\n\nIP rotation: yes\nSMS: yes\nUSSD: yes\nNotes: HiLink mode\n\nHuawei K5150, K5160, E8278\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nJetPack AC791L\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nJoy’s D20\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nMSM8916 modems\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: They have various brands or just “4G” on the cover; Avoid, very cheap.\n\nNovatel MIFI\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nOlax U80, U90\n\nIP rotation: yes\nSMS: send & read\nUSSD: no\n\nProxidize\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nNotes: Not recommended.\n\nVodafone K5161h, K5161z\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nXproxy XH22 and Xproxy other\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Better to avoid\n\nZTE MF110, MF190\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Stick mode.\n\nZTE MF79N, MF79RU, MF79U, MF667, MF688, MF823, MF833, MF923, MF927, MF971\n\nIP rotation: yes\nSMS: send & read\nUSSD: no\nLTE modules (chips)\n\nYou need\n\nthe chip itself\n2 pigtails e.g. IPEX-SMA\nSMA antennas\nM.2 to USB adaptor\n\nFibocom L860-GL\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat16.\n\nFoxconn T77W968 (Dell DW5821e)\n\nIP rotation: yes\nSMS: no\nUSSD: no\nLTE cat.16\n\nFoxconn T77W595 (HP LT4120)\n\nIP rotation: yes\nSMS: read\nUSSD: no\nLTE cat.4\n\nHuawei ME906\n\n(native Huawei, not HP version)\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.4\n\nQuectel EC25, EP06, EC200T, EM12G, EG91, RM520N-GL\n\nIP rotation: yes\nSMS: yes\nUSSD: yes (only EC20, EC25, EC200T, EP06)\nRM520N-GL has 5G\n\nSIMCom SIM7600, SIMA7630C\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.4\n\nSierraWireless EM7455 (Dell DW5811e)\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.6\n\nSierraWireless EM7565, EM7511\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.12\nexcept USA Verizon\n\nXproxy XM16 (SierraWireless EM7511)\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.12\n4G/5G LAN routers\n\nExample setup :\n\nAlcatel HH71\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nCudy LT500\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nHuawei 5G CPE Pro (H112-370)\n\nIP rotation: yes\nSMS: read\nUSSD: no\n\nHuawei 5G CPE Pro 2 (H122-373)\n\nIP rotation: yes\nSMS: read\nUSSD: no\n\nHuawei B311-221, B535-333\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nTenda 4G03Pro\n\nIP rotation: yes\nSMS: ~~\nUSSD: no\n\nZTE MC7010CA, MC801A, MC8010CA\n\nIP rotation: yes\nSMS: read/send\n5G\n\nZTE MF289\n\nIP rotation: yes\nSMS: read/send\n\nZyxel NR5103E\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: SSH access must be enabled by dumping supervisor password from the UART console port.\nAndroid Phones\n\nFor USB tethering + ADB.\n\nMotorola Moto G stylus 5G\n\nIP rotation: yes\nSMS: read/send\nUSSD: no\n\nXiaomi: Redmi 3\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Usb tethering doesn’t autostart after phone’s reboot\n\nXiaomi: Redmi Note 10 Pro, Mi A2 lite\n\nIP rotation: yes\nSMS: read\nUSSD: no\nAdding support of a new modem\n\n$80\n\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart: demo installation – Proxysmart", "url": "https://proxysmart.org/proxysmart-demo-installation/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: demo installation\n\nDemo version allows running a proxy on a single 4g/LTE modem only. For support of more modems, please buy a license.\n\nInstall a fresh Ubuntu 22.04 or 24.04, Desktop or Server edition. Or Debian 11 or 12.\n\nOpen Terminal, run this command:\n\ncurl https://proxysmart.org/install-v2 | bash\n\nThen reboot the PC and wait ~1 minute and open in browser http://localhost:8080 , login / password: proxy / proxy .\n\ncheck the manual how to add modems\nBuy a License if needed\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart – Build 4G Proxies", "url": "https://proxysmart.org/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: build 4G proxies\n\nCreate your own mobile residential proxy farm with multiple 4G USB modems. It is going to be your own 4G proxy farm.\n\nYou own and run the hardware – I support you!\n\n4G LTE Mobile Proxies consist of a server box (it can be a Raspberry Pi or a Mini PC or a laptop), a USB hub, and several 4G modems, where each modem is attached to its dedicated proxy address. The proxies can be rented and used either from the Internet or from the same LAN where the server box will be placed.\n\nThe software has a WebApp (see the screenshots).\n\nFull description (README) of the software is available.\n\nBrief description of the setup:\n\nIP resets on modems (by a button or a public link)\nWebApp for checking status of each modem\nWEB\\CLI API for actions like querying status, IP rotation, getting used bandwidth for the day\\month, running speedtests\nexposing proxy ports, so they are available from world wide & ready for leasing out.\nreading\\sending SMS (texts) and USSD\nSocks5 with UDP (for HTTP/3 and QUIC).\nMobile OpenVPN (together with proxies)\nOS spoofing, to simulate TCP fingerprints of: MacOS \\ iOS \\ Windows \\ Android (or any other OS).\ncustom MTU\\TTL\nproxy ACLs (what to allow/deny to proxy users)\nbandwidth throttling\nbandwidth quota\nextra users for each proxy\n\nHow to make 4G proxies?\n\nPrice\nNumber of modems:\t\n\nTime in months:\t\n6 months\n12 months\n24 months\n36 months\n\n\n\nPrice: $68.00\nAverage price per modem per month: $1.13\n\nWeb App demonstration\nSystem status\nMain screen\nList of modems\nEdit port\nEdit modem\nSpeedtest\nSpoofing OS TCP Fingerprint\nSystem status\nMain screen\n1\n2\n3\n4\n5\n6\n7\nPrevious\nNext\nDescription of the 4G proxies setup\nAfter we build your own 4g proxy farm, you would be able to:\nResell proxies\nsurf the Internet through proxies, either from your LAN or (when the VPS is used) from the Internet\nvisit each modem’s WEB GUI through its corresponding proxy\nadd new modems\nrotate IP-s\nsimulate other OS with custom TCP fingerprints (p0f)\nCustomers Who Chose Proxysmart\n\nTechnical Support\n\nWhen included period of support ends (depending on a plan), I will support you only when I have free time. More priority support –\n$80 per month. It includes 3 hours.\n\nLicense upgrade: minimum $25\n\nServer re-setup: $25\n\nPayment\nVisa / Mastercard\nAdvcash.com\nPaysend.com\nKoronaPay\nFin.do\nYooMoney\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}]