"""
Celery background tasks for SMS processing, forwarding, and health monitoring.
"""
import logging
import time
import requests
from datetime import datetime
from typing import Dict, Any, Optional

try:
    from celery import Task
    from circuitbreaker import circuit
except ImportError:
    # Fallback for when dependencies are not installed yet
    Task = object
    def circuit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

from .config import config
from .message_queue import celery_app, redis_queue, SMSMessage, ModemHealthStatus
from .database_manager import add_message, update_forwarding_status

logger = logging.getLogger(__name__)


class CallbackTask(Task):
    """Base task class with callbacks."""
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called when task succeeds."""
        logger.debug(f"Task {task_id} succeeded with result: {retval}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when task fails."""
        logger.error(f"Task {task_id} failed: {exc}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Called when task is retried."""
        logger.warning(f"Task {task_id} retrying due to: {exc}")


# Only create tasks if Celery is available
if celery_app.app:
    
    @celery_app.app.task(base=CallbackTask, bind=True, max_retries=3)
    def process_sms(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming SMS message."""
        try:
            logger.info(f"Processing SMS: {message_data.get('id', 'unknown')}")
            
            # Convert to SMSMessage object
            sms_message = SMSMessage.from_dict(message_data)
            
            # Validate message
            if not sms_message.sender or not sms_message.message_body:
                raise ValueError("Invalid SMS message: missing sender or body")
            
            # Store in database
            db_message_data = {
                "modem_port": sms_message.modem_port,
                "sender": sms_message.sender,
                "timestamp_device": sms_message.timestamp_device,
                "timestamp_received_app": sms_message.timestamp_received,
                "message_body": sms_message.message_body,
                "status_on_modem": "RECEIVED",
                "is_read_in_app": 0,
                "forwarding_status": "PENDING",
                "forwarding_attempts": 0,
                "last_forwarding_error": None
            }
            
            message_id = add_message(db_message_data)
            
            if message_id:
                logger.info(f"SMS stored in database with ID: {message_id}")
                
                # Queue for forwarding if enabled
                if config.sms.forwarding_enabled:
                    forward_sms.delay(message_id, sms_message.to_dict())
                
                return {
                    "status": "SUCCESS",
                    "message_id": message_id,
                    "processed_at": datetime.utcnow().isoformat()
                }
            else:
                raise Exception("Failed to store SMS in database")
                
        except Exception as exc:
            logger.error(f"Failed to process SMS: {exc}")
            
            # Move to dead letter queue after max retries
            if self.request.retries >= self.max_retries:
                sms_message = SMSMessage.from_dict(message_data)
                redis_queue.push_to_dead_letter_queue(sms_message, str(exc))
                return {"status": "FAILED", "error": str(exc)}
            
            # Retry with exponential backoff
            countdown = 2 ** self.request.retries
            raise self.retry(exc=exc, countdown=countdown)
    
    
    @circuit(failure_threshold=config.monitoring.circuit_breaker_failure_threshold,
             recovery_timeout=config.monitoring.circuit_breaker_recovery_timeout)
    def make_forwarding_request(url: str, payload: Dict[str, Any], headers: Dict[str, str]) -> requests.Response:
        """Make HTTP request with circuit breaker protection."""
        return requests.post(url, json=payload, headers=headers, timeout=30)
    
    
    @celery_app.app.task(base=CallbackTask, bind=True, max_retries=5)
    def forward_sms(self, message_id: int, sms_data: Dict[str, Any]) -> Dict[str, Any]:
        """Forward SMS to external service."""
        try:
            logger.info(f"Forwarding SMS ID: {message_id}")
            
            if not config.sms.forwarding_enabled or not config.sms.forwarding_url:
                logger.warning("SMS forwarding is disabled or URL not configured")
                return {"status": "SKIPPED", "reason": "Forwarding disabled"}
            
            # Prepare payload
            payload = {
                "message_id": message_id,
                "from": sms_data.get("sender"),
                "to": sms_data.get("recipient", sms_data.get("modem_port")),
                "message": sms_data.get("message_body"),
                "timestamp": sms_data.get("timestamp_received"),
                "source": f"modem:{sms_data.get('modem_port')}"
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {config.smshub_api_key}",
                "User-Agent": f"SMSHub-Agent/{config.smshub_agent_id}"
            }
            
            # Make request with circuit breaker
            response = make_forwarding_request(config.sms.forwarding_url, payload, headers)
            response.raise_for_status()
            
            response_data = response.json()
            
            if response_data.get("status") == "SUCCESS":
                logger.info(f"Successfully forwarded SMS ID: {message_id}")
                update_forwarding_status(message_id, "SUCCESS", self.request.retries + 1)
                return {"status": "SUCCESS", "response": response_data}
            else:
                error_msg = response_data.get("error", "Unknown error from forwarding service")
                logger.error(f"Forwarding failed for SMS ID {message_id}: {error_msg}")
                
                # Update status based on retry count
                if self.request.retries >= self.max_retries:
                    update_forwarding_status(message_id, "FAILED", self.request.retries + 1, error_msg)
                    return {"status": "FAILED", "error": error_msg}
                else:
                    update_forwarding_status(message_id, "RETRYING", self.request.retries + 1, error_msg)
                    raise Exception(error_msg)
                    
        except requests.exceptions.RequestException as exc:
            logger.error(f"HTTP error forwarding SMS ID {message_id}: {exc}")
            
            if self.request.retries >= self.max_retries:
                update_forwarding_status(message_id, "FAILED", self.request.retries + 1, str(exc))
                return {"status": "FAILED", "error": str(exc)}
            
            # Exponential backoff retry
            countdown = min(300, (2 ** self.request.retries) * 10)  # Max 5 minutes
            raise self.retry(exc=exc, countdown=countdown)
            
        except Exception as exc:
            logger.error(f"Unexpected error forwarding SMS ID {message_id}: {exc}")
            
            if self.request.retries >= self.max_retries:
                update_forwarding_status(message_id, "FAILED", self.request.retries + 1, str(exc))
                return {"status": "FAILED", "error": str(exc)}
            
            countdown = min(300, (2 ** self.request.retries) * 10)
            raise self.retry(exc=exc, countdown=countdown)
    
    
    @celery_app.app.task(base=CallbackTask)
    def health_check_modem(port: str) -> Dict[str, Any]:
        """Perform health check on a specific modem."""
        try:
            from .modem_manager import get_modem_info
            
            start_time = time.time()
            modem_info = get_modem_info(port)
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            # Determine health status
            status = modem_info.get("status", "UNKNOWN")
            if status == "RESPONSIVE":
                health_status = "HEALTHY"
            elif status in ["CONNECTED", "PARTIAL"]:
                health_status = "DEGRADED"
            else:
                health_status = "FAILED"
            
            # Create health status object
            health = ModemHealthStatus(
                port=port,
                status=health_status,
                last_check=datetime.utcnow().isoformat(),
                error_count=0 if health_status == "HEALTHY" else 1,
                response_time_ms=response_time
            )
            
            # Store in Redis
            redis_queue.set_modem_health(health)
            
            logger.debug(f"Health check completed for modem {port}: {health_status}")
            
            return {
                "port": port,
                "status": health_status,
                "response_time_ms": response_time,
                "modem_info": modem_info
            }
            
        except Exception as exc:
            logger.error(f"Health check failed for modem {port}: {exc}")
            
            # Record failed health check
            health = ModemHealthStatus(
                port=port,
                status="FAILED",
                last_check=datetime.utcnow().isoformat(),
                error_count=1,
                response_time_ms=0.0
            )
            redis_queue.set_modem_health(health)
            
            return {
                "port": port,
                "status": "FAILED",
                "error": str(exc)
            }
    
    
    @celery_app.app.task(base=CallbackTask)
    def periodic_health_check() -> Dict[str, Any]:
        """Perform periodic health checks on all modems."""
        try:
            from .modem_manager import get_modem_status
            
            logger.info("Starting periodic health check for all modems")
            
            modem_statuses = get_modem_status()
            health_results = []
            
            for modem in modem_statuses:
                port = modem.get("port") or modem.get("id")
                if port:
                    # Schedule individual health check
                    result = health_check_modem.delay(port)
                    health_results.append({
                        "port": port,
                        "task_id": result.id
                    })
            
            logger.info(f"Scheduled health checks for {len(health_results)} modems")
            
            return {
                "status": "SUCCESS",
                "scheduled_checks": len(health_results),
                "results": health_results
            }
            
        except Exception as exc:
            logger.error(f"Periodic health check failed: {exc}")
            return {"status": "FAILED", "error": str(exc)}
    
    
    @celery_app.app.task(base=CallbackTask)
    def cleanup_old_data() -> Dict[str, Any]:
        """Clean up old data from queues and cache."""
        try:
            logger.info("Starting cleanup of old data")
            
            # This would implement cleanup logic for:
            # - Old messages in dead letter queue
            # - Expired health check data
            # - Old task results
            
            # For now, just return success
            return {
                "status": "SUCCESS",
                "cleaned_items": 0,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as exc:
            logger.error(f"Cleanup task failed: {exc}")
            return {"status": "FAILED", "error": str(exc)}


else:
    # Fallback functions when Celery is not available
    def process_sms(message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback SMS processing without Celery."""
        logger.warning("Celery not available - processing SMS synchronously")
        # Implement synchronous processing here
        return {"status": "PROCESSED_SYNC", "message": "Celery not available"}
    
    def forward_sms(message_id: int, sms_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback SMS forwarding without Celery."""
        logger.warning("Celery not available - skipping SMS forwarding")
        return {"status": "SKIPPED", "reason": "Celery not available"}
    
    def health_check_modem(port: str) -> Dict[str, Any]:
        """Fallback health check without Celery."""
        logger.warning("Celery not available - skipping health check")
        return {"status": "SKIPPED", "reason": "Celery not available"}


# Task scheduling helpers
def schedule_sms_processing(message_data: Dict[str, Any]) -> Optional[str]:
    """Schedule SMS for processing."""
    if celery_app.app:
        result = process_sms.delay(message_data)
        return result.id
    else:
        # Fallback to synchronous processing
        process_sms(message_data)
        return None


def schedule_health_checks() -> Optional[str]:
    """Schedule periodic health checks."""
    if celery_app.app:
        result = periodic_health_check.delay()
        return result.id
    return None
