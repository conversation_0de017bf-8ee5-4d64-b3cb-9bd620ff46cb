import serial
import time

# Pick one of the ports that consistently gives PermissionError, e.g., COM281
# You can change this to COM271, COM13, COM283, COM11, or COM175 if needed for testing.
PORT_TO_CHECK = "COM281"
BAUDRATE = 115200
TIMEOUT = 1.0 # Using float for timeout

print(f"Attempting to open port {PORT_TO_CHECK} with baudrate {BAUDRATE} and timeout {TIMEOUT}s...")
ser = None
try:
    ser = serial.Serial(PORT_TO_CHECK, baudrate=BAUDRATE, timeout=TIMEOUT, write_timeout=TIMEOUT)
    if ser.is_open:
        print(f"Successfully opened port {PORT_TO_CHECK}.")
        print("Flushing input and output buffers...")
        ser.reset_input_buffer()
        ser.reset_output_buffer()
        print("Sending AT command (AT\\r\\n)...")
        ser.write(b"AT\r\n")
        print("Waiting for response (up to 1 second)...")
        time.sleep(0.5) # Give modem a moment to process and respond

        response_lines = []
        start_time = time.time()
        # Read lines until timeout or no more data
        while (time.time() - start_time) < TIMEOUT:
            if ser.in_waiting > 0:
                line_bytes = ser.readline()
                try:
                    line = line_bytes.decode('utf-8').strip()
                    print(f"Received (utf-8 decoded): {line!r}")
                except UnicodeDecodeError:
                    line = line_bytes.decode('latin-1', errors='ignore').strip()
                    print(f"Received (latin-1 decoded): {line!r}")
                
                if line: # Add non-empty lines
                    response_lines.append(line)
            else:
                # Small sleep if no data to prevent busy-waiting, but ensure timeout is respected
                # This helps ensure we don't exit the loop prematurely if data is slow to arrive
                # but still within the overall TIMEOUT.
                time.sleep(0.05) 
        
        if not response_lines:
            print("No response received for AT command within timeout.")
        else:
            print(f"Full response: {response_lines}")
            
    else:
        # This case should ideally not be reached if serial.Serial() succeeds without error
        # and doesn't open the port, but it's a safeguard.
        print(f"serial.Serial() call for {PORT_TO_CHECK} did not raise an error, but ser.is_open is false.")

except serial.SerialException as e:
    print(f"SerialException while trying to open or use port {PORT_TO_CHECK}: {e}")
except Exception as e:
    print(f"An unexpected error occurred with port {PORT_TO_CHECK}: {e}")
finally:
    if ser and ser.is_open:
        ser.close()
        print(f"Closed port {PORT_TO_CHECK}.")
    elif ser: # ser object exists but port wasn't opened
        print(f"Port {PORT_TO_CHECK} was initialized but not open, no explicit close needed.")
    else: # ser object was not even initialized (e.g. error in constructor)
        print(f"Serial object for {PORT_TO_CHECK} was not successfully initialized.")

print("Port check finished.")
