import pytest
import json
import json
import os
import sys # Added sys
from unittest.mock import patch, mock_open

# Add project root to sys.path to allow for absolute imports from smshub_app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from smshub_app.app.config_manager import load_config # Updated import

# This is the path that config_manager.py will try to open.
# We need to determine this path carefully for mocking.
# current_dir = os.path.dirname(os.path.abspath(__file__))
# config_file_path = os.path.join(current_dir, '..', 'config.json')
# If __file__ for config_manager.py is /path/to/smshub_app/app/config_manager.py
# current_dir = /path/to/smshub_app/app
# config_file_path = /path/to/smshub_app/config.json

# For the purpose of these tests, we will mock 'open' within the 'config_manager' module.
# The path 'smshub_app.app.config_manager.open' refers to the 'open' function
# as it's seen *within* the config_manager.py module.
# The actual path load_config will try to open is determined by its internal logic.
# We need to ensure our mock for 'open' intercepts calls for that specific path.

# A more robust way to get the expected path for mocking, assuming tests are in smshub_app/tests
# and app is in smshub_app/app
EXPECTED_CONFIG_PATH = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'config.json'))


def test_load_config_success():
    """
    Tests successful loading of configuration.
    """
    sample_config = {"key": "value"}
    mock_file_content = json.dumps(sample_config)

    # Patch 'open' in the module where it's being called (config_manager)
    # and 'json.load' globally or within 'json' module.
    with patch('smshub_app.app.config_manager.open', mock_open(read_data=mock_file_content)) as mock_file, \
         patch('json.load') as mock_json_load:
        mock_json_load.return_value = sample_config
        
        config = load_config() # load_config takes no arguments
        
        # Assert that open was called with the path constructed inside load_config
        # This path is relative to config_manager.py: ../config.json
        # The exact path string depends on how os.path.join and os.path.abspath resolve.
        # We can capture the call and check, or mock os.path functions too.
        # For simplicity, we'll assume the mock_open intercepts the call correctly
        # if patched at 'app.config_manager.open'.
        # The first argument to mock_file.assert_called_once_with would be this dynamic path.
        # A simpler check is that it was called.
        mock_file.assert_called_once() 
        # We can get the path it was called with:
        called_path = mock_file.call_args[0][0]
        # And assert it ends with 'config.json' or matches the expected structure
        assert 'config.json' in called_path 
        assert mock_file.call_args[0][1] == 'r' # mode 'r'
        assert mock_file.call_args[1]['encoding'] == 'utf-8' # encoding

        mock_json_load.assert_called_once_with(mock_file())
        assert config == sample_config

def test_load_config_file_not_found():
    """
    Tests behavior when the configuration file is not found.
    load_config should return an empty dict in this case.
    """
    with patch('smshub_app.app.config_manager.open', side_effect=FileNotFoundError) as mock_file:
        config = load_config() # load_config takes no arguments
        
        mock_file.assert_called_once()
        called_path = mock_file.call_args[0][0]
        assert 'config.json' in called_path
        assert config == {}

def test_load_config_invalid_json():
    """
    Tests behavior when the configuration file contains invalid JSON.
    load_config should return an empty dict in this case.
    """
    mock_file_content = "this is not valid json"
    
    with patch('smshub_app.app.config_manager.open', mock_open(read_data=mock_file_content)) as mock_file, \
         patch('json.load', side_effect=json.JSONDecodeError("Error", "doc", 0)) as mock_json_load:
        
        config = load_config() # load_config takes no arguments
        
        mock_file.assert_called_once()
        called_path = mock_file.call_args[0][0]
        assert 'config.json' in called_path
        mock_json_load.assert_called_once_with(mock_file())
        assert config == {}