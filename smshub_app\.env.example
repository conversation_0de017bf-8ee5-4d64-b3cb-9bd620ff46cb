# SMS Hub Enhanced Configuration
# Copy this file to .env and customize the values

# === Database Configuration ===
# Database type: sqlite, postgresql, mysql
DB_TYPE=sqlite
SQLITE_PATH=sms_database.db

# PostgreSQL/MySQL settings (when DB_TYPE is not sqlite)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=smshub_user
DB_PASSWORD=your_secure_password
DB_NAME=smshub
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# === Redis Configuration ===
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=50
REDIS_SOCKET_TIMEOUT=5

# === SMS Hub API Configuration ===
SMSHUB_API_KEY=your_api_key_here
SMSHUB_AGENT_ID=your_agent_id
SMSHUB_PROTOCOL_KEY=your_protocol_key
SMSHUB_SERVER_URL=https://api.smshub.org

# === Service Configuration ===
SERVICE_COUNTRY_NAME=usaphysic
SERVICE_OPERATOR_NAME=any

# === Server Configuration ===
SERVER_PORT=5000
APP_HOST=0.0.0.0
DEBUG_MODE=false

# === Modem Configuration ===
MODEM_SCAN_INTERVAL=10
MAX_CONCURRENT_MODEMS=50
MODEM_CONNECTION_TIMEOUT=5.0
MODEM_READ_TIMEOUT=1.0
MODEM_WRITE_TIMEOUT=1.0
MODEM_HEALTH_CHECK_INTERVAL=30
MODEM_MAX_FAILED_HEALTH_CHECKS=3

# === SMS Processing Configuration ===
SMS_FORWARDING_ENABLED=true
SMS_FORWARDING_URL=https://api.smshub.org/push_sms
SMS_FORWARDING_MAX_ATTEMPTS=5
SMS_FORWARDING_RETRY_DELAY=10
SMS_FORWARDING_SERVICE_INTERVAL=60
SMS_MAX_MESSAGE_LENGTH=1600
SMS_BATCH_SIZE=10
SMS_QUEUE_NAME=sms_processing
SMS_DEAD_LETTER_QUEUE=sms_failed

# === Monitoring Configuration ===
MONITORING_ENABLED=true
METRICS_PORT=8000
HEALTH_CHECK_PORT=8001
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# === Circuit Breaker Configuration ===
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=30

# === Legacy Configuration Support ===
# These are automatically mapped from config.json if not set above
# sms_forwarding_enabled -> SMS_FORWARDING_ENABLED
# sms_forwarding_url -> SMS_FORWARDING_URL
# smshub_api_key -> SMSHUB_API_KEY
# etc.
