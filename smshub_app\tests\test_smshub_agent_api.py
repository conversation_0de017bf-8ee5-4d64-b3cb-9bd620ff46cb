import unittest
import json
import sqlite3 # Added for direct in-memory DB management
from unittest.mock import patch, MagicMock

# Ensure the app path is correct for imports
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from smshub_app.app.main import app, SMSHUB_PROTOCOL_KEY, config
# We will mock get_db_connection, so direct imports from database_manager
# for db operations will use the mocked connection.
from smshub_app.app.database_manager import (
    create_activation,
    get_activation_by_id,
    update_activation_details,
    find_active_activation_for_modem_service,
    get_open_activation_for_modem,
    update_forwarding_status,
    get_pending_forward_messages,
    add_message
)
# Import the original init_db to get table creation SQL or adapt it
from smshub_app.app.database_manager import init_db as original_init_db_logic

# Use the actual protocol key from the app's config for tests
TEST_PROTOCOL_KEY = SMSHUB_PROTOCOL_KEY
# Use actual country and operator from config for valid requests
CONFIG_COUNTRY = config.get("service_country_name", "usaphyic")
CONFIG_OPERATOR = config.get("service_operator_name", "any")
# Pick an enabled service from config for tests, e.g., "wa"
ENABLED_SERVICE = "wa" if config.get("services", {}).get("wa") else next((s for s,e in config.get("services", {}).items() if e), "lf")


class SMSHubAgentAPITestCase(unittest.TestCase):

    def setUp(self):
        app.testing = True
        self.client = app.test_client()

        # Create a single in-memory SQLite connection for this test method
        self.db_conn = sqlite3.connect(':memory:')
        self.db_conn.row_factory = sqlite3.Row
        
        # Initialize schema on this connection
        # We can adapt original_init_db_logic or copy its CREATE TABLE statements
        cursor = self.db_conn.cursor()
        # SMS Messages Table from database_manager.py
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS sms_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            modem_port TEXT NOT NULL,
            modem_message_index INTEGER,
            sender TEXT,
            timestamp_device TEXT,
            timestamp_received_app TEXT NOT NULL,
            message_body TEXT,
            status_on_modem TEXT,
            is_read_in_app INTEGER DEFAULT 0,
            forwarding_status TEXT DEFAULT 'PENDING',
            forwarding_attempts INTEGER DEFAULT 0,
            last_forwarding_error TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(modem_port, sender, timestamp_device, message_body)
        )
        """)
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sms_modem_port ON sms_messages (modem_port);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sms_timestamp_received_app ON sms_messages (timestamp_received_app);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sms_forwarding_status ON sms_messages (forwarding_status);")
        # Activations Table from database_manager.py
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS activations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            modem_port TEXT NOT NULL,
            phone_number TEXT NOT NULL,
            service TEXT NOT NULL,
            country TEXT,
            operator TEXT,
            smshub_reported_status INTEGER,
            status_in_our_system TEXT NOT NULL DEFAULT 'PENDING_ISSUE',
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            expires_at TEXT
        )
        """)
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activations_status_ours ON activations (status_in_our_system);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activations_expires_at ON activations (expires_at);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activations_modem_service ON activations (modem_port, service, status_in_our_system);")
        self.db_conn.commit()

        # Patch get_db_connection in the database_manager module
        # Create a MagicMock that will act as the connection object returned by get_db_connection
        # This mock will delegate calls to the real self.db_conn, except for 'close'.
        self.mock_connection_wrapper = MagicMock(spec=sqlite3.Connection)
        self.mock_connection_wrapper.cursor.side_effect = lambda: self.db_conn.cursor()
        self.mock_connection_wrapper.commit.side_effect = lambda: self.db_conn.commit()
        self.mock_connection_wrapper.rollback.side_effect = lambda: self.db_conn.rollback()
        # Add other necessary sqlite3.Connection methods if they are directly called by your db functions
        # For now, cursor, commit, rollback are the most common. row_factory is set on the real conn.
        
        # The 'close' method on the wrapper will be a mock that does nothing by default,
        # or we can explicitly set it:
        self.mock_connection_wrapper.close = MagicMock(return_value=None)

        # Patch get_db_connection in the database_manager module to return our wrapper
        self.patcher_get_db_connection = patch('smshub_app.app.database_manager.get_db_connection', return_value=self.mock_connection_wrapper)
        self.patcher_get_db_connection.start()
        
        # Common mock modem data
        self.mock_modem_id = "MODEM001"
        self.mock_modem_phone_number = "12345678900" # Ensure it's a string for int conversion later
        self.mock_modem_port = "COM99" # Assuming modem_id is used as port in some contexts

        # Patch config to include a test modem if not present or to control its state
        # This ensures that get_modem_status can return something predictable
        self.original_modems_config = config.get("modems", [])
        config["modems"] = [
            {
                "id": self.mock_modem_id,
                "port": self.mock_modem_port,
                "name": "Test Modem 1",
                "enabled": True,
                "phone_number": self.mock_modem_phone_number,
                "country_code": CONFIG_COUNTRY,
                "operator": CONFIG_OPERATOR,
                "smsc": "",
                "preferred_network_mode": "auto",
                "allow_outgoing_sms": True,
                "status_check_interval": 30,
                "sms_check_interval": 10
            }
        ]


    def tearDown(self):
        self.patcher_get_db_connection.stop() # Stop the patch for get_db_connection
        
        # Now actually close the real database connection
        if self.db_conn:
            self.db_conn.close()
            
        # Restore original modems config
        config["modems"] = self.original_modems_config
        # app.config.pop('DATABASE_PATH', None) # Clean up app config if needed
        pass

    def _make_api_call(self, payload):
        return self.client.post('/api/smshub_agent',
                                data=json.dumps(payload),
                                content_type='application/json',
                                headers={"User-Agent": "SMSHubAgent-Test/1.0"})

    # --- Test GET_SERVICES ---
    @patch('smshub_app.app.main.get_modem_status')
    def test_get_services_success(self, mock_get_modem_status):
        # Mock modem status to simulate one active modem with a phone number
        mock_get_modem_status.return_value = [
            {"id": self.mock_modem_id, "port": self.mock_modem_port, "status": "RESPONSIVE", "phone_number": self.mock_modem_phone_number, "signal_strength": "Good"}
        ]
        
        payload = {
            "action": "GET_SERVICES",
            "key": TEST_PROTOCOL_KEY
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertEqual(data['status'], "SUCCESS")
        self.assertIn("countryList", data)
        self.assertEqual(len(data['countryList']), 1)
        country_data = data['countryList'][0]
        self.assertEqual(country_data['country'], CONFIG_COUNTRY)
        self.assertIn(CONFIG_OPERATOR, country_data['operatorMap'])
        
        operator_services = country_data['operatorMap'][CONFIG_OPERATOR]
        # Check if our enabled service is present and has a count of 1 (due to one mocked modem)
        self.assertIn(ENABLED_SERVICE, operator_services)
        self.assertEqual(operator_services[ENABLED_SERVICE], 1)


    def test_get_services_invalid_key(self):
        payload = {
            "action": "GET_SERVICES",
            "key": "INVALID_KEY_HERE"
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 403) # Forbidden
        data = response.get_json()
        self.assertEqual(data['status'], "ERROR")
        self.assertEqual(data['error'], "Invalid protocol key")

    # --- Test GET_NUMBER ---
    @patch('smshub_app.app.main.get_modem_status')
    def test_get_number_success(self, mock_get_modem_status):
        mock_get_modem_status.return_value = [
            {"id": self.mock_modem_id, "port": self.mock_modem_port, "status": "RESPONSIVE", "phone_number": self.mock_modem_phone_number}
        ]
        
        payload = {
            "action": "GET_NUMBER",
            "key": TEST_PROTOCOL_KEY,
            "country": CONFIG_COUNTRY,
            "service": ENABLED_SERVICE,
            "operator": CONFIG_OPERATOR,
            "sum": 10.0,
            "currency": 643 # RUB
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertEqual(data['status'], "SUCCESS")
        self.assertEqual(data['number'], int(self.mock_modem_phone_number))
        self.assertIsInstance(data['activationId'], int)
        
        # Verify activation was created in DB
        with app.app_context():
            activation_id_from_response = data.get('activationId')
            self.assertIsNotNone(activation_id_from_response, "Activation ID should be in the response")
            activation = get_activation_by_id(activation_id_from_response) # type: ignore
            self.assertIsNotNone(activation, "Activation record should exist in DB")
            if activation: # Satisfy Pylance
                self.assertEqual(activation['modem_port'], self.mock_modem_id)
                self.assertEqual(activation['phone_number'], self.mock_modem_phone_number)
                self.assertEqual(activation['service'], ENABLED_SERVICE)

    @patch('smshub_app.app.main.get_modem_status')
    def test_get_number_no_available_modems(self, mock_get_modem_status):
        mock_get_modem_status.return_value = [
            {"id": self.mock_modem_id, "port": self.mock_modem_port, "status": "NOT_DETECTED", "phone_number": "N/A"}
        ] # Simulate no responsive modems
        
        payload = {
            "action": "GET_NUMBER",
            "key": TEST_PROTOCOL_KEY,
            "country": CONFIG_COUNTRY,
            "service": ENABLED_SERVICE,
            "operator": CONFIG_OPERATOR,
            "sum": 10.0,
            "currency": 643
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertEqual(data['status'], "NO_NUMBERS")

    def test_get_number_service_not_enabled(self):
        # Assuming 'nonexistentservice' is not in config.services or is false
        payload = {
            "action": "GET_NUMBER",
            "key": TEST_PROTOCOL_KEY,
            "country": CONFIG_COUNTRY,
            "service": "nonexistentservice", 
            "operator": CONFIG_OPERATOR,
            "sum": 10.0,
            "currency": 643
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertEqual(data['status'], "NO_NUMBERS")
        self.assertIn("not enabled", data.get("error", "").lower())


    def test_get_number_country_operator_mismatch(self):
        payload = {
            "action": "GET_NUMBER",
            "key": TEST_PROTOCOL_KEY,
            "country": "wrongcountry",
            "service": ENABLED_SERVICE,
            "operator": CONFIG_OPERATOR,
            "sum": 10.0,
            "currency": 643
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertEqual(data['status'], "NO_NUMBERS") # As per current main.py logic
        self.assertIn("country/operator mismatch", data.get("error", "").lower())

    @patch('smshub_app.app.main.get_modem_status')
    def test_get_number_with_exception_phone_set(self, mock_get_modem_status):
        mock_get_modem_status.return_value = [
            {"id": self.mock_modem_id, "port": self.mock_modem_port, "status": "RESPONSIVE", "phone_number": self.mock_modem_phone_number}
        ]
        
        payload = {
            "action": "GET_NUMBER",
            "key": TEST_PROTOCOL_KEY,
            "country": CONFIG_COUNTRY,
            "service": ENABLED_SERVICE,
            "operator": CONFIG_OPERATOR,
            "sum": 10.0,
            "currency": 643,
            "exceptionPhoneSet": [self.mock_modem_phone_number[:4]] # Exclude our only modem's prefix
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertEqual(data['status'], "NO_NUMBERS")


    # --- Test FINISH_ACTIVATION ---
    @patch('smshub_app.app.main.get_modem_status') # Needed for GET_NUMBER part of setup
    def test_finish_activation_success(self, mock_get_modem_status_for_get_number):
        mock_get_modem_status_for_get_number.return_value = [
            {"id": self.mock_modem_id, "port": self.mock_modem_port, "status": "RESPONSIVE", "phone_number": self.mock_modem_phone_number}
        ]
        
        # First, create an activation by calling GET_NUMBER
        get_number_payload = {
            "action": "GET_NUMBER", "key": TEST_PROTOCOL_KEY, "country": CONFIG_COUNTRY,
            "service": ENABLED_SERVICE, "operator": CONFIG_OPERATOR, "sum": 1.0, "currency": 643
        }
        response_get_number = self._make_api_call(get_number_payload)
        data_get_number = response_get_number.get_json()
        self.assertEqual(data_get_number['status'], "SUCCESS")
        activation_id = data_get_number['activationId']

        # Now, finish it
        finish_payload = {
            "action": "FINISH_ACTIVATION",
            "key": TEST_PROTOCOL_KEY,
            "activationId": activation_id,
            "status": 3 # Successfully sold
        }
        response_finish = self._make_api_call(finish_payload)
        self.assertEqual(response_finish.status_code, 200)
        data_finish = response_finish.get_json()
        self.assertEqual(data_finish['status'], "SUCCESS")

        # Verify in DB
        with app.app_context():
            with app.app_context():
                activation = get_activation_by_id(activation_id)
                self.assertIsNotNone(activation, "Activation record should exist in DB after finishing")
                if activation: # Satisfy Pylance
                    self.assertEqual(activation['smshub_reported_status'], 3)
                    self.assertEqual(activation['status_in_our_system'], "COMPLETED_SMSHUB_SOLD")
    def test_finish_activation_non_existent_id(self):
        payload = {
            "action": "FINISH_ACTIVATION",
            "key": TEST_PROTOCOL_KEY,
            "activationId": 999999, # Non-existent
            "status": 3
        }
        response = self._make_api_call(payload)
        self.assertEqual(response.status_code, 200) # Handler returns 200 with error in JSON
        data = response.get_json()
        self.assertEqual(data['status'], "ERROR")
        self.assertIn("not found", data.get("error", "").lower())

    @patch('smshub_app.app.main.get_modem_status')
    def test_finish_activation_idempotency(self, mock_get_modem_status_for_get_number):
        mock_get_modem_status_for_get_number.return_value = [
            {"id": self.mock_modem_id, "port": self.mock_modem_port, "status": "RESPONSIVE", "phone_number": self.mock_modem_phone_number}
        ]
        get_number_payload = {
            "action": "GET_NUMBER", "key": TEST_PROTOCOL_KEY, "country": CONFIG_COUNTRY,
            "service": ENABLED_SERVICE, "operator": CONFIG_OPERATOR, "sum": 1.0, "currency": 643
        }
        response_get_number = self._make_api_call(get_number_payload)
        activation_id = response_get_number.get_json()['activationId']

        finish_payload = {
            "action": "FINISH_ACTIVATION", "key": TEST_PROTOCOL_KEY,
            "activationId": activation_id, "status": 3
        }
        # Call once
        self._make_api_call(finish_payload)
        # Call again
        response_finish_again = self._make_api_call(finish_payload)
        self.assertEqual(response_finish_again.status_code, 200)
        data_finish_again = response_finish_again.get_json()
        self.assertEqual(data_finish_again['status'], "SUCCESS") # Should still be success

    # --- Test PUSH_SMS (Outgoing call from our app) ---
    # This requires mocking requests.post
    @patch('smshub_app.app.main.requests.post')
    @patch('smshub_app.app.main.get_modem_status') # For get_open_activation_for_modem
    def test_forward_sms_message_success(self, mock_get_modem_status_dep, mock_post):
        actual_message_db_id = None
        message_content_for_db = {
            'modem_port': self.mock_modem_id,
            'sender': 'VK_SENDER',
            'timestamp_device': '2024-05-27T10:00:00', # Example timestamp
            'timestamp_received_app': '2024-05-27T10:00:01Z', # Example timestamp
            'message_body': 'Your VK code is 12345',
            'status_on_modem': 'REC READ',
            'forwarding_status': 'PENDING',
            'forwarding_attempts': 0
        }

        with app.app_context():
            # 1. Create the SMS message record that will be "forwarded"
            # add_message returns the ID of the newly inserted message or None
            actual_message_db_id = add_message(message_content_for_db)
            self.assertIsNotNone(actual_message_db_id, "add_message should return the new message ID")

            # 2. Create an open activation for the modem
            activation_id = create_activation(
                modem_port=self.mock_modem_id,
                phone_number=self.mock_modem_phone_number,
                service=ENABLED_SERVICE,
                country=CONFIG_COUNTRY,
                operator=CONFIG_OPERATOR
            )
            self.assertIsNotNone(activation_id, "Activation ID should be created for PUSH_SMS test setup")
        
        # Mock the response from SMSHub's PUSH_SMS endpoint
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "SUCCESS"}
        mock_post.return_value = mock_response

        # Simulate a message to be forwarded (this dict is passed to forward_sms_message)
        # Its 'id' must match an existing record in sms_messages table for update_forwarding_status to work.
        message_to_forward = {
            'id': actual_message_db_id, # Use the actual ID from the database
            'modem_port': message_content_for_db['modem_port'],
            'sender': message_content_for_db['sender'],
            'message_body': message_content_for_db['message_body'],
            'timestamp_device': message_content_for_db['timestamp_device'], # Not strictly needed by forward_sms_message but good for consistency
            'forwarding_status': message_content_for_db['forwarding_status'], # forward_sms_message reads this
            'forwarding_attempts': message_content_for_db['forwarding_attempts'] # forward_sms_message reads this
        }
        
        # Patch module-level config variables in smshub_app.app.main
        with patch('smshub_app.app.main.FORWARDING_ENABLED', True), \
             patch('smshub_app.app.main.FORWARDING_URL', "http://fake.smshub.server/api/push"), \
             patch('smshub_app.app.main.AGENT_API_KEY', "TEST_AGENT_KEY_FOR_PUSH"):

            from smshub_app.app.main import forward_sms_message
            
            with app.app_context(): # Ensure DB operations within forward_sms_message work
                forward_sms_message(message_to_forward)

        mock_post.assert_called_once()
        called_args, called_kwargs = mock_post.call_args
        # Assert against the value we patched FORWARDING_URL with
        self.assertEqual(called_args[0], "http://fake.smshub.server/api/push")
        
        sent_payload = called_kwargs['json']
        self.assertEqual(sent_payload['action'], "PUSH_SMS")
        # Assert against the value we patched AGENT_API_KEY with
        self.assertEqual(sent_payload['key'], "TEST_AGENT_KEY_FOR_PUSH")
        self.assertEqual(sent_payload['smsId'], message_to_forward['id'])
        self.assertEqual(sent_payload['phone'], int(self.mock_modem_phone_number)) # Phone number from activation
        self.assertEqual(sent_payload['phoneFrom'], message_to_forward['sender'])
        self.assertEqual(sent_payload['text'], message_to_forward['message_body'])
        
        # Verify DB update by checking the activation record
        with app.app_context():
            # Ensure activation_id is an int before passing to get_activation_by_id
            if activation_id is not None:
                activation = get_activation_by_id(activation_id)
                self.assertIsNotNone(activation, "Activation record should still exist")
                if activation: # Satisfy Pylance
                    self.assertEqual(activation['status_in_our_system'], "SMS_FORWARDED_AWAITING_HUB")
            else:
                self.fail("activation_id was None, cannot verify DB update for PUSH_SMS")


        # No need to manually restore config dict items as they were not directly changed for this test's logic
        # The patch context manager handles restoring the original module-level variables.


if __name__ == '__main__':
    unittest.main()