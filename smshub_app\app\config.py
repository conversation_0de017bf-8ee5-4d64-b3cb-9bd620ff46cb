"""
Enhanced configuration management with Pydantic validation and environment variable support.
"""
import os
import json
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

try:
    from pydantic import BaseSettings, Field, validator
except ImportError:
    # Fallback for when pydantic is not installed yet
    BaseSettings = object
    Field = lambda default=None, **kwargs: default
    def validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

logger = logging.getLogger(__name__)


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    
    # Database type: 'sqlite', 'postgresql', 'mysql'
    type: str = Field(default="sqlite", env="DB_TYPE")
    
    # SQLite settings
    sqlite_path: str = Field(default="sms_database.db", env="SQLITE_PATH")
    
    # PostgreSQL/MySQL settings
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    username: str = Field(default="", env="DB_USERNAME")
    password: str = Field(default="", env="DB_PASSWORD")
    database: str = Field(default="smshub", env="DB_NAME")
    
    # Connection pool settings
    pool_size: int = Field(default=10, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    
    @property
    def connection_string(self) -> str:
        """Generate database connection string based on type."""
        if self.type == "sqlite":
            return f"sqlite:///{self.sqlite_path}"
        elif self.type == "postgresql":
            return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.type == "mysql":
            return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        else:
            raise ValueError(f"Unsupported database type: {self.type}")


class RedisConfig(BaseSettings):
    """Redis configuration for message queues and caching."""
    
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: str = Field(default="", env="REDIS_PASSWORD")
    db: int = Field(default=0, env="REDIS_DB")
    
    # Connection pool settings
    max_connections: int = Field(default=50, env="REDIS_MAX_CONNECTIONS")
    socket_timeout: int = Field(default=5, env="REDIS_SOCKET_TIMEOUT")
    
    @property
    def connection_string(self) -> str:
        """Generate Redis connection string."""
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"


class ModemConfig(BaseSettings):
    """Modem management configuration."""
    
    scan_interval: int = Field(default=10, env="MODEM_SCAN_INTERVAL")
    max_concurrent_modems: int = Field(default=50, env="MAX_CONCURRENT_MODEMS")
    connection_timeout: float = Field(default=5.0, env="MODEM_CONNECTION_TIMEOUT")
    read_timeout: float = Field(default=1.0, env="MODEM_READ_TIMEOUT")
    write_timeout: float = Field(default=1.0, env="MODEM_WRITE_TIMEOUT")
    
    # Health check settings
    health_check_interval: int = Field(default=30, env="MODEM_HEALTH_CHECK_INTERVAL")
    max_failed_health_checks: int = Field(default=3, env="MODEM_MAX_FAILED_HEALTH_CHECKS")


class SMSConfig(BaseSettings):
    """SMS processing configuration."""
    
    # Forwarding settings
    forwarding_enabled: bool = Field(default=False, env="SMS_FORWARDING_ENABLED")
    forwarding_url: str = Field(default="", env="SMS_FORWARDING_URL")
    forwarding_max_attempts: int = Field(default=5, env="SMS_FORWARDING_MAX_ATTEMPTS")
    forwarding_retry_delay: int = Field(default=10, env="SMS_FORWARDING_RETRY_DELAY")
    forwarding_service_interval: int = Field(default=60, env="SMS_FORWARDING_SERVICE_INTERVAL")
    
    # Processing settings
    max_message_length: int = Field(default=1600, env="SMS_MAX_MESSAGE_LENGTH")
    batch_size: int = Field(default=10, env="SMS_BATCH_SIZE")
    
    # Queue settings
    queue_name: str = Field(default="sms_processing", env="SMS_QUEUE_NAME")
    dead_letter_queue: str = Field(default="sms_failed", env="SMS_DEAD_LETTER_QUEUE")


class MonitoringConfig(BaseSettings):
    """Monitoring and metrics configuration."""
    
    enabled: bool = Field(default=True, env="MONITORING_ENABLED")
    metrics_port: int = Field(default=8000, env="METRICS_PORT")
    health_check_port: int = Field(default=8001, env="HEALTH_CHECK_PORT")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    
    # Circuit breaker settings
    circuit_breaker_failure_threshold: int = Field(default=5, env="CIRCUIT_BREAKER_FAILURE_THRESHOLD")
    circuit_breaker_recovery_timeout: int = Field(default=30, env="CIRCUIT_BREAKER_RECOVERY_TIMEOUT")


class SMSHubConfig(BaseSettings):
    """Main application configuration."""
    
    # Server settings
    server_port: int = Field(default=5000, env="SERVER_PORT")
    app_host: str = Field(default="0.0.0.0", env="APP_HOST")
    debug_mode: bool = Field(default=False, env="DEBUG_MODE")
    
    # API settings
    smshub_api_key: str = Field(default="", env="SMSHUB_API_KEY")
    smshub_agent_id: str = Field(default="", env="SMSHUB_AGENT_ID")
    smshub_protocol_key: str = Field(default="", env="SMSHUB_PROTOCOL_KEY")
    smshub_server_url: str = Field(default="", env="SMSHUB_SERVER_URL")
    
    # Service settings
    service_country_name: str = Field(default="usaphysic", env="SERVICE_COUNTRY_NAME")
    service_operator_name: str = Field(default="any", env="SERVICE_OPERATOR_NAME")
    
    # Component configurations
    database: DatabaseConfig = DatabaseConfig()
    redis: RedisConfig = RedisConfig()
    modem: ModemConfig = ModemConfig()
    sms: SMSConfig = SMSConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    
    # Legacy support - services configuration
    services: Dict[str, bool] = Field(default_factory=dict)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


def load_legacy_config() -> Dict[str, Any]:
    """Load configuration from legacy config.json file."""
    current_dir = Path(__file__).parent
    config_file_path = current_dir.parent / 'config.json'
    
    try:
        with open(config_file_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            logger.info(f"Successfully loaded legacy configuration from {config_file_path}")
            return config
    except FileNotFoundError:
        logger.warning(f"Legacy configuration file not found at {config_file_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON from {config_file_path}: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error loading legacy configuration: {e}")
        return {}


def create_config() -> SMSHubConfig:
    """Create configuration instance with legacy support."""
    # Load legacy config first
    legacy_config = load_legacy_config()
    
    # Create environment variables from legacy config if they don't exist
    env_mapping = {
        'server_port': 'SERVER_PORT',
        'smshub_api_key': 'SMSHUB_API_KEY',
        'smshub_agent_id': 'SMSHUB_AGENT_ID',
        'smshub_protocol_key': 'SMSHUB_PROTOCOL_KEY',
        'smshub_server_url': 'SMSHUB_SERVER_URL',
        'service_country_name': 'SERVICE_COUNTRY_NAME',
        'service_operator_name': 'SERVICE_OPERATOR_NAME',
        'debug_mode': 'DEBUG_MODE',
        'log_level': 'LOG_LEVEL',
        'scan_interval': 'MODEM_SCAN_INTERVAL',
        'sms_forwarding_enabled': 'SMS_FORWARDING_ENABLED',
        'sms_forwarding_url': 'SMS_FORWARDING_URL',
        'sms_forwarding_max_attempts': 'SMS_FORWARDING_MAX_ATTEMPTS',
        'sms_forwarding_retry_delay_seconds': 'SMS_FORWARDING_RETRY_DELAY',
        'sms_forwarding_service_interval_seconds': 'SMS_FORWARDING_SERVICE_INTERVAL',
    }
    
    # Set environment variables from legacy config if not already set
    for legacy_key, env_key in env_mapping.items():
        if legacy_key in legacy_config and env_key not in os.environ:
            os.environ[env_key] = str(legacy_config[legacy_key])
    
    # Create the new config
    config = SMSHubConfig()
    
    # Set services from legacy config
    if 'services' in legacy_config:
        config.services = legacy_config['services']
    
    return config


# Global configuration instance
config = create_config()


# Backward compatibility function
def load_config() -> Dict[str, Any]:
    """Backward compatibility function that returns config as dict."""
    return config.dict()
