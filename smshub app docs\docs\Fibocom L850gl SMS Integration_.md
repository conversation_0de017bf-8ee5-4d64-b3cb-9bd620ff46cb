# **Integrating the Fibocom L850gl Modem for SMS Communication in Python Applications**

The Fibocom L850gl is a versatile multi-mode LTE and WCDMA module designed to provide high-speed cellular connectivity for a wide range of applications.1 Its capability to operate on both 4G LTE and 3G WCDMA networks ensures broad compatibility across different regions and network infrastructures. This report aims to provide a comprehensive guide for developers seeking to integrate the Fibocom L850gl modem into Python applications that require the functionality of sending and receiving Short Message Service (SMS) messages. The integration process involves understanding the modem's technical specifications, utilizing AT commands for control, installing the necessary drivers for the host operating system, implementing communication using Python libraries, and managing asynchronous events related to SMS reception.

## **Understanding the Fibocom L850gl Modem**

### **2.1 Key Specifications and Features**

The Fibocom L850gl modem adopts the M.2 form factor with dimensions of 30.0 x 42.0 x 2.3 mm, making it suitable for integration into modern laptops and embedded systems.1 It supports both PCIe and USB interfaces, offering flexibility in how it can be connected to the host system.3 While the default or preferred mode might vary depending on the specific system and operating system, some configurations, particularly on Linux, might necessitate switching between these modes to facilitate communication using standard serial tools.4

This modem supports a wide array of frequency bands across LTE FDD (B1/2/3/4/5/7/8/11/12/13/17/18/19/20/21/26/28/29/30/66), LTE TDD (B38/39/40/41), and WCDMA (B1/2/4/5/8) technologies.1 This extensive band support indicates its suitability for global deployment, potentially simplifying product development by allowing for a single hardware SKU for worldwide use.1

In terms of data transmission capabilities, the L850gl supports LTE Category 9, enabling high downlink speeds of up to 450 Mbps and uplink speeds of up to 50 Mbps on LTE FDD networks.2 LTE TDD supports downlink speeds of 347 Mbps and uplink speeds of 30 Mbps.1 Even though the primary focus of this report is on SMS, these high data rates suggest the modem's potential for applications involving data transfer in the future. The modem also provides fallback to WCDMA networks, supporting DC-HSPA+ with speeds up to 42 Mbps downlink and 5.76 Mbps uplink.1

Beyond cellular connectivity, the L850gl also includes support for GNSS (Global Navigation Satellite System) 1, which could be valuable for applications requiring location tracking capabilities, potentially expanding its utility beyond just SMS communication. Information regarding eSIM support appears inconsistent across different sources. While some specifications list eSIM as supported under general features, it is marked as NA (Not Applicable) under specific functions. The most reliable information on this would likely be found in the official datasheet.

### **2.2 Interface Modes (PCIe vs. USB)**

The Fibocom L850gl modem can operate using different interface modes, primarily PCIe and USB.4 On certain systems, particularly those running Linux, the modem might initially be configured in PCIe mode. For interaction using standard serial communication protocols and Python libraries like pyserial, it might be necessary to switch the modem to USB mode.4 Tools such as xmm7360-usb-modeswitch have been developed to facilitate this mode switching process.4 It's important to note that using such tools to bypass the intended communication mechanism might have regulatory implications, specifically concerning SAR (Specific Absorption Rate) limits, and this should be carefully considered during development.4

## **3\. Introduction to AT Commands**

AT commands are a standard set of text-based commands used to communicate with modems.12 These commands are sent over a serial interface to control various functionalities of the modem, including network registration, data connections, and SMS messaging. For the Fibocom L850gl, the AT command manual specific to this modem is crucial for detailed control over its features, including sending and receiving SMS messages.6 The existence of dedicated AT command manuals for the L8 series, which includes the L850gl, suggests that there might be specific commands or variations in command behavior compared to generic modem AT command sets.6 Several resources, such as those found on Techship's website and Scribd, provide access to these AT command manuals for the L8 series.6

## **4\. Setting Up the Development Environment**

### **4.1 Driver Installation**

**Windows:** For Windows operating systems to recognize and communicate with the Fibocom L850gl modem, specific drivers need to be installed. Lenovo's support website is a primary source for these drivers, particularly for ThinkPad laptops that often integrate this modem.19 Given that Lenovo provides these specific drivers, it suggests that they might include customizations or optimizations tailored for their hardware, making them the preferred choice for ThinkPad users. While generic Fibocom drivers might also be available on the Fibocom website, the provided research snippets do not directly link to them. It is worth noting that some users have reported issues with driver persistence after system reboots 23, indicating a potential conflict with Windows' automatic driver updates. In such cases, disabling automatic driver updates might be necessary to ensure the modem remains functional. Drivers can also be found on third-party websites like Treexy 24, but it is generally recommended to prioritize drivers from official sources to ensure stability and security.

**Linux:** The driver situation on Linux can be more complex. For the Fibocom L850gl, the xmm7360-pci driver available on GitHub is a significant community-driven project aimed at enabling the modem in PCIe mode.9 The existence of this community effort suggests that official Linux support directly from Fibocom might be limited or require specific configurations. This driver has dependencies such as build-essential, python3-pyroute2, and python3-configargparse 9, which need to be installed on the system. Additionally, the xmm7360-usb-modeswitch tool can be used to switch the modem to USB mode 4, which might be required to expose a standard serial interface for AT command communication. User experiences with these community drivers vary, with some reporting successful operation while others have encountered issues.5 Discussions on Linux forums provide valuable insights into user experiences and potential solutions for specific distributions like Ubuntu.5

### **4.2 Identifying the Serial Port**

**Windows:** In Windows, the Device Manager can be used to identify the COM port(s) assigned to the Fibocom L850gl modem. It is common for a cellular modem to have multiple COM ports, each dedicated to different functionalities. For the purpose of sending AT commands, the specific COM port designated for modem control needs to be identified.

**Linux:** On Linux systems, the device nodes corresponding to the modem's serial interface can be found using commands like ls /dev/tty\* and dmesg | grep tty.9 The naming convention for these device nodes might vary; for a modem in USB mode, it is often /dev/ttyUSBx or /dev/ttyACM0. If the modem is operating in PCIe mode with the xmm7360-pci driver, the device node might be different, such as /dev/wwan0at0.9 Some users have encountered issues with serial port timeouts on Linux, particularly with the xmm7360-pci driver, and have found that using the open\_xdatachannel.py script from the xmm7360-pci library can resolve these issues.34

### **4.3 Installing the pyserial Library**

The pyserial library is a widely used Python package that provides the necessary tools for serial communication.36 It supports Python versions 2.7 and 3.4 or later and is compatible with various operating systems, including Windows, Linux, and macOS.36 To install pyserial, the following command can be executed using pip, the Python package installer: pip install pyserial.36

## **5\. Sending SMS Messages with AT Commands in Python**

### **5.1 Essential AT Commands for Sending SMS**

To send SMS messages using the Fibocom L850gl modem, a specific set of AT commands is required.12 The first crucial command is AT+CMGF=1, which sets the SMS format to text mode. This mode is generally preferred for its ease of use in sending and reading SMS messages as the content is represented in a human-readable format. Optionally, the character set for SMS can be set using the command AT+CSCS="GSM" 12, where "GSM" is a common and widely supported character set. Setting the character set ensures that the SMS message is encoded and decoded correctly, especially when it contains special characters.

The command to initiate sending an SMS is AT+CMGS="\<phone\_number\>".12 The recipient's phone number must be provided in the international format, for example, "+1XXXXXXXXXX" for a US number. Using the correct international format is essential for reliable message delivery. After sending this command, the modem will expect the text content of the SMS message. Once the message text has been sent, the sending process is finalized by sending a Ctrl+Z character (ASCII value 26). This character acts as an end-of-message indicator, prompting the modem to attempt to send the SMS.

The following table summarizes the essential AT commands for sending SMS messages:

| AT Command | Syntax | Description |
| :---- | :---- | :---- |
| AT+CMGF | AT+CMGF=\<mode\> | Sets the SMS message format; \<mode\> is 0 for PDU mode, 1 for text mode. |
| AT+CMGS | AT+CMGS="\<phone\_number\>" | Specifies the recipient's phone number for sending an SMS. |
| \<SMS\_text\> |  | The actual content of the SMS message. |
| Ctrl+Z |  | Terminates the SMS message input and initiates sending. |
| AT+CSCS | AT+CSCS="\<charset\>" | Sets the character set for SMS messages (e.g., "GSM", "UCS2"). |

### **5.2 Python Code Example**

Python

import serial  
import time

def send\_sms(port, baudrate, phone\_number, message):  
    try:  
        ser \= serial.Serial(port, baudrate, timeout=1)  
        print(f"Serial port {port} opened successfully.")

        ser.write(b'AT+CMGF=1\\r')  
        time.sleep(0.1)  
        print(ser.read\_all().decode())

        ser.write(f'AT+CMGS="{phone\_number}"\\r'.encode())  
        time.sleep(0.1)  
        print(ser.read\_all().decode())

        ser.write(message.encode() \+ b'\\r')  
        time.sleep(0.1)  
        ser.write(bytes())  \# Ctrl+Z  
        time.sleep(0.5)  
        response \= ser.read\_all().decode()  
        print(response)

        if "OK" in response:  
            print("SMS sent successfully.")  
        else:  
            print("Failed to send SMS.")

    except serial.SerialException as e:  
        print(f"Error communicating with serial port: {e}")  
    finally:  
        if ser.is\_open:  
            ser.close()  
            print(f"Serial port {port} closed.")

if \_\_name\_\_ \== '\_\_main\_\_':  
    serial\_port \= '/dev/ttyUSB0'  \# Replace with the correct serial port for your modem  
    baud\_rate \= 115200           \# Adjust baud rate if necessary  
    recipient\_number \= '+15551234567' \# Replace with the recipient's phone number  
    sms\_message \= 'Hello from Python\!'

    send\_sms(serial\_port, baud\_rate, recipient\_number, sms\_message)

This Python code snippet demonstrates the basic steps to send an SMS message using the pyserial library. It first opens the specified serial port with the given baud rate. Then, it sends the AT+CMGF=1 command to set the SMS format to text mode. Following this, it sends the AT+CMGS command with the recipient's phone number. The SMS message content is then written to the serial port, followed by the Ctrl+Z character (ASCII 26\) to initiate sending. The code then reads the modem's response to check if the SMS was sent successfully. Error handling is included to catch potential issues with serial port communication, and the serial port is closed in the finally block to ensure proper resource management. The serial port (serial\_port) needs to be replaced with the actual serial port identified for the Fibocom L850gl modem on the system, and the baud\_rate should be adjusted if required by the modem's configuration.

## **6\. Receiving and Reading SMS Messages with AT Commands in Python**

### **6.1 Essential AT Commands for Receiving SMS**

Receiving SMS messages with the Fibocom L850gl involves configuring the modem to indicate the arrival of new messages and then retrieving those messages from the modem's storage.12 The AT+CNMI command is used to configure how the modem should notify the host application about new incoming SMS messages. For example, the command AT+CNMI=2,2,0,0,0 configures the modem to buffer new SMS messages and send an Unsolicited Result Code (URC) when a new message is received. Different configurations of AT+CNMI can be used depending on the desired behavior.

To retrieve received SMS messages, the AT+CMGL command is used. The syntax AT+CMGL="REC UNREAD" reads all unread SMS messages stored in the modem's memory. Other status options like "REC READ" to read already read messages or "ALL" to list all messages can also be used. The AT+CMGL command returns a list of messages along with their index number in the modem's storage, sender's phone number, and timestamp. To read the content of a specific SMS message, the AT+CMGR=\<index\> command is used, where \<index\> is the index number of the message obtained from the AT+CMGL command. After reading a message, it can be deleted from the modem's storage using the AT+CMGD=\<index\> command to prevent the modem's memory from becoming full.

The following table summarizes the essential AT commands for receiving SMS messages:

| AT Command | Syntax | Description |
| :---- | :---- | :---- |
| AT+CNMI | AT+CNMI=\<mode1\>,\<mode2\>,... | Configures how the modem indicates new SMS messages. |
| AT+CMGL | AT+CMGL="\<status\>" | Lists SMS messages with the specified status (e.g., "REC UNREAD", "REC READ", "ALL"). |
| AT+CMGR | AT+CMGR=\<index\> | Reads the SMS message at the specified index in the modem's storage. |
| AT+CMGD | AT+CMGD=\<index\> | Deletes the SMS message at the specified index from the modem's storage. |

### **6.2 Python Code Example**

Python

import serial  
import time  
import re

def receive\_sms(port, baudrate):  
    try:  
        ser \= serial.Serial(port, baudrate, timeout=1)  
        print(f"Serial port {port} opened successfully.")

        ser.write(b'AT+CMGF=1\\r')  
        time.sleep(0.1)  
        print(ser.read\_all().decode())

        ser.write(b'AT+CNMI=2,2,0,0,0\\r') \# Configure to buffer new messages and send URC  
        time.sleep(0.1)  
        print(ser.read\_all().decode())

        while True:  
            if ser.in\_waiting \> 0:  
                line \= ser.readline().decode().strip()  
                print(f"Received: {line}")  
                if "+CMTI:" in line: \# New message indication  
                    parts \= line.split(",")  
                    if len(parts) \> 1:  
                        index \= parts.strip()  
                        read\_sms(ser, index)  
            time.sleep(1)

    except serial.SerialException as e:  
        print(f"Error communicating with serial port: {e}")  
    finally:  
        if ser.is\_open:  
            ser.close()  
            print(f"Serial port {port} closed.")

def read\_sms(ser, index):  
    ser.write(f'AT+CMGR={index}\\r'.encode())  
    time.sleep(0.5)  
    response \= ser.read\_all().decode()  
    print(response)  
    match \= re.search(r'\\+CMGR: "REC UNREAD",".\*?",".\*?",,"(.\*?)"\\r\\n(.\*?)\\r\\n', response, re.DOTALL)  
    if match:  
        sender \= match.group(1)  
        content \= match.group(2)  
        print(f"New SMS from: {sender}\\nContent: {content}")  
        delete\_sms(ser, index)

def delete\_sms(ser, index):  
    ser.write(f'AT+CMGD={index}\\r'.encode())  
    time.sleep(0.1)  
    print(ser.read\_all().decode())

if \_\_name\_\_ \== '\_\_main\_\_':  
    serial\_port \= '/dev/ttyUSB0'  \# Replace with the correct serial port for your modem  
    baud\_rate \= 115200           \# Adjust baud rate if necessary

    receive\_sms(serial\_port, baud\_rate)

This Python code snippet demonstrates how to configure the modem to receive SMS messages and how to read them. It first sets the SMS format to text mode and then configures the new message indication using AT+CNMI. The receive\_sms function then enters a loop, continuously checking for incoming data on the serial port. When a URC indicating a new message (+CMTI:) is received, it extracts the index of the new message and calls the read\_sms function. The read\_sms function uses the AT+CMGR command to read the message at the given index and then parses the response to extract the sender's phone number and the message content using regular expressions. After reading, it calls the delete\_sms function to delete the message from the modem's storage using the AT+CMGD command. The serial port and baud rate should be adjusted to match the modem's configuration.

## **7\. Handling Asynchronous SMS Reception in Python**

Constantly polling the serial port for new messages can be inefficient. A more effective approach is to handle the asynchronous event of receiving an SMS.

**Threading:** One method is to use threading. A separate thread can be created to continuously monitor the serial port for incoming data. When the modem sends a URC (Unsolicited Result Code) indicating a new SMS message (e.g., \+CMTI:), this thread can then handle the reception and processing of the new message. This allows the main application to continue its other tasks without being blocked while waiting for SMS messages.

**Asynchronous I/O with asyncio:** If the Python application is already using asyncio for other asynchronous operations, integrating serial communication using libraries like aioserial can be a good option. aioserial provides asynchronous versions of the pyserial functions, allowing for non-blocking I/O operations on the serial port. This approach can be more efficient than threading in certain scenarios, especially when dealing with multiple concurrent I/O-bound tasks.

Below is an example using threading to handle asynchronous SMS reception:

Python

import serial  
import time  
import threading  
import re

class SMSReceiver(threading.Thread):  
    def \_\_init\_\_(self, port, baudrate):  
        threading.Thread.\_\_init\_\_(self)  
        self.port \= port  
        self.baudrate \= baudrate  
        self.serial\_connection \= None  
        self.running \= True

    def run(self):  
        try:  
            self.serial\_connection \= serial.Serial(self.port, self.baudrate, timeout=1)  
            print(f"Serial port {self.port} opened in thread.")  
            self.serial\_connection.write(b'AT+CMGF=1\\r')  
            time.sleep(0.1)  
            print(self.serial\_connection.read\_all().decode())  
            self.serial\_connection.write(b'AT+CNMI=2,2,0,0,0\\r') \# Configure to buffer new messages and send URC  
            time.sleep(0.1)  
            print(self.serial\_connection.read\_all().decode())

            while self.running:  
                if self.serial\_connection.in\_waiting \> 0:  
                    line \= self.serial\_connection.readline().decode().strip()  
                    print(f"Thread Received: {line}")  
                    if "+CMTI:" in line: \# New message indication  
                        parts \= line.split(",")  
                        if len(parts) \> 1:  
                            index \= parts.strip()  
                            self.read\_sms(index)  
                time.sleep(0.1)

        except serial.SerialException as e:  
            print(f"Serial port error in thread: {e}")  
        finally:  
            if self.serial\_connection and self.serial\_connection.is\_open:  
                self.serial\_connection.close()  
                print(f"Serial port {self.port} closed in thread.")

    def read\_sms(self, index):  
        self.serial\_connection.write(f'AT+CMGR={index}\\r'.encode())  
        time.sleep(0.5)  
        response \= self.serial\_connection.read\_all().decode()  
        print(response)  
        match \= re.search(r'\\+CMGR: "REC UNREAD",".\*?",".\*?",,"(.\*?)"\\r\\n(.\*?)\\r\\n', response, re.DOTALL)  
        if match:  
            sender \= match.group(1)  
            content \= match.group(2)  
            print(f"New SMS from: {sender}\\nContent: {content}")  
            self.delete\_sms(index)

    def delete\_sms(self, index):  
        self.serial\_connection.write(f'AT+CMGD={index}\\r'.encode())  
        time.sleep(0.1)  
        print(self.serial\_connection.read\_all().decode())

    def stop(self):  
        self.running \= False

if \_\_name\_\_ \== '\_\_main\_\_':  
    serial\_port \= '/dev/ttyUSB0'  \# Replace with the correct serial port for your modem  
    baud\_rate \= 115200           \# Adjust baud rate if necessary

    receiver\_thread \= SMSReceiver(serial\_port, baud\_rate)  
    receiver\_thread.start()

    try:  
        while True:  
            time.sleep(1)  
            \# Main application logic can go here  
    except KeyboardInterrupt:  
        print("Stopping SMS receiver thread...")  
        receiver\_thread.stop()  
        receiver\_thread.join()  
        print("SMS receiver thread stopped.")

This example creates a separate thread using the SMSReceiver class. The run method of this thread initializes the serial connection, configures the modem for SMS reception, and then continuously monitors the serial port for incoming URCs. When a new message indication is received, it calls the read\_sms method to retrieve and process the message. The main part of the script starts this thread and can continue with other application logic. The thread is stopped gracefully when a KeyboardInterrupt occurs.

## **8\. Advanced Considerations and Best Practices**

After powering up the modem, it is advisable to send initial AT commands to configure it properly. This might include basic commands like AT to test the connection and AT+CPIN? to check the status of the SIM card's PIN. Robust error handling is crucial in the Python application to manage potential issues such as serial port errors, failures in executing AT commands, and unexpected responses from the modem. Different encoding schemes are used for SMS messages, such as GSM 7-bit and UCS-2, especially for handling different languages. The Python application should be capable of encoding outgoing messages in the appropriate format and decoding incoming messages correctly. It's also important to be aware that some AT commands might require a specific delay before the modem responds, and the Python application should account for these timings to avoid communication errors. Finally, when dealing with SMS data, especially in applications exposed to networks, security considerations should be kept in mind to protect the sensitivity of the information.

## **9\. Troubleshooting Common Issues**

If the modem is not being detected by the operating system or pyserial, the first step is to check if the necessary drivers are installed correctly.33 On Linux, it's important to verify the modem's operating mode (PCIe or USB) and ensure that the correct device node is being used. Tools like xmm7360-pci and xmm7360-usb-modeswitch might be necessary for proper configuration. If AT commands are not responding, verify the serial port settings (baud rate, parity, stop bits, etc.) in the Python code and ensure they match the modem's configuration. Also, double-check that the correct COM port or device node is being used. For SMS sending failures, ensure that the recipient's phone number is in the correct international format and that there are no network issues. Verify the sequence of AT commands used for sending. If SMS messages are not being received, check the AT+CNMI configuration to ensure the modem is set up to indicate new messages correctly. Also, check if the modem's storage is full, which might prevent new messages from being received. For Linux users experiencing issues with the xmm7360-pci driver, refer to community forums and the GitHub repository for specific troubleshooting steps, such as issues related to SIM card detection and the use of scripts like open\_xdatachannel.py.34

## **10\. Conclusion**

Integrating the Fibocom L850gl modem into a Python application for sending and receiving SMS messages involves a series of steps, starting with understanding the modem's capabilities and specifications. The process includes installing the appropriate drivers for the operating system, identifying the correct serial port for communication, utilizing the pyserial library in Python to interact with the modem, and employing the necessary AT commands to send and receive SMS messages. For asynchronous reception of SMS, techniques like threading or asyncio can be implemented to efficiently handle incoming messages. By following these guidelines and considering the advanced aspects and troubleshooting tips, developers can successfully incorporate SMS functionality into their Python applications using the Fibocom L850gl modem. This integration can enable a wide range of applications, from automated notification systems to IoT devices requiring two-way SMS communication.

#### **Works cited**

1. L850-GL \- Fibocom, accessed May 1, 2025, [https://www.fibocom.com/en/Products/-L850-GL.html](https://www.fibocom.com/en/Products/-L850-GL.html)  
2. The Fibocom's L850-GL is a multimode LTE & WCDMA module \- Richardson RFPD, accessed May 1, 2025, [https://shop.richardsonrfpd.com/docs/rfpd/L850-GL.pdf](https://shop.richardsonrfpd.com/docs/rfpd/L850-GL.pdf)  
3. FIBOCOM L850-GL Series Hardware Guide \- FCC Report, accessed May 1, 2025, [https://fcc.report/FCC-ID/ZMOL850GLD-D2/4827208.pdf](https://fcc.report/FCC-ID/ZMOL850GLD-D2/4827208.pdf)  
4. Tools for the Fibocom L850-GL / Intel XMM7360 LTE modem \- GitHub, accessed May 1, 2025, [https://github.com/xmm7360/xmm7360-usb-modeswitch](https://github.com/xmm7360/xmm7360-usb-modeswitch)  
5. Fibocom-L850-GL-on-Ubuntu-Linux \- LENOVO COMMUNITY, accessed May 1, 2025, [https://forums.lenovo.com/t5/ThinkPad-X-Series-Laptops/Fibocom-L850-GL-on-Ubuntu-Linux/m-p/5026326](https://forums.lenovo.com/t5/ThinkPad-X-Series-Laptops/Fibocom-L850-GL-on-Ubuntu-Linux/m-p/5026326)  
6. Fibocom L850-GL-10 LTE CAT-9 M.2 | 4G LTE-A \- Techship, accessed May 1, 2025, [https://techship.com/product/fibocom-l850-gl-10-lte-cat-9-m2/?variant=001](https://techship.com/product/fibocom-l850-gl-10-lte-cat-9-m2/?variant=001)  
7. ThinkPad Fibocom L850-GL CAT9 M.2 WWAN | 4XC0R38452 | Lenovo US, accessed May 1, 2025, [https://www.lenovo.com/us/en/p/accessories-and-software/mobile-broadband/4g-lte/4xc0r38452](https://www.lenovo.com/us/en/p/accessories-and-software/mobile-broadband/4g-lte/4xc0r38452)  
8. Re: Yoga X380 \- How To Enable GPS Function On Fibocom L850-GL WAN Card, accessed May 1, 2025, [https://forums.lenovo.com/t5/ThinkPad-X-Series-Laptops/Yoga-X380-How-To-Enable-GPS-Function-On-Fibocom-L850-GL-WAN-Card/m-p/4624840](https://forums.lenovo.com/t5/ThinkPad-X-Series-Laptops/Yoga-X380-How-To-Enable-GPS-Function-On-Fibocom-L850-GL-WAN-Card/m-p/4624840)  
9. PCI driver for Fibocom L850-GL modem based on Intel XMM7360 modem \- GitHub, accessed May 1, 2025, [https://github.com/xmm7360/xmm7360-pci](https://github.com/xmm7360/xmm7360-pci)  
10. Has anyone used L850-gl 4G Card with mikrotik?, accessed May 1, 2025, [https://forum.mikrotik.com/viewtopic.php?t=178603](https://forum.mikrotik.com/viewtopic.php?t=178603)  
11. juhovh/xmm7360\_usb: Kernel module for Fibocom L850-GL / Intel XMM7360 LTE modem, accessed May 1, 2025, [https://github.com/juhovh/xmm7360\_usb](https://github.com/juhovh/xmm7360_usb)  
12. L850-GL Hardware User Manual \- FCC Report, accessed May 1, 2025, [https://fcc.report/FCC-ID/ZMOL850GLDD1/4169358.pdf](https://fcc.report/FCC-ID/ZMOL850GLDD1/4169358.pdf)  
13. FIBOCOM NL668 AT Commands User Manual \- Mcuzone, accessed May 1, 2025, [http://www.mcuzone.com/datasheet/FIBOCOM\_NL668\_AT\_Commands\_User\_Manual\_V3.5.14.pdf](http://www.mcuzone.com/datasheet/FIBOCOM_NL668_AT_Commands_User_Manual_V3.5.14.pdf)  
14. Fibocom L8 Series AT Commands User Manual V2.0.2 | Downloads \- Techship, accessed May 1, 2025, [https://techship.com/downloads/fibocom-l8-series-at-commands-user-manual-v202/](https://techship.com/downloads/fibocom-l8-series-at-commands-user-manual-v202/)  
15. Fibocom L8-Family at Commands User Manual | PDF \- Scribd, accessed May 1, 2025, [https://www.scribd.com/document/699981729/Fibocom-L8-Family-at-Commands-User-Manual](https://www.scribd.com/document/699981729/Fibocom-L8-Family-at-Commands-User-Manual)  
16. L850 AT Commands User Manual | Downloads \- Techship, accessed May 1, 2025, [https://techship.com/downloads/l850-at-commands-user-manual/](https://techship.com/downloads/l850-at-commands-user-manual/)  
17. L8-Family AT Commands User Manual : Free Download, Borrow, and Streaming, accessed May 1, 2025, [https://archive.org/details/manualzilla-id-5706876](https://archive.org/details/manualzilla-id-5706876)  
18. Fibocom L860-GL AT Commands User Manual V.3.2.3 | Downloads \- Techship, accessed May 1, 2025, [https://techship.com/downloads/fibocom-l860-gl-at-commands-user-manual-v323/](https://techship.com/downloads/fibocom-l860-gl-at-commands-user-manual-v323/)  
19. Fibocom L850-GL Wireless WAN Driver for Windows 11 (Version 21H2 or later), 10 (Version 1709 or later) \- ThinkPad \- Lenovo Support, accessed May 1, 2025, [https://support.lenovo.com/us/en/downloads/ds503062-fibocom-l850-gl-wireless-wan-driver-for-windows-10-version-1709-or-later-thinkpad](https://support.lenovo.com/us/en/downloads/ds503062-fibocom-l850-gl-wireless-wan-driver-for-windows-10-version-1709-or-later-thinkpad)  
20. Fibocom L850-GL Wireless WAN Driver and DPR Utility Package for Windows 11 (Version 21H2 or later), 10 (Version 1709 or later) \- ThinkPad X1 Carbon 7th Gen \- Lenovo Support, accessed May 1, 2025, [https://support.lenovo.com/us/en/downloads/ds542420-fibocom-l850-gl-wireless-wan-driver-and-dpr-utility-package-for-windows-10-version-1709-or-later-thinkpad-x1-carbon-7th-gen-types-20qd-20qe-20r1-20r2](https://support.lenovo.com/us/en/downloads/ds542420-fibocom-l850-gl-wireless-wan-driver-and-dpr-utility-package-for-windows-10-version-1709-or-later-thinkpad-x1-carbon-7th-gen-types-20qd-20qe-20r1-20r2)  
21. Fibocom L850-GL Wireless WAN Driver for Windows 11 (Version 21H2 or later), 10 (Version 1709 or later) \- ThinkPad \- Lenovo Support, accessed May 1, 2025, [https://support.lenovo.com/us/en/downloads/ds540197-fibocom-l850-gl-wireless-wan-driver-for-windows-10-version-1709-or-later-thinkpad](https://support.lenovo.com/us/en/downloads/ds540197-fibocom-l850-gl-wireless-wan-driver-for-windows-10-version-1709-or-later-thinkpad)  
22. Fibocom L850-GL Wireless WAN Driver for Windows 11 (Version 21H2 or later), 10 (Version 1709 or later) \- ThinkPad \- Lenovo Support, accessed May 1, 2025, [https://support.lenovo.com/bd/en/downloads/ds503062-fibocom-l850-gl-wireless-wan-driver-for-windows-10-version-1709-or-later-thinkpad](https://support.lenovo.com/bd/en/downloads/ds503062-fibocom-l850-gl-wireless-wan-driver-for-windows-10-version-1709-or-later-thinkpad)  
23. Fibocom WAN (Cellular) modem driver: auto-reinstall issue on Yoga X390/Win11 \- Reddit, accessed May 1, 2025, [https://www.reddit.com/r/techsupport/comments/1du6eac/fibocom\_wan\_cellular\_modem\_driver\_autoreinstall/](https://www.reddit.com/r/techsupport/comments/1du6eac/fibocom_wan_cellular_modem_driver_autoreinstall/)  
24. Lenovo Fibocom L850-GL extension drivers \- Treexy, accessed May 1, 2025, [https://treexy.com/products/driver-fusion/database/extensions/lenovo/fibocom-l850-gl/](https://treexy.com/products/driver-fusion/database/extensions/lenovo/fibocom-l850-gl/)  
25. Lenovo Fibocom L850-GL L860-GL extension drivers \- Treexy, accessed May 1, 2025, [https://treexy.com/products/driver-fusion/database/extensions/lenovo/fibocom-l850-gl-l860-gl/](https://treexy.com/products/driver-fusion/database/extensions/lenovo/fibocom-l850-gl-l860-gl/)  
26. Fibocom L850-GL on Linux obviously : r/LinuxOnThinkpad \- Reddit, accessed May 1, 2025, [https://www.reddit.com/r/LinuxOnThinkpad/comments/j178it/fibocom\_l850gl\_on\_linux\_obviously/](https://www.reddit.com/r/LinuxOnThinkpad/comments/j178it/fibocom_l850gl_on_linux_obviously/)  
27. Xmm7360-pci \- ArchWiki, accessed May 1, 2025, [https://wiki.archlinux.org/title/Xmm7360-pci](https://wiki.archlinux.org/title/Xmm7360-pci)  
28. Fibocom L850 GL on Linux : r/Lenovo \- Reddit, accessed May 1, 2025, [https://www.reddit.com/r/Lenovo/comments/q6fynl/fibocom\_l850\_gl\_on\_linux/](https://www.reddit.com/r/Lenovo/comments/q6fynl/fibocom_l850_gl_on_linux/)  
29. Working WWAN on Linux. (Fibocom L850-GL) : r/thinkpad \- Reddit, accessed May 1, 2025, [https://www.reddit.com/r/thinkpad/comments/dshqzz/working\_wwan\_on\_linux\_fibocom\_l850gl/](https://www.reddit.com/r/thinkpad/comments/dshqzz/working_wwan_on_linux_fibocom_l850gl/)  
30. Fibocom L850-GL installation on Ubuntu 18.04, accessed May 1, 2025, [https://askubuntu.com/questions/1091315/fibocom-l850-gl-installation-on-ubuntu-18-04](https://askubuntu.com/questions/1091315/fibocom-l850-gl-installation-on-ubuntu-18-04)  
31. WWAN-support-for-Linux-Fibocom-modem-drivers \- LENOVO COMMUNITY, accessed May 1, 2025, [https://forums.lenovo.com/t5/ThinkPad-X-Series-Laptops/WWAN-support-for-Linux-Fibocom-modem-drivers/m-p/5152397](https://forums.lenovo.com/t5/ThinkPad-X-Series-Laptops/WWAN-support-for-Linux-Fibocom-modem-drivers/m-p/5152397)  
32. Fibocom L850-GL installation on Ubuntu 20.04 or 21.10, accessed May 1, 2025, [https://askubuntu.com/questions/1385496/fibocom-l850-gl-installation-on-ubuntu-20-04-or-21-10](https://askubuntu.com/questions/1385496/fibocom-l850-gl-installation-on-ubuntu-20-04-or-21-10)  
33. GL-M2 DEV Board with Fibocom L850-GL or Sierra Airprime em9191 \- GL.iNet Forum, accessed May 1, 2025, [https://forum.gl-inet.com/t/gl-m2-dev-board-with-fibocom-l850-gl-or-sierra-airprime-em9191/49940](https://forum.gl-inet.com/t/gl-m2-dev-board-with-fibocom-l850-gl-or-sierra-airprime-em9191/49940)  
34. Modem Fibocom L850-GL on ThinkPad X1 Carbon not detecting SIM on Arch Linux (ModemManager serial command timeout) \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/79051900/modem-fibocom-l850-gl-on-thinkpad-x1-carbon-not-detecting-sim-on-arch-linux-mod](https://stackoverflow.com/questions/79051900/modem-fibocom-l850-gl-on-thinkpad-x1-carbon-not-detecting-sim-on-arch-linux-mod)  
35. L850GL in a Lenovo ThinkPad X1 Yoga 20LD... with Manjaro Linux · Issue \#209 \- GitHub, accessed May 1, 2025, [https://github.com/xmm7360/xmm7360-pci/issues/209](https://github.com/xmm7360/xmm7360-pci/issues/209)  
36. pyserial · PyPI, accessed May 1, 2025, [https://pypi.org/project/pyserial/](https://pypi.org/project/pyserial/)  
37. pyserial/pyserial: Python serial port access library \- GitHub, accessed May 1, 2025, [https://github.com/pyserial/pyserial](https://github.com/pyserial/pyserial)  
38. Full examples of using pySerial package \- python \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/676172/full-examples-of-using-pyserial-package](https://stackoverflow.com/questions/676172/full-examples-of-using-pyserial-package)  
39. pySerial 3.4 documentation, accessed May 1, 2025, [https://pyserial.readthedocs.io/en/latest/pyserial.html](https://pyserial.readthedocs.io/en/latest/pyserial.html)  
40. Short introduction — pySerial 3.4 documentation, accessed May 1, 2025, [https://pyserial.readthedocs.io/en/latest/shortintro.html](https://pyserial.readthedocs.io/en/latest/shortintro.html)  
41. Details on getting SERIAL \- Python discussion forum, accessed May 1, 2025, [https://discuss.python.org/t/details-on-getting-serial/20074](https://discuss.python.org/t/details-on-getting-serial/20074)  
42. What would be a good library for sending and receiving data from a USB serial port \- Reddit, accessed May 1, 2025, [https://www.reddit.com/r/learnpython/comments/113fqls/what\_would\_be\_a\_good\_library\_for\_sending\_and/](https://www.reddit.com/r/learnpython/comments/113fqls/what_would_be_a_good_library_for_sending_and/)  
43. How to send and receive serial in python, accessed May 1, 2025, [https://discuss.python.org/t/how-to-send-and-receive-serial-in-python/10394](https://discuss.python.org/t/how-to-send-and-receive-serial-in-python/10394)  
44. 3rd Party Python Library PySerial \- General Discussion \- Inductive Automation Forum, accessed May 1, 2025, [https://forum.inductiveautomation.com/t/3rd-party-python-library-pyserial/91293](https://forum.inductiveautomation.com/t/3rd-party-python-library-pyserial/91293)  
45. Welcome to pySerial's documentation — pySerial 3.4 documentation, accessed May 1, 2025, [https://pyserial.readthedocs.io/](https://pyserial.readthedocs.io/)  
46. M.2 XMM7360 (L850) on 5.19.1: "SIM not inserted" (\#612) · Issue · mobile-broadband/ModemManager \- Freedesktop GitLab, accessed May 1, 2025, [https://gitlab.freedesktop.org/mobile-broadband/ModemManager/-/issues/612](https://gitlab.freedesktop.org/mobile-broadband/ModemManager/-/issues/612)