import pytest
import pytest
import sys # Added sys
import os # Added os
from unittest import mock
import glob as actual_glob # Renaming to avoid conflict with mock

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Attempt to import serial, handle if not available for testing basic structure
try:
    import serial
except ImportError:
    serial = None

from smshub_app.app.modem_manager import (
    ModemCommunicator,
    check_modem_connection,
    get_sim_status, # Restored missing import
    get_signal_quality,
    get_network_operator,
    get_phone_number,
    get_imei,
    get_modem_model,
    get_modem_info,
    detect_modems
)
# Import load_config from the correct location if it's used directly by detect_modems
# Assuming it's from app.config_manager based on modem_manager.py
from smshub_app.app.config_manager import load_config # Updated import


# Mock serial.Serial for ModemCommunicator tests
@pytest.fixture
def mock_serial_port():
    """Fixture to mock serial.Serial object."""
    mock_port = mock.Mock(spec=serial.Serial if serial else object)
    mock_port.is_open = False
    mock_port.port = None
    mock_port.baudrate = None
    mock_port.timeout = None
    mock_port.open = mock.Mock()
    mock_port.close = mock.Mock()
    mock_port.write = mock.Mock()
    mock_port.readlines = mock.Mock()
    mock_port.readline = mock.Mock() # Added for send_at_command readline
    mock_port.reset_input_buffer = mock.Mock()
    mock_port.reset_output_buffer = mock.Mock()
    return mock_port

@pytest.fixture
def mock_serial_module(mocker, mock_serial_port):
    """Fixture to mock the entire serial module, specifically serial.Serial."""
    if serial: # Only mock if serial was successfully imported
        mock_serial_class = mocker.patch('serial.Serial', return_value=mock_serial_port)
        # Mock SerialException as well if it's raised and caught
        mocker.patch('serial.SerialException', Exception) # Simple mock for now
        return mock_serial_class, mock_serial_port
    return None, mock_serial_port # Return None if serial is not available


@pytest.mark.skipif(serial is None, reason="pyserial not installed, skipping ModemCommunicator tests")
class TestModemCommunicator:
    PORT = "COM_TEST"
    BAUDRATE = 9600
    TIMEOUT = 2

    def test_constructor_initializes_serial(self, mock_serial_module):
        mock_serial_class, mock_port_instance = mock_serial_module
        communicator = ModemCommunicator(port=self.PORT, baudrate=self.BAUDRATE, timeout=self.TIMEOUT)
        
        mock_serial_class.assert_called_once() # Check that serial.Serial() was called
        # The instance mock_port_instance is what serial.Serial() returns
        assert communicator.ser == mock_port_instance
        assert mock_port_instance.port == self.PORT
        assert mock_port_instance.baudrate == self.BAUDRATE
        assert mock_port_instance.timeout == self.TIMEOUT

    def test_connect_success(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = False
        mock_port_instance.open.return_value = None # Simulate successful open

        communicator = ModemCommunicator(port=self.PORT)
        assert communicator.connect() is True
        mock_port_instance.open.assert_called_once()

    def test_connect_already_open(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True
        
        communicator = ModemCommunicator(port=self.PORT)
        assert communicator.connect() is True
        mock_port_instance.open.assert_not_called()

    def test_connect_failure(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = False
        if serial: # Only if serial is available to mock its exception
            mock_port_instance.open.side_effect = serial.SerialException("Connection failed")

            communicator = ModemCommunicator(port=self.PORT)
            assert communicator.connect() is False
            mock_port_instance.open.assert_called_once()
        else:
            pytest.skip("serial.SerialException cannot be mocked as serial is not installed")


    def test_disconnect_open(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True

        communicator = ModemCommunicator(port=self.PORT)
        communicator.disconnect()
        mock_port_instance.close.assert_called_once()

    def test_disconnect_already_closed(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = False

        communicator = ModemCommunicator(port=self.PORT)
        communicator.disconnect()
        mock_port_instance.close.assert_not_called()

    def test_send_at_command_ok(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True
        mock_port_instance.readline.side_effect = [b"AT\r\n", b"OK\r\n"]

        communicator = ModemCommunicator(port=self.PORT, timeout=1) # Short timeout for test
        success, response = communicator.send_at_command("AT")
        
        assert success is True
        assert "OK" in response
        mock_port_instance.write.assert_called_once_with(b"AT\r\n")

    def test_send_at_command_error_response(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True
        mock_port_instance.readline.side_effect = [b"AT+CERR\r\n", b"ERROR\r\n"]

        communicator = ModemCommunicator(port=self.PORT, timeout=1)
        success, response = communicator.send_at_command("AT+CERR")
        
        assert success is False # ERROR means not expected_response
        assert "ERROR" in response

    def test_send_at_command_timeout(self, mock_serial_module, mocker):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True
        # Simulate readline returning empty bytes (timeout) repeatedly
        mock_port_instance.readline.return_value = b"" 

        # Mock time.time to control the timeout logic within send_at_command
        # Start time will be 0, then advance past timeout
        mock_time = mocker.patch('smshub_app.app.modem_manager.time') # Updated patch target
        # side_effect: [start_time, 1st loop check, 2nd loop check (timeout), logger call]
        mock_time.side_effect = [0, 0.5, 1.5, 1.5]

        communicator = ModemCommunicator(port=self.PORT, timeout=1)
        success, response = communicator.send_at_command("AT_TIMEOUT_TEST")
        
        assert success is False
        assert "ERROR: TIMEOUT" in response

    def test_send_at_command_specific_response(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True
        mock_port_instance.readline.side_effect = [b"COMMAND\r\n", b"+CPIN: READY\r\n", b"OK\r\n"]

        communicator = ModemCommunicator(port=self.PORT, timeout=1)
        success, response = communicator.send_at_command("AT+CPIN?", expected_response="+CPIN: READY")
        
        assert success is True
        assert "+CPIN: READY" in response

    def test_send_at_command_port_not_open_connect_fails(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = False
        if serial:
            mock_port_instance.open.side_effect = serial.SerialException("Failed to connect")
            
            communicator = ModemCommunicator(port=self.PORT)
            success, response = communicator.send_at_command("AT")

            assert success is False
            assert "ERROR: Port not open and connection failed" in response
            mock_port_instance.open.assert_called_once() # Attempted to connect
        else:
            pytest.skip("serial.SerialException cannot be mocked as serial is not installed")

    def test_send_at_command_serial_exception_during_rw(self, mock_serial_module):
        _, mock_port_instance = mock_serial_module
        mock_port_instance.is_open = True
        if serial:
            mock_port_instance.write.side_effect = serial.SerialException("Write failed")

            communicator = ModemCommunicator(port=self.PORT)
            success, response = communicator.send_at_command("AT")

            assert success is False
            assert any("ERROR: SERIAL EXCEPTION" in r for r in response)
        else:
            pytest.skip("serial.SerialException cannot be mocked as serial is not installed")

# --- Tests for AT Command Helper Functions ---

@pytest.fixture
def mock_communicator(mocker):
    """Fixture to mock ModemCommunicator instance."""
    mock_comm = mocker.Mock(spec=ModemCommunicator)
    # Ensure it's treated as an instance of ModemCommunicator for isinstance checks
    mocker.patch('smshub_app.app.modem_manager.ModemCommunicator', return_value=mock_comm) # Updated patch target
    return mock_comm

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_check_modem_connection_success(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["AT", "OK"])
    assert check_modem_connection(mock_communicator) is True
    mock_communicator.send_at_command.assert_called_once_with("AT", expected_response="OK", timeout_override=1)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_check_modem_connection_failure(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["AT", "ERROR"])
    assert check_modem_connection(mock_communicator) is False

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_sim_status_ready(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["+CPIN: READY", "OK"])
    success, status = get_sim_status(mock_communicator)
    assert success is True
    assert status == "READY"
    mock_communicator.send_at_command.assert_called_once_with("AT+CPIN?", expected_response="+CPIN:", timeout_override=2)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_sim_status_sim_pin(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["+CPIN: SIM PIN", "OK"])
    success, status = get_sim_status(mock_communicator)
    assert success is True
    assert status == "SIM PIN"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_sim_status_error_response(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["ERROR"])
    success, status = get_sim_status(mock_communicator)
    assert success is False
    assert status == "ERROR: No valid response"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_sim_status_parse_error(mock_communicator):
    # Success is True because "+CPIN:" was found, but parsing the actual status fails
    mock_communicator.send_at_command.return_value = (True, ["+CPIN: GARBAGE", "OK"])
    success, status = get_sim_status(mock_communicator)
    assert success is True # The command itself "succeeded" in finding +CPIN:
    assert status == "GARBAGE" # The current implementation returns the raw string after +CPIN:

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_signal_quality_success(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["+CSQ: 20,1", "OK"])
    success, sq = get_signal_quality(mock_communicator)
    assert success is True
    assert sq == {"rssi": 20, "ber": 1}
    mock_communicator.send_at_command.assert_called_once_with("AT+CSQ", expected_response="+CSQ:", timeout_override=2)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_signal_quality_error_response(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["ERROR"])
    success, sq = get_signal_quality(mock_communicator)
    assert success is False
    assert sq == {"error": "No valid +CSQ response"}

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_signal_quality_parse_error_value_error(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["+CSQ: BAD,DATA", "OK"])
    success, sq = get_signal_quality(mock_communicator)
    assert success is False
    assert sq == {"error": "Format error in +CSQ response"}

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_signal_quality_parse_error_wrong_parts(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["+CSQ: 1", "OK"]) # Not enough parts
    success, sq = get_signal_quality(mock_communicator)
    assert success is False
    assert sq == {"error": "Could not parse +CSQ response"} # This is the path taken

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_network_operator_success(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ['+COPS: 0,0,"My Operator",7', "OK"])
    success, operator = get_network_operator(mock_communicator)
    assert success is True
    assert operator == "My Operator"
    mock_communicator.send_at_command.assert_called_once_with("AT+COPS?", expected_response="+COPS:", timeout_override=3)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_network_operator_not_registered(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ['+COPS: 0', "OK"]) # Not enough parts for name
    success, operator = get_network_operator(mock_communicator)
    assert success is False # Due to format error
    assert operator == "UNKNOWN: +COPS response format error"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_network_operator_empty_name(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ['+COPS: 0,0,"",7', "OK"])
    success, operator = get_network_operator(mock_communicator)
    assert success is True
    assert operator == "Not registered/Unknown"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_network_operator_error_response(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["ERROR"])
    success, operator = get_network_operator(mock_communicator)
    assert success is False
    assert operator == "ERROR: No valid response"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_phone_number_success(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ['+CNUM: "MyNum","+1234567890",145', "OK"])
    success, number = get_phone_number(mock_communicator)
    assert success is True
    assert number == "+1234567890"
    mock_communicator.send_at_command.assert_called_once_with("AT+CNUM", expected_response="+CNUM:", timeout_override=3)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_phone_number_not_available(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["+CNUM: ,,129", "OK"]) # No number
    success, number = get_phone_number(mock_communicator)
    assert success is True
    assert number == "NOT AVAILABLE"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_phone_number_command_fail_error(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["ERROR"])
    success, number = get_phone_number(mock_communicator)
    assert success is False
    assert number == "ERROR: Modem reported error"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_phone_number_command_fail_no_error_in_response(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["SOME JUNK"]) # No "ERROR"
    success, number = get_phone_number(mock_communicator)
    assert success is False
    assert number == "NOT AVAILABLE" # Default fallback

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_imei_success(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["123456789012345", "OK"])
    success, imei = get_imei(mock_communicator)
    assert success is True
    assert imei == "123456789012345"
    mock_communicator.send_at_command.assert_called_once_with("AT+CGSN", expected_response="OK", timeout_override=2)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_imei_command_ok_no_imei_parsed(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["Some other text", "OK"])
    success, imei = get_imei(mock_communicator)
    assert success is False
    assert imei == "UNKNOWN: IMEI format not recognized"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_imei_command_fail(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["ERROR"])
    success, imei = get_imei(mock_communicator)
    assert success is False
    assert imei == "ERROR: No valid response or command failed"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_modem_model_success_own_line(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["MyModel123", "OK"])
    success, model = get_modem_model(mock_communicator)
    assert success is True
    assert model == "MyModel123"
    mock_communicator.send_at_command.assert_called_once_with("AT+CGMM", expected_response="OK", timeout_override=2)

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_modem_model_success_cgmm_response(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ['+CGMM: "MyModelXYZ"', "OK"])
    success, model = get_modem_model(mock_communicator)
    assert success is True
    assert model == "MyModelXYZ"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_modem_model_command_ok_no_model_parsed(mock_communicator):
    mock_communicator.send_at_command.return_value = (True, ["OK"]) # Only OK, no model line
    success, model = get_modem_model(mock_communicator)
    assert success is False
    assert model == "UNKNOWN: Model format not recognized"

@pytest.mark.skipif(serial is None, reason="pyserial not installed, ModemCommunicator dependent tests skipped")
def test_get_modem_model_command_fail(mock_communicator):
    mock_communicator.send_at_command.return_value = (False, ["ERROR"])
    success, model = get_modem_model(mock_communicator)
    assert success is False
    assert model == "ERROR: No valid response or command failed"


# --- Tests for get_modem_info ---

@pytest.fixture
def mock_modem_communicator_instance(mocker):
    instance = mocker.Mock(spec=ModemCommunicator)
    instance.connect = mocker.Mock()
    instance.disconnect = mocker.Mock()
    instance.send_at_command = mocker.Mock()
    return instance

@pytest.fixture
def patch_modem_communicator_class(mocker, mock_modem_communicator_instance):
    # Patch the class ModemCommunicator in modem_manager module
    # to return our mock_modem_communicator_instance when instantiated.
    return mocker.patch('smshub_app.app.modem_manager.ModemCommunicator', return_value=mock_modem_communicator_instance) # Updated patch target


@pytest.mark.skipif(serial is None, reason="pyserial not installed, get_modem_info tests skipped")
def test_get_modem_info_success(patch_modem_communicator_class, mock_modem_communicator_instance, mocker):
    mock_modem_communicator_instance.connect.return_value = True

    # Mock helper functions used by get_modem_info
    mocker.patch('smshub_app.app.modem_manager.check_modem_connection', return_value=True) # Updated patch target
    mocker.patch('smshub_app.app.modem_manager.get_sim_status', return_value=(True, "READY")) # Updated patch target
    mocker.patch('smshub_app.app.modem_manager.get_signal_quality', return_value=(True, {"rssi": 25, "ber": 0})) # Updated patch target
    mocker.patch('smshub_app.app.modem_manager.get_network_operator', return_value=(True, "TestNet")) # Updated patch target
    mocker.patch('smshub_app.app.modem_manager.get_phone_number', return_value=(True, "+19998887777")) # Updated patch target
    mocker.patch('smshub_app.app.modem_manager.get_imei', return_value=(True, "12345IMEI67890")) # Updated patch target
    mocker.patch('smshub_app.app.modem_manager.get_modem_model', return_value=(True, "TestModelX1")) # Updated patch target

    port = "COM_INFO"
    baud = 115200
    timeout = 1
    info = get_modem_info(port, baud, timeout)

    patch_modem_communicator_class.assert_called_once_with(port=port, baudrate=baud, timeout=timeout)
    mock_modem_communicator_instance.connect.assert_called_once()
    
    assert info["port"] == port
    assert info["status"] == "RESPONSIVE"
    assert info["sim_status"] == "READY"
    assert info["signal_rssi"] == 25
    assert info["signal_ber"] == 0
    assert info["operator"] == "TestNet"
    assert info["phone_number"] == "+19998887777"
    assert info["imei"] == "12345IMEI67890"
    assert info["model"] == "TestModelX1"
    mock_modem_communicator_instance.disconnect.assert_called_once()

@pytest.mark.skipif(serial is None, reason="pyserial not installed, get_modem_info tests skipped")
def test_get_modem_info_connection_fails(patch_modem_communicator_class, mock_modem_communicator_instance):
    mock_modem_communicator_instance.connect.return_value = False
    port = "COM_FAIL"
    info = get_modem_info(port)

    patch_modem_communicator_class.assert_called_once_with(port=port, baudrate=115200, timeout=1)
    mock_modem_communicator_instance.connect.assert_called_once()
    assert info["port"] == port
    assert info["status"] == "ERROR: Connection failed"
    assert info["sim_status"] == "N/A" # Should not proceed to query these
    mock_modem_communicator_instance.disconnect.assert_called_once() # disconnect is in finally

@pytest.mark.skipif(serial is None, reason="pyserial not installed, get_modem_info tests skipped")
def test_get_modem_info_pyserial_not_installed_scenario(mocker):
    # Simulate serial module being None at the top of modem_manager
    mocker.patch('smshub_app.app.modem_manager.serial', None) # Updated patch target
    
    port = "COM_NO_SERIAL"
    info = get_modem_info(port)
    
    assert info["port"] == port
    assert info["status"] == "ERROR: pyserial not installed"

# --- Tests for detect_modems ---

@pytest.fixture
def mock_load_config(mocker):
    return mocker.patch('smshub_app.app.modem_manager.load_config') # Updated patch target

@pytest.fixture
def mock_glob_glob(mocker):
    # Mock glob.glob within the modem_manager module's scope
    return mocker.patch('smshub_app.app.modem_manager.glob.glob') # Updated patch target


@pytest.fixture
def mock_get_modem_info(mocker):
    # Mock get_modem_info within the modem_manager module's scope
    return mocker.patch('smshub_app.app.modem_manager.get_modem_info') # Updated patch target

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_found_specific_port(mock_load_config, mock_get_modem_info, mocker):
    # Mock serial.tools.list_ports.comports to avoid real hardware scan
    mocker.patch('serial.tools.list_ports.comports', return_value=[])

    sample_config = {
        "modems": [
            {"name": "Modem1", "enabled": True, "port": "/dev/ttyUSB0", "baudrate": 115200, "timeout": 1}
        ]
    }
    mock_load_config.return_value = sample_config
    
    modem1_details = {"port": "/dev/ttyUSB0", "status": "RESPONSIVE", "imei": "IMEI1", "modem_name_config": "Modem1"}
    mock_get_modem_info.return_value = modem1_details

    detected = detect_modems()

    mock_load_config.assert_called_once()
    mock_get_modem_info.assert_called_once_with(port="/dev/ttyUSB0", baudrate=115200, timeout=1)
    assert len(detected) == 1
    assert detected[0] == modem1_details

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_found_port_pattern(mock_load_config, mock_glob_glob, mock_get_modem_info, mocker):
    mocker.patch('serial.tools.list_ports.comports', return_value=[])
    sample_config = {
        "modems": [
            {"name": "Modem2", "enabled": True, "port_pattern": "/dev/ttyACM*", "at_command_port_index": 0}
        ]
    }
    mock_load_config.return_value = sample_config
    mock_glob_glob.return_value = ["/dev/ttyACM0", "/dev/ttyACM1"] # glob finds these
    
    modem2_details = {"port": "/dev/ttyACM0", "status": "RESPONSIVE", "imei": "IMEI2", "modem_name_config": "Modem2"}
    # get_modem_info will be called with the first port from glob result due to index 0
    mock_get_modem_info.return_value = modem2_details 

    detected = detect_modems()

    mock_glob_glob.assert_called_once_with("/dev/ttyACM*")
    mock_get_modem_info.assert_called_once_with(port="/dev/ttyACM0", baudrate=115200, timeout=1) # Default baud/timeout
    assert len(detected) == 1
    assert detected[0] == modem2_details

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_port_pattern_index_selects_correct_port(mock_load_config, mock_glob_glob, mock_get_modem_info, mocker):
    mocker.patch('serial.tools.list_ports.comports', return_value=[])
    sample_config = {
        "modems": [
            {"name": "Modem3", "enabled": True, "port_pattern": "/dev/ttyUSB*", "at_command_port_index": 1}
        ]
    }
    mock_load_config.return_value = sample_config
    mock_glob_glob.return_value = ["/dev/ttyUSB0", "/dev/ttyUSB1", "/dev/ttyUSB2"]
    
    modem3_details = {"port": "/dev/ttyUSB1", "status": "RESPONSIVE", "imei": "IMEI3", "modem_name_config": "Modem3"}
    mock_get_modem_info.return_value = modem3_details

    detected = detect_modems()
    mock_get_modem_info.assert_called_once_with(port="/dev/ttyUSB1", baudrate=115200, timeout=1)
    assert len(detected) == 1
    assert detected[0] == modem3_details

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_no_ports_found_by_pattern(mock_load_config, mock_glob_glob, mock_get_modem_info, mocker):
    mocker.patch('serial.tools.list_ports.comports', return_value=[])
    sample_config = {"modems": [{"name": "ModemX", "enabled": True, "port_pattern": "/dev/ttyNonExistent*"}]}
    mock_load_config.return_value = sample_config
    mock_glob_glob.return_value = [] # No ports match

    detected = detect_modems()
    mock_get_modem_info.assert_not_called()
    assert len(detected) == 0

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_no_modems_configured(mock_load_config, mock_get_modem_info, mocker):
    mocker.patch('serial.tools.list_ports.comports', return_value=[])
    mock_load_config.return_value = {"modems": []} # Empty list of modems
    
    detected = detect_modems()
    mock_get_modem_info.assert_not_called()
    assert len(detected) == 0

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_modem_disabled(mock_load_config, mock_get_modem_info, mocker):
    mocker.patch('serial.tools.list_ports.comports', return_value=[])
    sample_config = {"modems": [{"name": "DisabledModem", "enabled": False, "port": "/dev/ttyUSB5"}]}
    mock_load_config.return_value = sample_config

    detected = detect_modems()
    mock_get_modem_info.assert_not_called()
    assert len(detected) == 0

@pytest.mark.skipif(serial is None, reason="pyserial not installed, detect_modems tests skipped")
def test_detect_modems_get_modem_info_fails_connection(mock_load_config, mock_get_modem_info, mocker):
    mocker.patch('serial.tools.list_ports.comports', return_value=[])
    sample_config = {"modems": [{"name": "FailModem", "enabled": True, "port": "/dev/ttyFAIL0"}]}
    mock_load_config.return_value = sample_config
    
    # Simulate get_modem_info returning an error status
    fail_details = {"port": "/dev/ttyFAIL0", "status": "ERROR: Connection failed", "modem_name_config": "FailModem"}
    mock_get_modem_info.return_value = fail_details

    detected = detect_modems()
    mock_get_modem_info.assert_called_once_with(port="/dev/ttyFAIL0", baudrate=115200, timeout=1)
    assert len(detected) == 1 # Still adds it to the list with error status
    assert detected[0] == fail_details

def test_detect_modems_pyserial_not_installed_main_module_scope(mocker):
    # This test checks the behavior when 'serial' is None at the module level of modem_manager
    mocker.patch('smshub_app.app.modem_manager.serial', None) # Updated patch target
    mock_load_config = mocker.patch('smshub_app.app.modem_manager.load_config') # Updated patch target
    mock_load_config.return_value = {
        "modems": [
            {"name": "ModemNoSerial", "enabled": True, "port": "/dev/ttyUSB0"}
        ]
    }
    # get_modem_info itself will return "ERROR: pyserial not installed"
    # We need to mock get_modem_info to simulate this behavior correctly
    mock_gmi = mocker.patch('smshub_app.app.modem_manager.get_modem_info') # Updated patch target
    mock_gmi.return_value = {"port": "/dev/ttyUSB0", "status": "ERROR: pyserial not installed", "modem_name_config": "ModemNoSerial"}


    detected = detect_modems()
    
    mock_gmi.assert_called_once_with(port="/dev/ttyUSB0", baudrate=115200, timeout=1)
    assert len(detected) == 1
    assert detected[0]["status"] == "ERROR: pyserial not installed"

def test_detect_modems_pyserial_not_installed_for_port_pattern(mocker):
    mocker.patch('smshub_app.app.modem_manager.serial', None) # Updated patch target
    mock_load_config = mocker.patch('smshub_app.app.modem_manager.load_config') # Updated patch target
    mock_get_modem_info = mocker.patch('smshub_app.app.modem_manager.get_modem_info') # Updated patch target
    mock_load_config.return_value = {
        "modems": [
            {"name": "ModemPatternNoSerial", "enabled": True, "port_pattern": "/dev/ttyACM*"}
        ]
    }
    
    detected = detect_modems()
    
    # glob.glob should not be called, get_modem_info should not be called
    mock_get_modem_info.assert_not_called()
    assert len(detected) == 0 # Skips this modem