[{"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nPage Tools\n    "}, {"title": "v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nThis topic does not exist yet\n\nYou've followed a link to a topic that doesn't exist yet. If permissions allow, you may create it by clicking on Create this page.\n\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:android ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nTable of Contents\nAll Methods: brief info\nGeneral Info\nMethod 1.1 : the App + cloud VPS\nMethod 1.2 : the App + Proxysmart server in the LAN (“WIFI-Split”)\nMethod 2.1 : USB tethering + Termux\nMethod 2.2 : Termux + VPS\nMethod 3 : USB tethering + ADB\nAll Methods: brief info\nMethod 1.1 : the App + cloud VPS\nThe App is installed on phones, it connects to a VPS.\nProxysmart software is installed on the VPS.\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nMethod 1.2 : the App + Proxysmart server in the LAN (“WIFI-Split”)\nThe App is installed on phones, it connects to a Proxysmart server installed in the LAN by its local WIFI IP\nProxysmart software is installed on the local server in the LAN\nProxy ports forwarded either through a VPS, or through LAN router\nPRO: no USB hubs, just phones wall chargers\nPRO: Home WAN can be used for forwarding the proxy ports\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nMethod 2.1 : USB tethering + Termux\nAn App (Termux) is installed on phones.\nThey are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: phone can be controlled even if its MobileData goes off\nPRO: all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nMethod 2.2 : Termux + VPS\nAn App (Termux) is installed on phones.\nThe phones are connected to the Linux VPS running in the cloud\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nMethod 3 : USB tethering + ADB\nPhones enable ADB and are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: no extra Apps are installed on the phone, very simple to setup\nPRO: a phone can be controlled even if its MobileData goes off\nCON: not all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nGeneral Info\nAvoid Android 12. It has very agressive mechanism that kills Apps running in background. More info: https://github.com/termux/termux-app/issues/2366\nNo Android Root is needed. But it's nice to have it.\nMethod 1.1 : the App + cloud VPS\nBrief info of the method:\nThe App is installed on phones, it connects to a VPS.\nProxysmart software is installed on the VPS.\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nPrepare the Proxysmart server:\nin the WebApp → Settings → set APROXY_ENABLE = On\nrun proxysmart.sh reset_complete or click Reset Complete in the WebApp or reboot the Proxysmart server\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nDisconnect USB data cable from the PC\nProxysmart App installation\nDownload & Install APK from http://proxysmart.org/android/ or https://proxysmart.org/files/proxysmart-android-agent/proxysmart-android-agent.apk\nAfter the App appears on the Desktop - long tap the App icon → Info, set its Battery properties: 1. Can use Battery in background; 2. No Battery optimization\nConnect the App to the Proxysmart server:\n\nOpen the App, enter details:\n\nDevice Name: must be u101 , u102, and so on.\nServer password: px903903 (or whatever is set in the Proxysmart server WebApp as APROXY_API_PASSWORD )\nIP address: IP or hostname of the cloud VPS with Proxysmart\nTap SAVE\nTap ENABLE IP CHANGE\nTap NO ROOT\nTap CONFIGURE, tap Google or Gear icon, set “Proxysmart” as voice assistant, tap back.\nTap TRY CHANGE IP. Confirm the phone went to AirPlane mode and came back.\nTap START\nWait until it shows “Connnected” and appears in the Proxysmart WebApp\nTest IP rotation\nTap “TRY CHANGE IP” in the App\nThe phone will go Airplane mode and back\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later), the Proxysmart App will start and connect to the Proxysmart server\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 1.2 : the App + Proxysmart server in the LAN (“WIFI-Split”)\nBrief info of the method:\nThe App is installed on phones, it connects to a Proxysmart server installed in the LAN by its local WIFI IP\nProxysmart software is installed on the local server in the LAN\nProxy ports forwarded either through a VPS, or through LAN router\nPRO: no USB hubs, just phones wall chargers\nPRO: Home WAN can be used for forwarding the proxy ports\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nPrepare the Home/Office router\nPeer-to-peer connections must be allowed between LAN clients, so LAN clients can communicate over WiFi.\nPrepare the Proxysmart server:\nin the WebApp → Settings → set APROXY_ENABLE = On\nrun proxysmart.sh reset_complete or click Reset Complete in the WebApp or reboot the Proxysmart server\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nIn Android Settings → Developer Settings → enable 'keep mobile data On even if WiFi is connected'\nConnect phone to WIFI and enable MobileData.\nDisconnect USB data cable from the PC\nProxysmart App installation\nDownload & Install APK from http://proxysmart.org/android/ or https://proxysmart.org/files/proxysmart-android-agent/proxysmart-android-agent.apk\nAfter the App appears on the Desktop - long tap the App icon → Info, set its Battery properties: 1. Can use Battery in background; 2. No Battery optimization\nConnect the App to the Proxysmart server:\n\nOpen the App, enter details:\n\nDevice Name: must be u101 , u102, and so on.\nServer password: px903903 (or whatever is set in the Proxysmart server WebApp as APROXY_API_PASSWORD )\nIP address: local LAN IP of the PC with Proxysmart\nTap SAVE\nTap ENABLE IP CHANGE\nTap NO ROOT\nTap CONFIGURE, tap Google or Gear icon, set “Proxysmart” as voice assistant, tap back.\nTap TRY CHANGE IP. Confirm the phone went to AirPlane mode and came back.\nTap START\nWait until it shows “Connnected” and appears in the Proxysmart WebApp\nTest IP rotation\nTap “TRY CHANGE IP” in the App\nThe phone will go Airplane mode and back\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later), the Proxysmart App will start and connect to the Proxysmart server\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 2.1 : USB tethering + Termux\nBrief info of the method:\nAn App (Termux) is installed on phones.\nThey are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: phone can be controlled even if its MobileData goes off\nPRO: all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nPrepare the Proxysmart server:\n\nNo steps needed\n\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nConnect with USB cable to the PC with Proxysmart\nIn Developer settings: set Default USB mode: Tethering.\nTermux App installation\nInstall F-Droid https://f-droid.org/\nOpen F-Droid App, install Termux, Termux:Api, Termux:Boot.\nRun Termux:Boot, it will show up, then switch to Home screen.\nLong press Termux icon, INFO>Battery>set Unrestricted, grant access to Location, SMS, CONTACTS, Calls\nRepeat for Termux:Boot and Termux:API Apps.\nOpen Termux, wait until it's initialized & black screen with cursos appears.\nType in:\n\ncurl modus.tanatos.org/pz/pz1 > pz; bash -x pz\n\nand press ENTER. It will ask for these details, type in everything:\nimei: IMEI of the phone\n\nExpect big green DONE in the end.\n\nMacrodroid\nInstall Macrodroid from GooglePlay.\nLong tap on Macrodroid app icon ⇒ Run in background ⇒ yes\nOpen Macrodroid app, settings ⇒ Ignore battery optimizations\n\nCreate a macro\n\nTrigger: catch notification: notification recieved: any app: contains: PX_RESET\nThen press OK.\nAction: vibrate Blip, Airplane ON, wait 2s, Airplane OFF\nGrant all permissions! (catch notification, assistent, etc).\nSave macro as IP-Reset\nTest trigger. It must vibrate and do Airplane on\\off.\n\nCreate a macro\n\nTrigger: Regular interval: 15 minutes\nActions: Airplane off, Launch application: Termux\nSave as Autofix, (grant Overlay)\nTest IP rotation\n\nOpen Termux App. Type in:\n\nbash ip_reset.md\n\nAnd press ENTER. You will see how IP is changed + micro vibration.\n\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later) USB tethering will be enabled\nAfter start-up (1-2 minute later) Termux will auto-start.\nPlug in the Proxysmart server with USB data cable\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 2.2 : Termux + VPS\nBrief info of the method:\nAn App (Termux) is installed on phones.\nThe phones are connected to the Linux VPS running in the cloud\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nPrepare the Proxysmart server:\n\nNo steps needed\n\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nDisconnect USB data cable from the PC\nTermux App installation\nInstall F-Droid https://f-droid.org/\nOpen F-Droid App, install Termux, Termux:Api, Termux:Boot.\nRun Termux:Boot, it will show up, then switch to Home screen.\nLong press Termux icon, INFO>Battery>set Unrestricted, grant access to Location, SMS, CONTACTS, Calls\nRepeat for Termux:Boot and Termux:API Apps.\nOpen Termux, wait until it's initialized & black screen with cursos appears.\nType in:\n\ncurl modus.tanatos.org/pz/pz2 > pz; bash -x pz\n\nand press ENTER. It will ask for these details, type in everything:\n\nimei: IMEI of the phone,\nVPS: IP of the VPS in the cloud\nVPS_PORT: 22\n\nExpect big green DONE in the end.\n\nMacrodroid\nInstall Macrodroid from GooglePlay.\nLong tap on Macrodroid app icon ⇒ Run in background ⇒ yes\nOpen Macrodroid app, settings ⇒ Ignore battery optimizations\n\nCreate a macro\n\nTrigger: catch notification: notification recieved: any app: contains: PX_RESET\nThen press OK.\nAction: vibrate Blip, Airplane ON, wait 2s, Airplane OFF\nGrant all permissions! (catch notification, assistent, etc).\nSave macro as IP-Reset\nTest trigger. It must vibrate and do Airplane on\\off.\n\nCreate a macro\n\nTrigger: Regular interval: 15 minutes\nActions: Airplane off, Launch application: Termux\nSave as Autofix, (grant Overlay)\nTest IP rotation\n\nOpen Termux App. Type in:\n\nbash ip_reset.md\n\nAnd press ENTER. You will see how IP is changed + micro vibration.\n\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later) Termux will auto-start.\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 3 : USB tethering + ADB\nBrief info of the method:\nPhones enable ADB and are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: no extra Apps are installed on the phone, very simple to setup\nPRO: a phone can be controlled even if its MobileData goes off\nCON: not all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nPrepare the Proxysmart server:\nin the WebApp → Settings → set ADB_PHONES = On\nrun proxysmart.sh reset_complete or click Reset Complete in the WebApp or reboot the Proxysmart server\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nConnect with USB cable to the PC with Proxysmart\nIn Developer settings: set Default USB mode: Tethering.\nIn Developer settings: 1. Enable USB debugging. 2. Disable ADB authorization timeout. 3. USB debugging, allow security settings: set ON.\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later) USB tethering will be enabled\nPlug in the Proxysmart server with USB data cable\nWhen a popup box appears on the phone, 'Allow USB debugging from this computer?', answer YES\nWait until the phone appears in the WebApp of the Proxysmart server\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nPermission Denied\n\nSorry, you don't have enough rights to continue.\n\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nPage Tools\n    "}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nPage Tools\n    "}, {"title": "v1:changelog ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nChangelog\n\n2024-03-21\n\nSierraWireless EM7455 in PPP mode\n\n2024-01-23\n\nMTU fix (slow speed fix) on USA Verizone on LTE chips\n\n2024-01-22\n\nAPN per modem (LTE chips and PPP sticks)\n\n2024-01-17\n\nAlcatel IK41, improved IP rotation\n\n2024-01-08\n\nSierra Wireless EM7455: force set APN even before checking LTE status\n\n2023-12-25\n\nWebApp: mobile signal is shown as colored bars\n\n2023-12-22\n\nsupport of Huawei 5G CPE H112-370 (ip rotation, sms read)\n\n2023-12-21\n\nadded 'modems states' : adding queued→adding→added→deleting , failed_to_add.\n\n2023-12-19\n\nreduced IP rotation time on Huawei (E3372, E8372, E55*) by approx. 4 seconds\n\n2023-12-12\n\nimproved support of Huawei E3372s modems.\nexport/import of LAN modems & other local settings during the Backup/Restore\n\n2023-12-07\n\nAndroid VPN can send UDP.\n\n2023-12-06\n\nAndroid proxies - cover reconnects.\nReboot call in the WebApp opens in new tab.\n\n2023-12-05\n\nAndroid App can read SMS.\n\n2023-12-04\n\nfix Fibcom L860 on long USB id's.\n\n2023-11-30\n\nSIMA7630C (ip rotation, SMS read, reboot)\n\n2023-11-27\n\nAndroid VPN - added.\nAndroid VPN - proper native DNS from the Mobile Carrier\n\n2023-11-25\n\nopenvpn sessions log in a separate file ( $OPENVPN_SESSIONS_LOG )\nussd on Quectel EP06\n\n2023-11-23\n\nsniffer script to log all proxies requests and original clients IP's. Can be installed either on VPS or on Proxysmart server, or both. It generates easy to read & analyze single log of who,when,what.\n\n2023-11-21\n\nsupport of LTE module - T77W595 / LT4120\n\n2023-11-12\n\nbuild for Ubuntu 24.04 Noble\n\n2023-11-08\n\nimport & export mongodb – a button in the WebApp\nexport to Proxysmart v2\n\n2023-11-07\n\nmongodb uri can be set in /etc/proxysmart/conf.d/*.inc\n\n2023-11-05\n\nParallel initialization of proxies during reset_complete & reset_gently (enablable with PARALLEL_STARTUP variable)\nfix IP rotation counter logging\nReboot button in the main WebApp for rebooting the server\nAndroid proxies - automatic ports probing and detection.\n\n2023-11-03\n\nOlax U90 - support of a new modem\nnew WEB API call for storing a modem object\nQuectel modems now obey with TARGET_MODE during IP rotation\n\n2023-11-01\n\nAlcatel HH71 autoreconnect\nLAN modems: improved detection; ability to reboot\nfix for adding modems when IMEI is not properly detected\n\n2023-10-25\n\nQuectel EC20 support; Imvproved Chinese letters in SMS\nimproved stability of IK41 (stick mode)\n\n2023-10-23\n\nindividual OS TCP spoofing (per proxy)\n\n2023-10-18\n\nimproved speed on Android proxies over 27mbps\n\n2023-10-13\n\nimproved SMS on IK41 (stick mode)\n\n2023-10-09\n\nPossibility to set Bandwitdh Quota direction, one of (in, inout, out).\n\n2023-10-04\n\nimproved Huawei E3272 support.\n\n2023-10-03\n\nTarget mode of a modem during IP rotation: can be set in the WebApp\nDeleted TARGET_MODE from conf.txt (global variable)\n\n2023-09-28\n\nfixed installation in India where github is blocked\nandroid: VPN integration - done\n\n2023-09-26\n\nAdd Huawei 5g CPE H122-373\n\n2023-09-25\n\nimproved ME-906s modems IP rotation\nPrevent getting non-unique IP’s after rotation\n\n2023-09-13\n\nallow more than 1 device to use 1 VPN profile\nVodafone K5161h supported\n\n2023-09-10\n\nWebApp: show total online modems count\nWebApp: show VPN profile link in the main screen of the webapp\nFibocom L860 - custom APN fix & obeying custom Net Mode (TARGET_MODE)\ndeal with dongles Nicknames conflicts\ndeal with HTTP and SOCKS port conflicts\n\n2023-09-09\n\nAndroid proxies - improved Ping graphs; improved Carrier detection\n\n2023-08-30\n\nadded auto reconnect for DW5811e\n\n2023-08-28\n\nimproved QUIC support: add switch 3proxy/gost for Socks software in LAN ; add switch gost/gost3 as Socks software\n\n2023-08-25\n\nqmicli , added timeouts\n\n2023-08-23\n\nzteMF79NV support\n\n2023-08-21\n\nAlcatel modems (MW40,MW42,HH71) obeys $DEFAULT_HILINK_ADMIN_PASSWORD\nAlcatel MW45 improved support.\n\n2023-08-15\n\nadd Reset Counters button to the Bandwidth report page.\n\n2023-07-17\n\nimprove SMS sending on ZTE MF667, MF927, MF688V3E, MF823, MF833, MF93\n\n2023-07-13\n\nIP uniqueness report\n\n2023-07-10\n\nTop hosts report (WEB API method + WebApp table).\nAuto USB reset when modem’s WEB APP is stuck ( AUTO_USB_RESET_DONGLES=1 )\n\n2023-07-06\n\nAdd proxies built on Proxysmart android App\n\n2023-06-04\n\nSIM7600G proper SMS read\n\n2023-05-26\n\nMain proxy user:added Threads limit\nExtra users: added Threads limit & Dual_Auth IP\n\n2023-05-11\n\nadd SIM7600G (Lte cat4)\n\n2023-05-04\n\nadd Foxconn DW5821e (LTE cat 16)\n\n2023-05-02\n\nusb reset button in the WebApp\n\n2023-05-01\n\ncached list of the modems in the main screen of the WebApp\n\n2023-04-13\n\nsupport of pure old Stick(PPP) modems like Huawei E173, E156; ZTE MF110, MF193\nadd E3276 and E303 in Stick(NCM) mode\n\n2023-04-05\n\nability to hide old and new IP’s from the output of IP rotation links.\nauto reboot modems under some circumstances (check conf.txt for configuration)\n\n2023-03-29\n\nadded RequestsPerSecond graphs in modem status\nadded Pings graphs in modem status\n\n2023-03-28\n\nZyxel NR5103E support\n\n2023-03-20\n\n3proxy 0.9.x support\nability to block direct requests to IP’s (e.g. when clients resolve hostnames on their side and bypass Hostnames blocklist) and process only requests containing hostnames (Forces DNS resolution on proxy server side)\n\n2023-03-19\n\nautofix mass storage on Huawei modems\n\n2023-03-11\n\nRepeat IP rotation in case of failure (went online>offline, or the same IP received)\n\n2023-03-09\n\nfixed issue of DNS cache on modems\nBlack 4g modem – MSM8916 with /json WEB API\n\n2023-03-06\n\nability to reboot Quectel if no SIM\nNew UI\nshow graphs in modems statuses\n\n2023-03-03\n\ntimers fix (allows arbitrary nonstandard minutes like 31,61,1222)\nAlcatel IK41 in “stick” mode\n\n2023-03-02\n\nChinese MSM8916 modems support\nbug fix: JS was cached forever\nproxysmart.log - grandparent pid is logged\nproxysmart.log - timestamps preceed every line\nztemf79 : reconnect data\nNum of current connections is reported properly\nBrovi Huawei E3372-325 added support\nQuectel EC25-AFX added\nAPI for status query fixed\n\n2023-02-07\n\nMF667 modem support\n\n2023-02-06\n\nQuectel EM12G\n\n2023-02-05\n\nability to set a custom URL in secure rotation links\nability to set a custom HOST in proxy credentials ports\n\n2023-02-04\n\nQuectel EC25 can send USSD\nIK40 support + USSD\n\n2023-02-02\n\nE3276 can send USSD\nMF823 is properly shown\n\n2023-02-01\n\n3proxy logs contains IMEI and Nickname\n\n2023-01-29\n\nAPI for getting bandwidth stats within arbitrary time interval\n\n2023-01-28\n\nHuawei 3372h-325 “BROVI”\n\n2023-01-27\n\nmongodb cache bug fixed\n\n2023-01-22\n\nvpn: default UDP\n\n2023-01-19\n\nallow only DEMO version on VirtualBox\n\n2023-01-17\n\nshow anonymous link in main screen of the WebApp\n\n2023-01-15\n\nAdded new modem, Xproxy XH22 Wireless\nadded table of content in README\nProxies “Extra users”: added individual speed limits\n\n2023-01-14\n\nQuectel RM520 support (5g)\n\n2023-01-12\n\nFibocom L860, Ipv6 support\n\n2023-01-11\n\npurging sms (for Huawei in HiLink mode only)\n\n2023-01-10\n\nVodafone K5161z supported\n\n2023-01-08\n\nDocumentation: Dirty ip reset\n\n2023-01-07\n\nandroid phones integration (USB tethering mode & remote mobile tunnel mode).\n\n2023-01-04\n\nSecure IP reset links are now shown as full URL\n\n2023-01-03\n\nStatic VPN IP’s based on Index from Openvpn PKI\n\n2022-12-30\n\nWeb: editable field for Pho.Number.\nLink for downloading Openvpn profiles for modems\n\n2022-12-25\n\nQUIC support (UDP, HTTP/3.0) for SOCKS5 proxies , check README\n\n2022-12-22\n\nredefine variables in /etc/proxysmart/conf.d/*.inc\n\n2022-12-21\n\ndetect when CellOp redirects to their Billing Page\n\n2022-12-11\n\nSet minimum time between IP resets\na function for re-add a device: proxysmart.sh add_dev $DEV\nUDEV plug-n-play (hook scripts)\n\n2022-12-02\n\nHuawei dongles in NCM mode (WWAN+AT)\n\n2022-11-27\n\nFiboCom L860-gl : basic work + sms send\nHuawei ME906s : basic work + sms send\n\n2022-11-20\n\nSierraWireless EM7455\n\n2022-11-18\n\nCLR900A\nFranklinT10\n\n2022-11-12\n\nBW quota with PMACCTD, block outgoing access\nignore extra user when it matches with existing user (main user or another extra user)\nAlcatel MW40 - proper operation now\n\n2022-11-09\n\nVerizone Jetpack AC791L\nmore fast status for unknown modems models\n\n2022-11-08\n\nalcatel IK41: reboot, sms list, helper\n\n2022-11-01\n\nsolve issue when run_cache xxx times out and prints nothing, thus is executed over and over again\n\n2022-10-29\n\nDocumentation: secure rotation links\nget_ConnectionStatus_n response must contain OK\ncell op 425 02 ⇒ must be converted too\nmodel_shown returns “” sometimes ⇒ won’t fix, it happens when MAX_PARALLEL_WORKERS_STATUS is too large\n\n2022-10-27\n\nNovatel MIFI\nFranklin T9 (R717)\ncustom DNS servers for proxies\n\n2022-10-25\n\nNM disable modem*\n\n2022-10-19\n\nxproxy.io modems support\nbug fixed: Configuration file /etc/systemd/system/proxysmart.service is marked executable.\nwhen main proxy user == one of extra users, use extra user password\n\n2022-10-16\n\nvpn: blocklist of domains. make sniproxy enablable in conf.txt\nlicense revoking status checked online\n\n2022-10-15\n\nrework of denied domains list, *.domains are added automatically\n\n2022-10-12\n\nUF906 (KuWfi, Anydata, TianJie) modems integration\n\n2022-10-10\n\ndirty ip rotation support\nsecure ip rotation links with auto expiration\n\n2022-10-06\n\nOS spoofing for VPN users\nvpn: mongodb integration\n\n2022-10-04\n\nzte mf688T proxidize can send SMS\nd-link dwm222 basic support (beta)\nsignal reported in main table of the WebApp\n\n2022-09-30\n\nadded OS TCP spoofing with p0f3 signatures (WOW!!!), including Mac OS X, iOS, Android, Windows. Total rework of OSfooler-ng!\nmodem WEB server warm-up (when 1st request is ignored and 2nd and subsequent are processed)\n\n2022-09-22\n\nmodems helpers reorg, make them more fast & cached\nadded hourly IP rotation\n\n2022-09-19\n\nget by mongodb - get 1st value\n\n2022-09-16\n\nzte mf79VIVO support\nextra delay after IP rotation\n\n2022-09-12\n\nreport APN\nzte mf79 - sms send\n\n2022-09-08\n\nimproved support of ZTE MF927\n\n2022-09-03\n\nreport LTE band in full status\n\n2022-09-02\n\nsupport of 4g LAN routers like Huawei Bxxx\n\n2022-08-27\n\nreport IP rotation history as table\n\n2022-08-24\n\nreport IP rotation history\nWebApp can edit modems\nminor fixes: [ ignoring cell fwding in altnetworking2, dns in vpn, etc ]\nshow_model - more correct modems model showing\n\n2022-08-09\n\nopenvpn support (residential VPN!)\n\n2022-08-04\n\nipv6 bug fixed (ipv6 not assigned)\nchange mongodb uri in conf.txt\n\n2022-08-02\n\nnagios plugin exit code fixed ; nagios plugin moved to the main codebase\n\n2022-07-30\n\nfix: del symlink of altnetworking on installation\n\n2022-07-28\n\nhaproxy check >= 2.2\nDocumentation: – periodic IP rotation – ipv6 support – VPS integration\n\n2022-07-22\n\napply_settings = > if absent in DB, assign random creds.\n\n2022-07-20\n\nfixed bug when license stopped working because of floating (??) disk size and RAM size.\n\n2022-07-01\n\nconvert numeric cellop MCCMNC to Letters\ndel old show_status\n\n2022-06-23\n\nipv6, haproxy integration, systemd-slices (altnetworking2).\n\n2022-06-22\n\nmongodb: timeout handling\n\n2022-06-18\n\nmodem_names OR static udev map ⇒ make configurable\nis syslog-ng needed for Haproxy ?? revert back to rsyslog.\n\n2022-06-11\n\ndouble get_external_ip when doing IP reset via API\n\n2022-06-07\n\nPeriodic automatic IP rotation , set AUTO_IP_ROTATION per modem or globally\n\n2022-05-30\n\nadd usb_reset ( usb_reset_individual_DEV ) API, by nick\n\n2022-05-22\n\nadd proxy live counters based on RRD\n\n2022-05-07\n\nwait UDEV_xx after usb reset  reboot\nadd reboot API (CLI|WEB)\n\n2022-05-03\n\nreboot doesn’t wait till modem is added to the system; use reset_gently for that\n\n2022-05-01\n\nbug fixed when a modem has the same LAN as EthLAN, so the modem took priority over LAN\n\n2022-04-26\n\nzte MF971\nadd Quectel modems support\nreport ICCID\n\n2022-04-23\n\nbw_quota bug - quota restrictions are applied on next proxy request only\nread bw_quota, bandlimin, bandlimout, DENIED_SITES_ENABLE, DENIED_SITES_LIST, mtu, extra_users : from Mongodb\n\n2022-04-18\n\ncx /usr/lib/nagios/plugins/proxysmart-nagios-helper.sh\nDNS in Jinja2\n\n2022-04-08\n\nFIX: error 500 when modem not found\nlicense in web\nImei not unique ⇒ show in dash why\nIgnore reset if IMEI is in IGNORED_IMEIS\nDEV not added ⇒ why? show in status\nshow_status_brief, header must be 1st row\nNagios plugin doesn’t work with encrypted source\nZTE MF93: reboot call\n\n2022-03-31\n\nhuawei K5150: reset_ip , list_sms, reboot, send_sms\n\n2022-03-29\n\nmore UWSGI workers\ndemo license\ndeb package building\nmongodb template\n\n2022-03-28\n\nzte mf79: sms list, reboot, IP reset\n\n2022-03-22\n\nindividual blocklists( DENIED_SITES_ENABLE, DENIED_SITES_LIST )\n\n2022-03-21 (PRE)\n\nSms read\nDefault Modem admin pw in config.\nForwarder type in config ( WAN / CELL )\nusb reset yin config ( kris )\nworkmode: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.74 hipi.pl admin123 | grep -i workmode | cut -d“’” -f4\ncell_operator name: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.59 hlcli NetworkInfo | grep ‘ShortName’ | cut -d“ -f4\nadd_indiv.. and reset_gently\nN by modemN\nRandom port and nickname assignment if absent in the db\nlocks\nmake reset_gently WAIT till reset_complete is done\nJSON: report_bandwidth\nJSON function: apply settings for a modem\nstop redirector BEFORE ip rotation to prevent stalled SSH connection on remote SSH server\nleave checking global lock but never acquire it.\nJSON for list modems\nTABLEd status\nshow_status: model & cell_operator & workmode:\nreset_complete: del all Cgroups & ip rules\nJSON function: reset IP\nHipi scripts accept GW & AdminPW args\nmake screenshort w. CutyCapt\nVnstat autorestart\nflush ipv6 on modems\nmake show_status default\norder: curl, hipi w. pass, hipi w/o pass, hlcli, ETC\nOptional ttl fix: Y\nsms sort\nre-connect DATA on modems, if ext_ip empty ⇒ <NAME_EMAIL>\nre-connect DATA on modems, at reset_complete ⇒ <NAME_EMAIL>\nmtu. Report via Valdik\nmtu. Set\nACL : optional: allow admin only to browse HiLink webUI\nstart with no modems reset_complete doesn’t touch ⇒ adding a modem ⇒ reset_gently doesn’t work\ndel ipv6 leak from modems\nACL : optional: deny some sites\nProper logging , with ssh connection, stderr, as one SHOT\nno LAN test\ndefault login/pw for autogenerated proxies\nreport rotation time\nsafe way to read vars from included files\ndeploy script\nN from modemN May not work when iflink is not like modemXXX\ntrim CELLOP\nmanual: tell what http and socks5 ports to use\nmanual: how to use proxy, from internet, from lan\nallow ::1 in ip6tables\ndef.gw via 1 available modem – add to reset_complete\nrun_cached , to prevent multiple hipi runs\nfix EVAL() of commented lines like ‘v=v #ddd’\nregenerate ssh keys for FWD, if absent\nget rid of sourcing files: constants\nrun cached: log hit or miss\nmanual- check if your vps has firewall. disable it. both inside and in hoster panel.\nshow_status: If CurrNet is ’’, use workmode\ncheck faked route on reset_gnelty\nshow_status_json : redirector statuses\nupdate Manual: api calls & sample output\nspeedtest , up & down\ninclude redirector status in status. enabled, failed/running, uptime\nshow_status: list all proxies std format. depends on FWD_ENABLE, list $VPS:\nreplace XARGS+source to just xargs\nlog TimeTook for /var/log/proxysmart.log\ncrypted source autoGen\nbw report change\ncheck /var/run/proxysmart/reset_complete_done ⇒ show_status_json & show_status BREAK\nignored IMEI\nignored devices\nencode with shc.\nanti-p0f\nintegrate ZTE_MF6xx api ( goform/goprocess)\ncheck whether modem WebUI is available, for get_imei(), show_status_json(), get_model()\nadded Anydata UF906, Alcatel IK41 & MW40\nis_locked to JSON status\nglobal RESET LOCK\nzte MF823 ip change\nUSSD\nSMS\nreport phone uptime\nreport USB uptime\nIgnore FRESHLY inserted modems in reset_gently. Let them init\ndon’t rely on bash RANDOM:\nnagios plugin\nzte MF93D\nindividual JSON status for a modem\nWebapp: +GW, +Local_IP, +current hostname, send sms , send ussd\nDHCP more attempts\nWeb: brief status\nbrief status\nreconnect data - additional command, separate from reset_gently\nIP-based ACL\nspeed limits for proxies\nopen proxy (y)\ncheck modem WEB ⇒ change check_tcp to curl.\nDEB=mktemp /tmp/reset_modem_by_imei.XXXXXX – purge it\ndhcp → migrate to dhcpcd from Nmap\nQUICK_STATUS=0\\1, show only few details in show_status..\nreconnect data must give up when reset_complete not finished\nfix: add iptables -w to altnetowrking and Source\nget model ⇒ depend on manufacturer (from lsusb) for ZTE\nWEB API: report bandwidth\nCollectd + Web gui\nttl for _run_cached\nignore Ethernet-USB adapters\nmtu common and individual per modem\nhuman readable bytes counters in bandwidth reports\nmongodb: cached responses\nmake all functions accept IMEI|NICK\nmonthly Quota per user 2022-03-17:\nWEB sometimes show_status_json returns ”“ ⇒ Main WEB screen shows “Expecting value: line 1 column 1 (char 0)” and stucks and doesn’t do further refreshes\nExtra usersin addition to main uper proxy\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nTable of Contents\n1. Proxysmart manual.\n1. Brief details\n2. Adding modems\n2.1 Adding a new modem (USB)\n2.2. Adding a LAN modem.\n2.3. Adding a virtual modem (backend proxy).\n3. Proxy credentials for new modems\n4. Where is WebApp\n5. How to use proxies\n6. Get list of all modems & their external IPs\n7. Reconfigure all modems & proxies.\n8. How to change proxy credentials for a modem. How to rename a modem.\n9. Reset (change) IP on a modem.\n10. How many modems can I run on a single computer?\n11. How to set TTL and why?\n12. How to set MTU and why?\n13. How to set extra settings for a modem.\n14. How can I access the web interface admin panel of each modem?\n14.1. How can I prevent access to modems web interface via proxy?\n15. How to set monthly traffic quota per modem?\n16. How to make my proxes Open (i.e. not requiring authentication )\n17. Get monthly/daily proxy usage.\n18. How to get current number of connections for a modem?\n19. How to read SMS from a modem.\n20. How to change WebApp password\n21. OS Spoofing\n22. Performance tuning\n23. How to lock network mode per modem\n24. What if a modem connected via 3G or 2G, and I want 4G?\n25. I want to add extra users to a proxy\n26. Is IPV6 supported?\n27. Nagios integration.\n28. Secure (anonymous) IP rotation links.\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n29. “Dirty” IP reset.\n30. Exclude some modems\n31. Use custom Speedtest server.\n32. Minimum time between IP rotations\n33. How to block domains\n33.a. How to allow only whitelisted domains.\n34. How to re-rotate IP when IP doesn’t change?\n34.1 Prevent non-unique IP’s after IP rotation.\n35. How to forward proxy ports using HAproxy?\n36. How to use newer 3proxy version 0.9 ?\n37. Where are proxy logs.\n37.1. No logs policy\n38. My proxies are slow.\n39. My proxies are slower than the same SIM card in a Phone.\n40. How to forward proxy ports via each modem individually?\n41. Auto-rebooting modems.\n42. My proxy is offline and showing Red in the WebApp.\n43. Parallel processing of modems.\n44. IP's are rotated on their own\n45. Install logging of all requests in single place\n46. PPP modems\n2. Project description\n1. project architecture (clients, servers, websites),\n2. Online services are used:\n3. CLI API\n1. show status\n2. full reconfiguration\n3. apply setting for a modem by IMEI\n4. reset IP on a modem\n5. reboot a modem\n6.1. Reset a modem via USB\n6. Run speedtest on all modems at once\n7. report bandwitdh\n8. reset bandwidth counter on a modem\n9. list sms on a modem\n10. send sms\n11. purge SMS\n12. send ussd\n13. get bandwidth counters from a modem\n14. Get IP rotations log for a modem\n15. Get Top hosts from a modem\n16. Report IP uniqueness\n4. WEB API\n1. Web API description.\n2. List all modems ( full status, slow)\n3. List all modems ( brief status, fast )\n4. Single modem status\n5. Reset (change) IP on a modem.\n6. Reboot a modem\n7. Send SMS\n8. Send USSD and read response\n9. Read SMS from a modem\n10. Read bandwidth stats from a modem\n11. del\n12. Reset bandwidth stats for a modem\n13. Reset a modem via USB\n14. Get IP rotations log for a modem\n15. Apply settings for a modem\n16. Purge SMS from a modem\n17. Get Top hosts from a modem\n18. Report IP uniquness\n19. Store a modem object in Mongodb\n20. Export backup\n5. Mongodb integration\n5.1. Mongodb schema\n5.2. Moving Mongodb to other server\n6. Installation\n1. Initial installation\nDevelopment version installation\n2. Upgrade\n3. Post Installation\n4. Cloud VPS integration.\nDo I need a VPS?\nVPS setup steps.\nCloud VPS IP change\n5. Forwarding ports through your own LAN router.\n7. License\n1. Demo license\n2. Requesting a License\n2. License installation\n3. Restoring Demo license.\n8. Mobile (4G/5G) VPN\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n8.1.2. Installation with TCP protocol (through LAN router)\n8.1.3. Installation with UDP protocol (through VPS)\n8.1.4. Installation with UDP protocol (through LAN router)\n8.2. Extra profiles for a modem\n8.3. Mobile VPN, how to connect\n8.4. Many users with the same profile\n8.5. Mobile VPN logs\n9. Bugs and Limitations\nLTE modules\nLAN routers\nOpenvpn profiles\n1. Proxysmart manual.\n1. Brief details\n\nI have developed a software that allows you to run your own 4g proxy farm. It runs on a Linux box (PC) with USB hub and the modems.\n\nFunctions:\n\nIP resets on modems\nWebApp for checking status of each modem\nWEBAPI for actions like querying status, IP rotation, getting used bandwidth for the day, running speedtests\nsetting bandwidth quota per modem per month\nbandwidth throttling per modem\nexposing proxy ports, so they are available from world wide\nreadingSMS and USSD\nOS spoofing, to simulate TCP fingerprints of: MacOS  iOS  Windows  Android\ncustom MTU per modem\nproxy ACLs (what to allow/deny to proxy users)\nBasic configuration.\n\nVariables are set /etc/proxysmart/conf.txt.\n\nEach variable has brief description in place.\n\n2. Adding modems\n2.1 Adding a new modem (USB)\nremove PIN from the modem’s SIM card and plug in the modem into USB port or USB hub.\nCheck whether your modem Web App (e.g. Huawei’s E8372 / E5xxx or ZTE MF79 or Alcatel MW4x ) requires authentication, and if it does, set its admin password to admin123. Basically to the value of $DEFAULT_HILINK_ADMIN_PASSWORD variable in /etc/proxysmart/conf.txt. Otherwise many functions will not work, and its IMEI will be detected similarly to 2-1.1.2\nPlug in the modem\nwait ~5 minutes or run sudo proxysmart.sh reset_gently\nthe modem will appear in the WebApp, click EDIT on it, assign some unique Nickname, HTTP & SOCKS5 ports, Login and Password, then click APPLY\nrefresh the WebApp\ndone!\n2.2. Adding a LAN modem.\n\nMake sure LAN_MODEMS_ENABLE=1 is in /etc/proxysmart/conf.txt.\n\nConfigure the server with 2 LAN cards\n\nAssume you have 2 LAN cards, enp6s0 main LAN, enp2s0 is dedicated for LAN modems:\n\nnmcli con\n\nNAME                UUID                                  TYPE      DEVICE \nWired connection 1  bbbee134-51c3-3830-801f-9636470e0708  ethernet  enp6s0\nWired connection 2  000ed912-2d99-3f37-882b-d79ad13102e7  ethernet  enp2s0 \nRename Wired connection 2 → HUBS\nnmcli con modify Wired\\ connection\\ 2 con-name HUBS\nDisable DHCP and IPV6 on HUBS and assign static IPv4 address\nnmcli con modify HUBS ipv4.method manual \\\n    ipv4.addresses *************0/24 ipv6.method disabled ipv4.route-metric 300 \n\nSo you will add the LAN modems to ************/24 network as ************, ************ etc.\n\nsystemctl restart NetworkManager\n\nDelete old route\n\nip ro del default via ************\n\nConfirm you have only 1 default route via main LAN:\n\nip ro\n\nOutput\n\ndefault via *********** dev enp6s0 proto static metric 100 \n\nAdd the modem\n\nChange the modem’s web admin password to something stored in /etc/proxysmart/conf.txt in DEFAULT_HILINK_ADMIN_PASSWORD variable.\nChange the modem’s IP to something unique e.g. *************\nPut the modem into Ethernet switch routed to the Proxysmart server.\nOn the Proxysmart server make sure you can ping the new modem by its IP you set in previous step.\n\nOn the server, edit the /etc/proxysmart/lan_modems.yaml file, add a line\n\n- { gw: *************, dev: lanmodem10 }\n\nThe line contains its unique IP and the word lanmodem10 ( constructed from a word lanmodem plus a unique number ).\n\nThen either wait 5 minutes or run the command proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n2.3. Adding a virtual modem (backend proxy).\n\nA virtual modem is a in fact a redirect to a 3rd party proxy (HTTP or SOCKS5) so you can build own proxies based on that and resell them.\n\nThey even can be rotated if the backend proxy supports it.\n\nHow to add?\n\nMake sure BACKEND_PROXIES_ENABLE=1 is in /etc/proxysmart/conf.txt.\n\nEdit /etc/proxysmart/backend_proxies.yaml , post lines like these:\n\n- id: bproxy1\n  creds: ***************************\n  ip_reset: 'http://x.x.x.x:8083/api/changeIp?cool'\n\n- id: bproxy2\n  creds: https://lll:<EMAIL>:3129\n\nWhere:\n\nid has to be in the form 'bproxy' + a number\ncreds is a line with credentials of the backend proxy\nip_reset is an optional parameter , the URL for triggering IP rotation of the backend proxy\n\nThen either wait 5 minutes or run the command proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n3. Proxy credentials for new modems\n\nWhen adding new modems, please use\n\nunique HTTP ports from 8001 to 8999,\nunique SOCKS ports from 5001 to 5999.\n\nIf you want different ports ranges, update firewall.conf accordingly.\n\nplease use unique nicknames like dongleXXX or whatever else. Don’t use nicknames like randomXXX, that are assigned automatically.\n4. Where is WebApp\n\nOne of\n\nhttp://localhost:8080/\nhttp://LAN_IP:8080/\nhttp://VPS_IP:8080/\n\nBy default login/password are proxy / proxy.\n\n5. How to use proxies\nIf proxy ports are forwarded via remote cloud VPS: then the proxies can be used from all over the Internet, by that VPS IP and proxy port numbers.\nFrom the same LAN where multimodem server is located: by the server’s LAN IP and proxy port numbers.\n6. Get list of all modems & their external IPs\n\nRun: proxysmart.sh show_status for table-alike output.\n\n7. Reconfigure all modems & proxies.\n\nRun: proxysmart.sh reset_complete\n\nIt is done after reboot automatically by a Cron job.\n\n8. How to change proxy credentials for a modem. How to rename a modem.\n\nWebApp method\n\nclick EDIT on a modem, set new port or password or nickname for a modem\nclick APPLY\n9. Reset (change) IP on a modem.\n\nThe options are below.\n\nFrom Web App\n\nClick Reset Ip button.\n\nFrom command line.\n\nRun: proxysmart.sh reset_quick_nick dongle1\n\nWhere dongle1 is a Dongle “nickname” that is seen from output of proxysmart.sh show_status\n\nFrom Web API.\n\ncheck WEB API section of this manual.\n\nHow to rotate a modem periodically?\n\nWebApp method\n\nUpdate modem’s settings in the WebApp and click APPLY.\n\nFor global setting, edit /etc/proxysmart/conf.txt and set AUTO_IP_ROTATION=5 in order to rotate each modem every 5th minute. If set to 0, automatic IP rotation is not done. You can also set hourly rotation, set 120 for every 2h rotation.\n\nCron method\n\nInstall a Cron job. Edit a file /etc/cron.d/proxysmart, add a line ( or uncomment a commented line.. )\n\n*/10 * * * * root run-one /usr/local/bin/proxysmart.sh reset_quick_nick dongle3\n\nso that a modem with the Nickname dongle3 is rotated every 10 min.\n\nRepeat for each modem you want to rotate periodically.\n\n10. How many modems can I run on a single computer?\n\nHi , technically it depends on how powerful this PC is, and how intensively proxies are used.\n\nRaspberry PI - 4 proxies (roughly)\na miniPC (Intel NUC or similar) - up to 10\na Laptop like Core i5 - up to 30.\n\nAlso it depends on what Plan you buy.\n\nAlso it depends on USB configuration, for maximum number of modems:\n\ndisable USB3.0 in BIOS\nuse USB2.0 hubs\n11. How to set TTL and why?\n\nIn some cases custom TTL must be set in order to have Cell Operator think we are not using the modem in hotsport  tethering mode. I.e. we don’t share its data. By default Linux OS has ttl = 64. To change Cell Operator perception of the situation, we want to set it +1 i.e. 65.\n\nEdit /etc/proxysmart/conf.txt and set CUSTOM_TTL_SET=1 and CUSTOM_TTL_VALUE=65 and regenerate settings.\n\n12. How to set MTU and why?\n\nIn some cases different MTU values connect with different types of ISP’s. You may want to change it.\n\nMtu can be only lowered. E.g. if you have MTU 1390, you can set 1340. Not opposite.\n\n- Edit /etc/proxysmart/conf.txt and set CUSTOM_MTU_SET=1 . - Set MTU in the WebApp for each modem.\n\n13. How to set extra settings for a modem.\n\nThose are optional and are set in the WebApp\n\nWHITELIST - allowed customers IP’s who are not required to type in proxy password (IP-based auth).\nbandwidth (speed) limit. Values are in bits per second. E.g. 2/2 mbps will be 2000000/2000000.\nDENIED_SITES_ENABLE (1 or 0) and DENIED_SITES_LIST (list of blocked sites patterns).\nBandwidth Quota, (in Megabytes)\nMTU\n14. How can I access the web interface admin panel of each modem?\n\nOpen WebApp. Locate the modem. Configure a proxy on your desktop browser.\n\nUse proxy login & password as desribed below (14.1 chapter).\n\nVisit modem IP via that proxy.\n\n14.1. How can I prevent access to modems web interface via proxy?\n\nSince 2023-09-10 it is enabled by default.\n\nEdit /etc/proxysmart/conf.txt and set\n\nPROXY_ADMIN_ENABLE=1\nPROXY_ADMIN_LOGIN=SuperAdmin\nPROXY_ADMIN_PASS=Hqmz81mmZr\n\nAnd regenerate configs. So only admin user is allowed to use modems web interfaces, and normal proxy users are not.\n\n15. How to set monthly traffic quota per modem?\n\nIn the WebApp, set monthly traffic quota. Click EDIT & APPLY.\n\n16. How to make my proxes Open (i.e. not requiring authentication )\n\nSet OPEN_PROXIES=1 in /etc/proxysmart/conf.txt and regenerate all configs.\n\nNote, when proxy ports are forrwarded via a VPS, the proxies are available to any internet user. Use it with caution.\n\n17. Get monthly/daily proxy usage.\n\nClick bandwitdh stats in the WebApp, or run proxysmart.sh bandwidth_report_json dongleXXX, you will see these columns:\n\n“bandwidth_bytes_day_in”\n“bandwidth_bytes_day_out”\n“bandwidth_bytes_month_in”\n“bandwidth_bytes_month_out”\n“bandwidth_bytes_yesterday_in”\n“bandwidth_bytes_yesterday_out”\n18. How to get current number of connections for a modem?\n\nRun a command\n\nss -o state established | grep -c :8038\n\nBut change 8038 with HTTP port of a desired proxy\n\n19. How to read SMS from a modem.\n\nYou have these options.\n\nBrowse to the modem IP ( it is shown as GW in proxysmart.sh show_status ) through the proxy. Click SMS button.\nrun proxysmart.sh list_sms_for_a_modem_by_imei_json 999999999999999 i.e. IMEI of required modem.\nClick SMS in the WebApp\n20. How to change WebApp password\n\nBy default it is set to proxy / proxy. The password sits on the server’s folder /etc/nginx/. It Can be updated from the Terminal , with the command as follows:\n\n sudo htpasswd -b /etc/nginx/htpasswd proxy NewAweSomePassword999999\n\nThen it will ask for password for current Ubuntu user.\n\nIf you want to change username as well, just delete the file and then assign new password\n\nsudo rm /etc/nginx/htpasswd\nsudo htpasswd -b /etc/nginx/htpasswd MyNewUsername NewAweSomePassword999999\n\nHow to change WEB port\n\nedit /etc/nginx/sites-enabled/proxysmart.nginx and set other port and restart Nginx.\n\n21. OS Spoofing\n\nOs Spoofing is used to simulate other OS TCP fingerprints, MacOS  iOS  Windows  Android\n\nHow to enable OS Spoofing?\n\nIn the WebApp set destination OS per each modem.\n\nHow to test OS Spoofing ?\n\nVisit one of these websites (IP checkers) through a proxy. Find something like “OS TCP fingerprints”.\n\nhttp://witch.valdikss.org.ru/\nhttps://thesafety.us/\nhttps://Whoer.net , extended results\nhttps://browserleaks.com/ip\n\nWhat OS can I spoof?\n\nMacOS  iOS  Windows  Android\n\nCan I dump OS TCP fingerprint from a real device and use it?\n\nYes, contact me.\n\nI enabled OS TCP spoofing, but it is not working!\n\nThe reason may be that the operator passes all traffic through its internal proxy, or in other way modifies TCP signatures. Then local OS TCP modifications are overwritten. Is it bad? No! Because still traffic looks natural as it was coming from this operator network.\n\nTry other operator.\n\n22. Performance tuning\n\nWhen >10 modems are added, and when modem list is generated slowly, play with MAX_PARALLEL_WORKERS_STATUS variable, e.g. set it to 2 or 4. On faster CPU’s it can be set to 8.\n\nAlso try to disable OS TCP reporting, i.e. set ENABLE_VALDIK=0 in /etc/proxysmart/conf.txt. It will also make modem list generation faster.\n\nAlso you can disable detailed status, set QUICK_STATUS=1 in /etc/proxysmart/conf.txt & refresh the WebApp.\n\n23. How to lock network mode per modem\n\nSet TARGET_MODE in its settings in the Proxysmart WebApp. Allowed values:\n\nauto\n3g\n4g\n24. What if a modem connected via 3G or 2G, and I want 4G?\n\nRotate its IP.\n\n25. I want to add extra users to a proxy\n\nIn the WebApp, click EDIT on a modem, add some extra users, click APPLY.\n\n26. Is IPV6 supported?\n\nYes but it’s off by default.\n\nOn modems , edit APN and set APN type for both IPv4 and IPv6 , e.g. Ip4Ip6 or Ip4+ip6, there is a dropdown list for that.\n\nOn Proxysmart box: Update /etc/proxysmart/conf.txt with\n\nALTNETWORKING_VERSION=2\nIPV6_SUPPORT=1\n\nand reset configuration proxysmart.sh reset_complete ; or even better do a reboot.\n\n27. Nagios integration.\n\nThere is a plugin embedded, run it as root,\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh IMEI\n\nor\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh NICKNAME\n\nso it will return OK/WARN/CRIT/UNKNOWN and corresponding exit code.\n\n28. Secure (anonymous) IP rotation links.\n\nThese links\n\nCan be safely passed to your customers. They don’t reveal real dongle parameters like IMEI or Nickname.\nThey don’t require HTTP basic authentication\nThey have limited lifetime , it is set in /etc/proxysmart/conf.txt as RESET_LINK_VALIDITY variable, (default value : 5 years).\nThey depend on proxy password. So, when you change proxy password - old IP rotation links will stop working.\n\nA link can be retrieved this way: Open dongle status (click on its IMEI!) in the WebApp, take RESET_SECURE_LINK→URL value.\n\nIf you realized you gave a link to a customer, and want to revoke it, just set new password for the proxy.\n\nIf you want to invalidate all links of all modems, set a new secret: set RESET_LINK_SECRET in /etc/proxysmart/conf.txt .\n\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n\nIt is needed for proper work of HTTP/3.0 which uses UDP.\n\nQUIC (UDP over socks5) will work either in your LAN or via a VPS. Steps are below.\n\nSteps on VPS :\n\nRun:\n\ninstall logrotate rule so Gost logs won’t fill up the disk space.\n\necho '\n/var/log/gost/*.log {\n    missingok\n    compress\n    notifempty\n    hourly\n    rotate 48\n    copytruncate\n}\n' > /etc/logrotate.d/gost\n\necho '35 * * * * root /usr/sbin/logrotate -v /etc/logrotate.d/gost' > /etc/cron.d/gost-logrotate \n\n\nInstall sudoers so proxysmart server can run commands with sudo on the VPS:\n\necho 'fwd  ALL=NOPASSWD:  ALL' >  /etc/sudoers.d/proxysmart \nchmod 400 /etc/sudoers.d/proxysmart\nusermod -s /bin/bash fwd\n\nInstall Gost v2\n\nARCH=linux-amd64\nVER=2.11.3\ncurl -L -o /tmp/gost.gz https://github.com/ginuerzh/gost/releases/download/v$VER/gost-$ARCH-$VER.gz\ngunzip -dc /tmp/gost.gz  > /usr/local/bin/gost.new\nchmod 755 /usr/local/bin/gost.new\nmv  /usr/local/bin/gost.new /usr/local/bin/gost\ngost -V\n\nInstall Gost v3\n\nVER=3.0.0-rc8\nARCH=linux_amd64\nURL=\"https://github.com/go-gost/gost/releases/download/v$VER/gost_${VER}_$ARCH.tar.gz\";\nD=`mktemp -d`;\n( cd $D;\n  curl -L -o /tmp/gost3.tgz \"$URL\";\n  tar xf /tmp/gost3.tgz gost;\n  mv gost /usr/local/bin/gost3.new \n  );\nrm -rf $D;\nchmod 755 /usr/local/bin/gost3.new;\nmv /usr/local/bin/gost3.new /usr/local/bin/gost3;\ngost3 -V\n\nif Haproxy is not installed, do nothing.\n\nif Haproxy installed: free up SOCKS ports (5xxx) from Haproxy: edit /etc/haproxy/haproxy.cfg and delete section frontend fe_SOCKS5 and restart it systemctl restart haproxy.service\n\nSteps on Proxysmart server :\n\nset in /etc/proxysmart/conf.txt :\n\nQUIC_SUPPORT=1\nGOST_VER=gost\n\nand run proxysmart.sh reset_complete.\n\nNote: make sure the VPS has enough RAM, each proxy needs 50MB of RAM. Also add swap if needed.\n\n29. “Dirty” IP reset.\n\nIt may be needed when you need even faster IP reset. In this case, post-checks are not made, so it is not sure if the modem really went online after IP reset. It can be activated by DIRTY_IP_ROTATION=1 in /etc/proxysmart/conf.txt.\n\n30. Exclude some modems\n\nIn /etc/proxysmart/conf.txt\n\nby Device name, populate this array IGNORED_DEV=( modem132 modem0000000002) – array of Network Interfaces that are not processed\nby IMEI, populate this array IGNORED_IMEI=( 9999999999999999 8888888888888888 ) – array of IMEI that are not processed\n31. Use custom Speedtest server.\n\nIt is useful when for some reason you want to run speed tests towards a custom server, instead of Ookla servers. So set up a Apache web server with a large file (500MB) and get 2 URL’s, one will test download and 2nd will test upload. The latter must accept large POST data.\n\nThe commands to setup a server part\n\napt install apache2\ndd if=/dev/urandom  of=/var/www/html/file.bin bs=1M count=500\n\nUpdate /etc/proxysmart/conf.txt with IP of the WEB server:\n\nSPEEDTEST_CUSTOM=1  \nDL_URL=http://$VPS/file.bin\nUL_URL=http://$VPS/i.php\n32. Minimum time between IP rotations\n\nIf you want to avoid too frequent IP rotations triggered by your users – set MINIMUM_TIME_BETWEEN_ROTATIONS=120 e.g. for 120 seconds minimum delay in /etc/proxysmart/conf.txt .\n\n33. How to block domains\nCheck (enable) DENIED_SITES_ENABLE in the WebApp\nDENIED_SITES_LIST is a list of domains that will be blocked, both HTTP and HTTPS, plus their subdomains. E.g. if you list porn.com, then also www1.porn.com,www.porn.com,porn.com are blocked.\n\nNote for Socks5 proxies\n\nWhen a domain blacklist is imposed, then by default users still can access blocked sites by their IP’s.\n\nIn order to prevent it, set DENY_IP_REQUESTS=1 and VERSION_3PROXY=0.9 in /etc/proxysmart/conf.txt and run proxysmart.sh reset_complete for resetting all configuration.\n\n33.a. How to allow only whitelisted domains.\nCheck (enable) WHITELIST_SITES_ENABLE in the WebApp\nWHITELIST_SITES_LIST is a list of domains that are allowed, while other are blocked. Both HTTP and HTTPS, plus their subdomains. E.g. if you list bbc.com, then also www.bbc.com,www1.bbc.com are listed.\n34. How to re-rotate IP when IP doesn’t change?\n\nIn /etc/proxysmart/conf.txt set RETRY_IP_ROTATIONS=1 .\n\nSo when Old_IP == New_IP, then IP rotation is retried. Up to MAX_RETRY_IP_ROTATIONS attempts which is by default 3.\n\n34.1 Prevent non-unique IP’s after IP rotation.\n\nFor example to prevent using IP’s that were in use 1 time (or more) within last 24h: set in /etc/proxysmart/conf.txt :\n\nRETRY_IP_ROTATIONS=1                 # enables Re-rotation\nNON_UNIQUE_IP_OCCURS=\"1\"             # how many times an IP must occur to be considered NonUnique. E.g. 1\nNON_UNIQUE_IP_PERIOD=\"24hour\"        # during which period an IP must occur to be considered NonUnique. E.g. 1day or 1hour\n35. How to forward proxy ports using HAproxy?\n\nWhy? In order to enable client IP whitelisting, i.e. 3proxy on proxysmart server will see original client IP and will be able to use whitelising.\n\nSteps:\n\n1. On Proxysmart server\n\nset PROXY_PORTS_FORWARDER_SOFTWARE=ssh+haproxy in /etc/proxysmart/conf.txt\nrun proxysmart.sh reset_complete for resetting all configuration.\n\n2. On the VPS\n\nRun apt install haproxy rsyslog\n\n3. Copy Haproxy and Syslog conf files from the Proxysmart server files to the VPS\n\nscp them from the Proxysmart server to the VPS. $VPS variable is sourced from the conf.txt\n\nsource /etc/proxysmart/conf.txt\ncd /usr/share/doc/proxysmart/examples/haproxy_integration/\nscp etc/haproxy/haproxy.* $VPS:/etc/haproxy/\nscp etc/rsyslog.d/49-haproxy.conf $VPS:/etc/rsyslog.d/\n\n4. On the VPS\n\nRun\n\ntouch /var/log/haproxy.log\nchown syslog:syslog /var/log/haproxy.log \n\nsystemctl restart rsyslog.service \nsystemctl restart haproxy.service \nsystemctl status haproxy.service\n\nMust be green and show active(running).\n\n5. Post check\n\nTest a proxy via VPS IP and you will original client IP in 3proxy logs.\n\n36. How to use newer 3proxy version 0.9 ?\n\nEdit /etc/proxysmart/conf.txt , set VERSION_3PROXY=0.9 , run proxysmart.sh reset_complete.\n\n37. Where are proxy logs.\n\nOn the Proxysmart server in a folder /var/log/3proxy/ , each filename is named for HTTP proxy port.\n\nLogs are rotated daily and 90 copies are saved, details are in /etc/logrotate.d/3proxy.\n\nLogs of IP rotations are in a folder /var/log/proxysmart/dongle_rotations/.\n\n37.1. No logs policy\n\nIf you want to run NoLogs policy, create a cron script that deletes the logs, i.e. the files\n\n/var/log/gost/*\n/var/log/3proxy/*\n/var/log/sniproxy*\n/var/log/haproxy*\n38. My proxies are slow.\n\nAssume a chain UsbModem→PC→VPS→ProxyUser. Final Proxy speed is limited by:\n\nDownload speed of the modem.\n\nIt can be measured on the side of the PC e.g. in the Proxysmart WebApp by clicking the Speedtest button.\n\nHow to improve it?\n\ntry other carriers\ntry other modems\ntry better location with better signal\nUpload speed from PC to VPS.\n\nNormally it correlates with quality of home internet (Fiber/xDSL) and can be measured by running speedtest on the PC in browser or in Terminal (speedtest-cli). Upload value has to be high.\n\nHow to improve it?\n\nget a better home internet with better upload\nswitch from WiFi to Ethernet\nDownload speed from VPS to the ProxyUser\n\nIt can be measured by downloading a file from VPS to the Proxyuser.\n\nHow to improve it?\n\nChange location of the VPS to a Cloud Hoster that has better reachability to the clients from all over the world\n39. My proxies are slower than the same SIM card in a Phone.\n\nReason 1: Compare LTE category of the modem and the phone. Phone has higher LTE cat e.g. 12..20, while modem has LTE cat 4..6 (depends).\n\nReason 2: when the speed is really bad (about 1mbps) then it is Operator's throttling. Perhaps you bought a plan that allows only phones/tablets and doesn't allow modems.\n\n40. How to forward proxy ports via each modem individually?\n\nWhy is it needed? When home base internet is unstable or its upload speed <15mbps.\n\nA VPS is needed in order to expose the ports this way ( see VPS integration chapter ).\n\nHow it works\n\nEach proxy forwards its port through its modem, not using base internet.\n\nPRO's :\n\nHome base internet speed & stability is not important\n\nCON's :\n\neach modem is working in bidirectional mode\nproxy speed is limited to 4G Upload speed which is slow\n\nSteps: on Proxysmart server\n\nset PROXY_PORTS_FORWARDER_TYPE=cell in /etc/proxysmart/conf.txt\nrun proxysmart.sh reset_complete for resetting all configuration.\n41. Auto-rebooting modems.\n\nSometimes only a reboot can fix a modem. In order to enable, set AUTOREBOOT_DONGLES=1 in /etc/proxysmart/conf.txt. How it works:\n\nif a situation occurs , “reboot score” of a modem is increased by the value, according to the situation:\nSCORE_IP_ROTATION_FAIL=10                   # score increments when IP rotation failed\nSCORE_IP_NOT_DETECTED=2                     # score increments when IP not detected\nSCORE_IP_RECONNECT_FAIL=10                  # score increments when IP not auto-reconnected\nSCORE_WWAN_DATA_FAIL=10                     # score increments when WWAN device can't establish Data connection\nSCORE_WEBAPP_FAIL=20                        # score increments when the modem's WebApp is stuck\nwhen the modem’s reboot score reaches MAX_REBOOT_SCORE then the modem is rebooted.\nspecial case, do USB reset instead of a reboot, when AUTO_USB_RESET_DONGLES is 1, it is useful when modems’ WEB APP is not available.\n42. My proxy is offline and showing Red in the WebApp.\n\nCheck if the modem has good signal.\n\nCheck if the modem has correct APN (set in its Web Dashboard).\n\nCheck if its SIM card is active (not blocked on Operator side) and is topped up.\n\nCheck the modem on another PC (e.g. your own desktop).\n\n43. Parallel processing of modems.\n\nEdit /etc/proxysmart/conf.txt , set PARALLEL_STARTUP=1 .\n\nSo the modems are processed in parallel, in the number of threads defined in MAX_PARALLEL_WORKERS_STATUS variable (default 8).\n\n44. IP's are rotated on their own\n\nIf you don't rotate IP's and they are detected each time as a new IP - it is natural behaviour of mobile provider, when it routes its clients through random different gateways every 1 minute or so. T-Mobile USA is known of doing so.\n\n45. Install logging of all requests in single place\n\n*the Goal*\n\nGet single log of all requests from Proxies (HTTP/Socks5) clients and VPN clients.\n\nInstallation On Proxysmart server\n\nEdit /etc/proxysmart/conf.txt , set SNIFFER_ENABLED=1 .\n\nrun proxysmart.sh reset_complete\n\nWatch the log /var/log/proxy_log.log on Proxysmart server.\n\nIt is rotated and 365 daily copies are stored on disk.\n\nIt can also be installed on a VPS if the VPS is working as proxies frontend.\n\nInstallation On VPS\n\nRequired files (copy from Proxysmart server to the VPS):\n\n/usr/local/bin/proxy_log.sh\n/etc/systemd/system/proxy_log.service\n/etc/logrotate.d/proxy_log\n\nrun :\n\napt update && apt install tshark\nsystemctl enable proxy_log --now \n\nWatch the log /var/log/proxy_log.log on VPS.\n\nLog format\n\nFile: /var/log/proxy_log.log\n\n    _ws.col.Time  frame.interface_name   ip.src  tcp.srcport   ip.dst   tcp.dstport  \n    #   1          2                        3       4           5           6\n    \n    socks.remote_name    socks.dst    socks.port   socks.dstport \n    # 7                         8         9         10\n    \n     http.request.method    http.host  \n    #   11                  12        \n\n     tls.handshake.extensions_server_name  x509ce.dNSName\n    #   13                                  14\n46. PPP modems\n\nThese are very old 3g modems like Huawei E303, E173, E156; ZTE MF110, MF193, MF190. In order to make them work with proxysmart,\n\nedit /etc/proxysmart/conf.txt and set PPP_MODEMS_ENABLE=1 .\n\nMake Quectel LTE modules work in PPP mode\n\nWhy? sometimes they fail working in QMI mode. So:\n\nedit /etc/proxysmart/conf.txt and set PPP_MODEMS_ENABLE=1\nplace a file /etc/udev/rules.d/21-wwan.rules\n# ignore QMI_WWAN endpoints on Quectel, to make it work in PPP mode.\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"2c7c\" , ATTRS{idProduct}==\"0125\",  ENV{.LOCAL_ifNum}==\"04\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\nre-plug Quectel modems or reboot Proxysmart server\n2. Project description\n1. project architecture (clients, servers, websites),\nonsite: box with Ubuntu, USB hub and modems\nremote: VPS with proxy ports (optional)\n2. Online services are used:\nhttp://ip.tanatos.org/ip.php which is simple PHP script that returns visitor’s IP. It is used to detect whether a modem is really online. Can be replaced with one of https://ifconfig.co or similar, but I was not happy with their reliabiality, they are down sometimes. The URL is defined in /etc/proxysmart/conf.txt.\nhttp://witch.valdikss.org.ru/ : used for detecting p0f and MTU\n3. CLI API\n1. show status\n\nShow full status of all modems, table (slower).\n\n# proxysmart.sh  show_status \n\nOutput:\n\nShow brief status of all modems, table, (faster)\n\nRun\n\n# proxysmart.sh  show_status_brief\n\nOutput:\n\nShow full status of all modems , json\n\n# proxysmart.sh  show_status_json \n\nOutput:\n\nShow status for a single modem, JSON\n\n# proxysmart.sh  show_single_status_json dongle111 \n\nOutput:\n\n2. full reconfiguration\n\nRun\n\n# proxysmart.sh reset_complete  \n\nOutput:\n\n3. apply setting for a modem by IMEI\n\nJSON output\n\n# proxysmart.sh   apply_settings_for_a_modem_by_imei  868723023562406 \n\nOutput:\n\nPlain text output.\n\n proxysmart.sh  apply_settings_for_a_modem_by_imei_raw    359999999999999 \n\noutput:\n\n4. reset IP on a modem\n\nArgs: IMEI or NICKNAME.\n\nJSON output:\n\n# proxysmart.sh   reset_modem_by_imei    899999999999999 \n# proxysmart.sh   reset_modem_by_imei    Dongle222\n\nOutput:\n\nPlain text output:\n\n# proxysmart.sh  reset_quick_nick  899999999999999\n# proxysmart.sh  reset_quick_nick  Dongle222\n\nOutput:\n\n5. reboot a modem\n\nArgs: Nickname or IMEI.\n\nTEXT Output\n\nJSON Output\n\n6.1. Reset a modem via USB\n\nCan accept DEV name, IMEI or Nickname. So\n\nFor Text output:\n\nFor Json output.\n\n6. Run speedtest on all modems at once\n# proxysmart.sh  speedtest all\n\nResponse:\n\n7. report bandwitdh\n\nOn a single modem\n\nWith arbitrary time interval.\n\nOn all modems:\n\n8. reset bandwidth counter on a modem\n\nJSON output\n\n9. list sms on a modem\n\nJSON output\n\n10. send sms\n\nPlain output:\n\nJSON output:\n\n11. purge SMS\n\nPurges SMS from all folders.\n\nCall by IMEI or nickname,\n\njson output:\n\n12. send ussd\n\nPlain output\n\nJSON output:\n\n13. get bandwidth counters from a modem\n\n..use bandwidth stats..\n\n14. Get IP rotations log for a modem\n\nBy Nickname or IMEI\n\n15. Get Top hosts from a modem\n\nBy Nickname or IMEI\n\n16. Report IP uniqueness\n\nJSON output.\n\nTEXT output.\n\n4. WEB API\n1. Web API description.\n\nWEB API endpoint is the URL that Proxysmart WebApp available at.\n\nIt can be - LAN_IP:8080 when you call it from the same LAN - VPS_IP:8080 when you forwardded ports to the Cloud VPS - STATIC_IP:8080 when you forwarded ports via your LAN router and your ISP gave you STATIC_IP\n\nAlso attach proper username:password (the -u parameter).\n\nWhenever below you are seeing localhost:8080, replace it with the actual WEB API endpoint.\n\n2. List all modems ( full status, slow)\n\nRequest:\n\ncurl 'http://localhost:8080/apix/show_status_json' -u proxy:proxy \n\nResponse:\n\n3. List all modems ( brief status, fast )\n\nRequest:\n\ncurl localhost:8080/apix/show_status_brief_json -u proxy:proxy\n\nResponse:\n\n4. Single modem status\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/show_single_status_json?arg=dongle111    -u proxy:proxy\ncurl http://localhost:8080/apix/show_single_status_json?arg=899999999999999    -u proxy:proxy\n\nResponse:\n\n5. Reset (change) IP on a modem.\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reset_modem_by_imei?IMEI=899999999999999 -u proxy:proxy\ncurl http://localhost:8080/apix/reset_modem_by_nick?NICK=dongle22 -u proxy:proxy\n\nResponse:\n\n6. Reboot a modem\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reboot_modem_by_imei -d IMEI=860493043888886 -u proxy:proxy\ncurl http://localhost:8080/apix/reboot_modem_by_nick -d NICK=dongle2 -u proxy:proxy\n\nResponse:\n\nETA: ~ 1.5 minute\n\n7. Send SMS\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-sms' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' \\\n    --data-urlencode 'phone=+11111111111' \\\n    --data-urlencode \"sms=txt txt fff\"\n\nResponse:\n\n8. Send USSD and read response\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-ussd' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' --data-urlencode 'ussd=*100#'\n\nResponse:\n\n9. Read SMS from a modem\n\nRequest:\n\ncurl 'http://localhost:8080/modem/sms/862329888888888?json=1' -u proxy:proxy\n\nResponse:\n\n10. Read bandwidth stats from a modem\n\nRequest:\n\ncurl localhost:8080/apix/bandwidth_report_json?IMEI=899999999999999   -u proxy:proxy\n\nResponse:\n\nWith arbitrary time interval:\n\nRequest:\n\ncurl -G http://localhost:8080/apix/get_counters_imei -X GET -d IMEI=868888888888888 --data-urlencode 'START=2023-01-28 18:10' --data-urlencode 'END=2023-01-28 19:20:01' -u proxy:proxy \n\nResponse:\n\n11. del\n12. Reset bandwidth stats for a modem\n\nRequest (by IMEI or nickname):\n\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=dongle111    -u proxy:proxy\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=2727233671671676    -u proxy:proxy\n\nResponse:\n\n{\"result\":\"success\",\"debug\":null}\n13. Reset a modem via USB\n\nRequest either - by network interface e.g. modem77 - by Nickname - by IMEI\n\ncurl localhost:8080/apix/usb_reset_modem_json?arg=modem77      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=dongle22      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n14. Get IP rotations log for a modem\n\nRequest - by Nickname - by IMEI\n\ncurl localhost:8080/apix/get_rotation_log?arg=899999999999999  -u proxy:proxy \ncurl localhost:8080/apix/get_rotation_log?arg=dongle2          -u proxy:proxy \n\nResponse:\n\n15. Apply settings for a modem\n\nRequest:\n\ncurl http://localhost:8080/modem/settings -d imei=862329099999999 -u proxy:proxy\n\nResponse:\n\n16. Purge SMS from a modem\n\nRequest either - by Nickname - by IMEI\n\ncurl localhost:8080/apix/purge_sms_json?arg=Nick77      -u proxy:proxy\ncurl localhost:8080/apix/purge_sms_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n{ \"result\": \"success\", \"msg\": \"\" }\n17. Get Top hosts from a modem\n\nRequest:\n\n18. Report IP uniquness\n\nRequest:\n\n19. Store a modem object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a modem”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n20. Export backup\n\nDestination format: v1\n\nSo it can be later imported in V1 version of Proxysmart.\n\nRequest:\n\nDestination format: v2\n\nSo it can be later imported in V2 version of Proxysmart.\n\nRequest:\n\n5. Mongodb integration\n\nMongodb contains a collection modems with elements, 1 element = 1 modem.\n\nMandatory fields are\n\nIMEI\nname\nhttp_port\nsocks_port\nproxy_login\nproxy_password\n\nOther fields are optional.\n\nAfter editing a record in Mongodb, apply settings for the edited modem ( /modem/settings WEB API call).\n\n5.1. Mongodb schema\n\nSample file modems.json with 2 modems. 1st modem: only mandatory fields. 2nd modem: also arbitrary fields.\n\nschema\n\nNotes:\n\nbw_quota : bandwidth quota in MB\nPROXY_VALID_BEFORE: expiry of a port\nCONNLIM: number of allowed new connnections during 1 minute\nbandlimin: upload speed (from proxy user perspective), bits/second\nbandlimout: download speed (from proxy user perspective), bits/second\nTARGET_MODE - the mode (3g/4g/auto/default) the mode will work in.\nOS - spoofed destination OS, can be\n(empty or absent field) No spoofing\n“android:1” Android, p0f compliant but slow\n“android:3” real Android, almost like Linux\n“macosx:3” macosx:3\n“macosx:4” real MacOSX 12.6 / iPhone 13 Pro Max\n“ios:1” ios:1, p0f compliant\n“ios:2” ios:2, real Iphone\n“windows:1” real Windows 10\n5.2. Moving Mongodb to other server\n\nSometimes you want to move Mongodb to a cloud server.\n\nIn order to do so\n\nkeep collection name modems\nif your new mongodb is Mongodb 5+ and doesn’t have backward compatibility with the older clients, upgrade Mongodb Client to 5th version. Run on the Proxysmart box:\napt purge mongo\\* -y\n. /etc/os-release \nrm -f /etc/apt/sources.list.d/mongodb*\ncurl -L https://www.mongodb.org/static/pgp/server-5.0.asc | gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/mongodb-5.0.gpg\necho \"deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu $VERSION_CODENAME/mongodb-org/5.0 multiverse\" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list \napt-get update\napt install mongodb-mongosh mongodb-database-tools -y\nln -sf /usr/bin/mongosh /usr/local/bin/mongo\nupdate MONGODB_URI to new Mongodb URI in /etc/proxysmart/conf.txt\nif your new mongodb URI has +srv extension , install a PIP module: /var/www/proxysmart/venv/bin/pip install \"pymongo[srv]\"\ntest new Mongodb URI (I assume you updated MONGODB_URI variable in conf.txt above):\n    . /etc/proxysmart/conf.txt;\n    mongoexport --quiet --uri=\"$MONGODB_URI\" -c modems --forceTableScan\n\nit should return array of all elements in the modems collection\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n6. Installation\n1. Initial installation\n\nInstall a fresh OS.\n\nSupported OS and architectures:\n\nUbuntu 22.04, 20.04 on amd64, arm64.\nDebian 11 or Raspberry PI OS (ex-Raspbian) on amd64, arm64, armhf ( see Raspberry PI OS Notes below).\nRaspberry PI : https://ubuntu.com/download/raspberry-pi , choose Ubuntu Server 22.04 64bit\nNormal PC/laptop: Choose Server or Desktop, https://ubuntu.com/download, choose Ubuntu 22.04\n\nArmhf (arm 32 bit) doesn’t have Mongodb support!\n\nThose steps will take 5..10 minutes.\n\nUnplug any 4g modems.\n\nAdd an APT repo.\n\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | \\\n    gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\n\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\n\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo $VERSION_CODENAME main\" \\\n    | sudo tee /etc/apt/sources.list.d/proxysmart.list\n\nsudo apt update\nsudo apt install proxysmart\n\nThen follow instructions: It will tell what to do next ( run 2 files ).\n\nsudo /usr/lib/proxysmart/install_pkgs.sh\nsudo /usr/lib/proxysmart/install_webapp.sh\n\nReboot or run sudo proxysmart.sh reset_complete.\n\nAfter that either enjoy the Demo version at http://localhost:8080 or check License section.\n\nRockpi Notes\n\nIf LOGRAM is enabled ( a folder /var/log.hdd exists). Disable logging:\n\nmongodb, edit /etc/mongodb.conf, comment logpath directive.\n\nRaspberry PI OS (ex-Raspbian) Notes\n\nits kernel doesn't have xt_cgroup module , so you have to rebuild its kernel and include this module. It is recommended to switch to Ubuntu instead.\n\nDevelopment version installation\n\nWhy? To unlock new features that are not yet in the Main version.\n\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | \\\n    gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\n\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\n\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.dev $VERSION_CODENAME main\" \\\n    | sudo tee /etc/apt/sources.list.d/proxysmart.list\n\nsudo apt update \nsudo apt install proxysmart\nsudo /usr/lib/proxysmart/install_pkgs.sh\nsudo /usr/lib/proxysmart/install_webapp.sh\n\nReboot or run sudo proxysmart.sh reset_complete.\n\n2. Upgrade\n\nRun these commands:\n\nNOTE when dpkg will ask whether to replace old config file with new one, answer N (No) or just press Enter.\n\nSo old config file is saved.\n\nsudo apt update \nsudo apt install proxysmart\nsudo /usr/lib/proxysmart/install_pkgs.sh\nsudo /usr/lib/proxysmart/install_webapp.sh\n\nReboot or run sudo proxysmart.sh reset_complete.\n\n3. Post Installation\n\nPlug in all 4g modems you have, wait ~20 sec to let them initialize.\n\nNow test if ip li shows you any modem* interfaces, otherwise reboot to apply UDEV rules.\n\nIf it does, continue next below. (Otherwise reboot to apply UDEV rules.)\n\nNow you can start all the modems:\n\nYou have to run proxysmart.sh reset_complete or reboot the multi-modem server.\n\nCommand proxysmart.sh show_status will return a table with proxy port, external IP’s.\n\nNavigate to the WebApp http://localhost:8080 proxy/proxy and assign login/password/nicknames/ports to the modems.\n\nTest reboot, reboot the box, wait 1 minute, make sure the WebApp shows the modems.\n\nWebApp\n\nVisit http://your_box_lan_IP_address:8080/ or http://localhost:8080/\n\nDefault user:password pair is proxy:proxy\n\n4. Cloud VPS integration.\n\nWhy? The VPS is needed to forward proxy ports from a cloud VPS IP back to the multi modem server, so proxy ports are available for all users around the world.\n\nDo I need a VPS?\n\nA VPS is NOT needed when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nThe VPS server can be a cheap 1GB DigitalOcean / Linode / Vultr VPS or similar.\n\nIt has to be located as close as possible to the 4g farm server ( for lowest ping ).\n\nVPS setup steps.\nOn multi modem server\n\nCopy content from the file /root/.ssh/fwd.pub [1]\n\nOn VPS\n\nCheck if your VPS has no firewall. Disable it if it has – Both inside Linux OS and in hoster panel.\n\nCreate a user fwd , run :\n\nuseradd -s /bin/true -m fwd\nusermod -p '*' fwd\nmkdir -p /home/<USER>/.ssh/\ntouch /home/<USER>/.ssh/authorized_keys\nchown -R fwd: /home/<USER>/\nchmod 700 /home/<USER>/.ssh/\nchmod 600 /home/<USER>/.ssh/authorized_keys\n\nAdjust SSH server configuration, run :\n\nmkdir -p /etc/ssh/sshd_config.d\necho '\nGatewayPorts clientspecified\nClientAliveInterval 3\nClientAliveCountMax 3\nMaxStartups 100:30:1000\nLoginGraceTime 10\n' > /etc/ssh/sshd_config.d/proxysmart.conf\n\nservice ssh restart\n\nedit the file and paste the content [1] you copied in the step above. It is public part of fwd.ssh key that is used for communication from Proxysmart to VPS.\n\nnano /home/<USER>/.ssh/authorized_keys\n\nSave the file (press Control O) and exit the editor (Control x)\n\nOn multi modem server\n\nin /etc/proxysmart/conf.txt :\n\nset VPS variable to VPS IP\nset PROXY_PORTS_FORWARDER_ENABLE=1\nrun proxysmart.sh reset_complete\nedit /etc/systemd/system/fwdssh-vps.service , change CONNECT_HOST to VPS IP\nPick a free port for SSH_REMOTE_PORT, in most cases 6902 is fine.\nPick a free port for WEB_REMOTE_PORT, in most cases 8080 is fine.\n\nRun:\n\nsystemctl daemon-reload\nsystemctl start fwdssh-vps\nsystemctl enable fwdssh-vps\nsystemctl status fwdssh-vps\n\nMake sure it is green.\n\nOn VPS\n\nissue the command ss -tnlp and you will see proxy ports are bound with sshd daemon. That means the ports are forwarded.\n\nOn your private desktop or any other PC\nvisit http://vps_ip:8080 for the WebApp , default login:password is proxy:proxy\nyou can ssh to VPS IP and port 6902, and that goes to the multi-modem-server:22.\nCloud VPS IP change\n\nIf CLoud VPS IP is changed, update it on multi-modem-server side by defining new VPS variable in the /etc/proxysmart/conf.txt file, and rerun proxysmart.sh reset_complete there.\n\nAlso change VPS IP in /etc/systemd/system/fwdssh-vps.service on multi-modem-server and run these:\n\nsystemctl daemon-reload\nsystemctl restart fwdssh-vps\nsystemctl status fwdssh-vps\n\nMake sure it is green.\n\n5. Forwarding ports through your own LAN router.\n\nWhy? It is needed to forward proxy ports from a your ISP IP address back to the multi modem server, so proxy ports are available for all users around the world.\n\nIt is suitable when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nSteps\n\nConsult with documentation of your LAN router. Forward these ports from ISP IP address to the LAN IP of proxysmart server:\n\nTCP 8001-8999 for HTTP proxies\nTCP 5001-5999 for SOCKS5 pproxies\nTCP 8080 for the WebApp\nTCP 1194 for Openvpn (if it is working in TCP mode)\nUDP 1194 for Openvpn (if it is working in UDP mode)\n\nNotes\n\nAlso edit /etc/proxysmart/conf.txt . Replace myrouter.com with your actual Hostname or IP addresss.\n\nSo proxy credentials & links will be shown with your actual Hostname or IP addresss.\n\nPROXY_PORTS_FORWARDER_ENABLE=0\nREWRITE_WEBAPP_URL=1\nREWRITE_WEBAPP_TO=\"http://myrouter.com:8080\"\nREWRITE_HOST_IN_PROXY_CREDS=1\nREWRITE_HOST_IN_PROXY_CREDS_TO=\"myrouter.com\"\n\nrun\n\nsystemctl disable --now gost_forward_vpn\nsystemctl disable --now fwdssh-vps\n\n.. so forwarding system ports to a VPS is disabled.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete .\n\n7. License\n1. Demo license\n\nInstallation is shipped with default demo license.\n\nIt allows you to run proxy on 1 modem.\n\nIn order to run more modems, buy a License.\n\n2. Requesting a License\n2.1. Get the machine data\n\nMethod1. From the WebApp:\n\nOpen the proxysmart WebApp at http://localhost:8080 or http://LAN_IP:8080\nExpand License section\nCopy machine_data value\n\nMethod2. From the CLI:\n\nOpen terminal\nRun sudo proxysmart.sh license_status\nCopy machine_data value\n2.2. Contact Sales Team\n\nSend the copied value to proxysmart.org\n\n2. License installation\n\nYou will be given the license and license signature. Both are sequences of numbers and characters. Then submit both either via WebApp or CLI:\n\nsubmitting via WebApp\n\nOpen the WebApp , http://localhost:8080 , expand License section and type in the keys & submit both.\n\nsubmitting via CLI\n\nrun commands\n\nproxysmart.sh submit_license LICENSE\nproxysmart.sh submit_license_signature LICENSE_SIGNATURE\n3. Restoring Demo license.\n\nIf your paid license expired or broken, restore DEMO license, run:\n\nsudo cp -v /usr/share/doc/proxysmart/examples/license.txt* /etc/proxysmart/\n\n8. Mobile (4G/5G) VPN\n\nTogether with building proxies, it is possible to build Residential VPN.\n\nAssumption is, your proxies are already available via Cloud VPS.\n\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n\nIf ports forwarded through a VPS\n\nSteps on VPS\n\nAssume the VPS is already “integrated” - see VPS integration topic.\n\nPick a free TCP port on the VPS, run ss -tnlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501. We will call it OPENVPN_REMOTE_PORT.\n\nSteps on Proxysmart server\n\nedit /etc/systemd/system/fwdssh-vps.service\nuncomment and set Environment=OPENVPN_LOCAL_PORT=1194\nuncomment and set Environment=OPENVPN_REMOTE_PORT=1501 , to the OPENVPN_REMOTE_PORT from the step above.\nedit /etc/proxysmart/conf.txt and set OPENVPN_SERVER_PORT=1501 , to the OPENVPN_REMOTE_PORT from the step above.\nset OPENVPN_INTEGRATION=1\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $VPS_IP:$OPENVPN_REMOTE_PORT/TCP )\n\nsystemctl daemon-reload\nsystemctl restart fwdssh-vps\n\nThis just enabled port forwarding of TCP port OPENVPN_REMOTE_PORT to localhost:OPENVPN_LOCAL_PORT.\n\nThen run /usr/lib/proxysmart/install_openvpn.sh , it will do the installation of Openvpn server.\n\nCheck if /etc/openvpn/server.conf has proto tcp otherwise set it there.\n\nCheck if /etc/openvpn/client.ovpn.template has proto tcp and proper remote (with VPS IP and OPENVPN_SERVER_PORT) otherwise set it there.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete . For each modem it will generate a VPN profile.\n\nRestart proxysmart WebApp so it shows a web link for downloading the profiles systemctl restart proxysmart .\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.2. Installation with TCP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded TCP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nedit /etc/proxysmart/conf.txt and set OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_INTEGRATION=1\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/TCP )\n\nThen run /usr/lib/proxysmart/install_openvpn.sh , it will do the installation of Openvpn server.\n\nCheck if /etc/openvpn/server.conf has proto tcp otherwise set it there.\n\nCheck if /etc/openvpn/client.ovpn.template has proto tcp and proper remote (with $EXT_IP and OPENVPN_SERVER_PORT ) otherwise set it there.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete . For each modem it will generate a VPN profile.\n\nRestart proxysmart WebApp so it shows a web link for downloading the profiles systemctl restart proxysmart .\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.3. Installation with UDP protocol (through VPS)\n\nExpand\n\n8.1.4. Installation with UDP protocol (through LAN router)\n\nExpand\n\n8.2. Extra profiles for a modem\n\nIf you need 2 extra VPN profiles for a dongle dongle1 , run openvpn_create_user dongle1@a or openvpn_create_user dongle1@b .\n\n8.3. Mobile VPN, how to connect\n\nSo download the VPN profiles and connect using any VPN client software.\n\nDownload and install software:\n\nWindows: https://openvpn.net/community-downloads/ or https://openvpn.net/client-connect-vpn-for-windows/\n\nMacOS: https://tunnelblick.net/\n\nAndroid: https://play.google.com/store/apps/details?id=de.blinkt.openvpn or https://f-droid.org/en/packages/de.blinkt.openvpn/\n\nIOS: https://apps.apple.com/us/app/openvpn-connect/id590379981\n\nImport downloaded OpenVPN profile, tap Connect.\nuse Login and Password from the corresponding proxy.\n8.4. Many users with the same profile\n\nBy default only 1 device (PC, mobile, tablet) can use 1 OpenVPN profile. If you want multiple devices use 1 profile, edit /etc/openvpn/server.conf , comment out ;duplicate-cn line by removing the ; character, and run proxysmart.sh reset_complete.\n\n8.5. Mobile VPN logs\n\nLogs of openvpn sessions - /var/log/openvpn/sessions.log. Format:\n\n'$time','$type','$local_port','$proto','$duration','$bytes_in','$bytes_out','$Real_IP','$Real_PORT','$Ovpn_CERT','$Ovpn_IP','$IMEI','$proxy_login','$auth_reject_why'\ntype - session_start / session_stop / auth_reject\nlocal_port - local port of Openvpn server\nproto - tcp-server or udp\nduration - when type is session_stop, how many the session lasted\nReal_IP, Real_PORT - of a client\nauth_reject_why - when type is session_stop, the reason why auth was rejected\n9. Bugs and Limitations\nLTE modules\nIPV6 is not fully supported\nLAN routers\nIPV6 is not fully supported\nOpenvpn profiles\nBandwidth quotas don’t apply to Openvpn users\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:changelog.v2dev ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nChangelog\n\n2024-07-11\n\nIPv6 on LAN 4G/5G modems(routers)\n\n2024-07-10\n\nfix for Openvpn client - TCP looped connections. Affected only servers with real IP's\n\n2024-07-05\n\nAdded Android Xiaomi Mi A2 Lite (Redmi6 PRO) in USB+ADB mode.\nFix TTL for Lan modems for Os TCP spoofing.\n\n2024-06-28\n\nimproved reboot of stuck Proxidize modems.\n\n2024-06-27\n\nSierraWireless on USA Verizon - improved ( WWAN_MODEMS_MSS_FIX setting)\n\n2024-06-26\n\nadded LAN 4G router modem, Tenda 5G03\n\n2024-06-23\n\nfix - p0f spoofing stopped working after IP rotation.\n\n2024-06-22\n\nQuectel EG91.\n\n2024-06-21\n\nAlcatel (TCL) MW43.\n\n2024-06-17\n\nImproved sniffer proxy logging (memory & disk usage).\n\n2024-06-16\n\nQuectel RM520N-GL - improved SMS reading.\n\n2024-06-13\n\nimproved IP leak prevention.\n\n2024-06-07\n\nZTE MF79 - support TARGET_MODE during IP rotation.\n\n2024-06-06\n\nWebApp - Ports bandwidth stats - show previous month usage.\nWebApp - System status - show overall TCP connections count.\n\n2024-06-05\n\nImproved IP-rotation on IPv4-only modems in IPv6-enabled system.\n\n2024-06-03\n\nBug fixed of Openvpn connections stuck on Ubuntu 20.04.\n\n2024-06-02\n\nImproved Debian 11 and 12 support.\n\n2024-05-28\n\nWebApp>SystemStatus - show main server external IPv4 and IPv6\n\n2024-05-24\n\nGlobal IP uniqueness check (if enabled, checking IP uniqueness across all present modems)\n\n2024-05-24\n\nHuawei stick mode (E303, E3272, E3276), improved APN detection.\n\n2024-05-23\n\nUbuntu 18.04, Debian 10 support dropped\n\n2024-05-21\n\nModems Autoreboot - bug fixed.\n\n2024-05-18\n\nWebApp - show ping latency and packets loss.\n\n2024-05-17\n\nWebApp - a button to download the license (works if the license is issued after 2024-05-17)\n\n2024-05-09\n\nnew p0f signatures - android 14, IPhone 12 max pro, MacOSX 13.5, Windows 11 х64 Desktop\n\n2024-05-06\n\nAndroid phones in raw ADB mode (no extra apps), in particular Motorola Stylus 5G on Android 12\n\n2024-04-29\n\nadded LAN 5G/4G router modem, ZTE MC7010CA\nadded LAN 4G router modem, ZTE MF289\n\n2024-04-27\n\nAnsible playbok for setting up a VPS\n\n2024-04-25\n\nWebApp - new Desktop/Mobile layout\n\n2024-04-19\n\nadded LAN 4G router modem, Tenda 4G03Pro\n\n2024-04-18\n\nadded LAN 5G/4G router modem, ZTE MC801A and ZTE MC8010CA\n\n2024-04-16\n\nadded p0f signature for “Windows 10 64bit Desktop”\n\n2024-04-12\n\nadded LAN 4G router modem, Cudy LT500\n\n2024-04-06\n\nWebApp - expired and overquota ports are shown in pink.\nImproved Ubuntu 24.04 support\n\n2024-04-01\n\nadded new modem - Huawei K5160\n\n2024-03-28\n\nan option to download only last month proxy logs\n\n2024-03-26\n\nLong running tasks are now queued up - Modem Rebooting, USB resetting a modem, downloading proxy logs.\n\n2024-03-25\n\nimproved Tshark memory & disk consumption\n\n2024-03-21\n\nSierraWireless EM7455 now can work in PPP mode\n\n2024-03-02\n\nV2.1 release\nWebApp - improved speed\nWebApp - show next IP rotation time\nWebApp - cached statuses of modems + background statuses updater\nWebApp - choose Openvpn profiles format, default or OpenvpnConnnect3.4+\nWebApp - choose how proxy credentials are shown ( host:port:login:password or proto://login:password@host:port )\nWebApp - ApplySettings replaced with Re-Add Device - which is useful when a device is stuck and settings can't be applied\nWebApp - Link to IP info\noption to use xt-tls iptables module for more reliable domains blocking\ndaily/monthly quotas are automatically unblocked on the next day/month\nImproved support of Socks5 UDP behind DMZ in LAN's\nRefactored Ping graphs (single daemon instead of multiple per-modem processes)\n\n2024-02-27\n\nHuawei K5150 can work in Stick (NCM) mode\n\n2024-02-19\n\nlog Caller of IP rotation (schedule or manual or WebAppClick or ByLink)\n\n2024-02-17\n\nprevent IP rotation by link if proxy is expired or over quota\n\n2024-02-15\n\nSupport of SierraWireless EM7565, EM7511\n\n2024-02-07\n\nshow live Reboot scores in the WebApp\n\n2024-02-05\n\nUSB devices tree and USB errors log, are shown in the WebApp\n\n2024-01-31\n\nImproved Fibocom L860 ip rotation\n\n2024-01-29\n\nMax connections test\n\n2024-01-24\n\nHuawei B535 support\n\n2024-01-23\n\nMTU fix (slow speed fix) on USA Verizone on LTE chips\n\n2024-01-19\n\nWebApp - dynamic search in the table\nWebApp - show failed modems on top\n\n2024-01-18\n\nReplace product name & company name in the WebApp (WhiteLabel'ing)\n\n2024-01-17\n\nAlcatel IK41, improved IP rotation\n\n2024-01-10\n\nCompacted layout of HTML tables\n\n2024-01-08\n\nSierra Wireless EM7455: force set APN even before checking LTE status\n\n2024-01-04\n\nTelegram alerts for expiring proxies\nAlerts to a WebHook\nAlerts are saved in a local log file.\n\n2024-01-01\n\ngraphs for VPN bandwidth\n\n2023-12-30\n\nglobal sites blocklist can be edited in the WebApp\nWebApp improved, true\\false settings are shown as radio buttons\n\n2023-12-27\n\nAlerts to Telegram\n\n2023-12-27\n\nWebApp: adding modems Notes.\n\n2023-12-25\n\nWebApp: mobile signal is shown as colored bars\n\n2023-12-22\n\nsupport of Huawei 5G CPE H112-370 (ip rotation, sms read)\n\n2023-12-21\n\nadded 'modems states' : adding queued→adding→added→deleting , failed_to_add.\n\n2023-12-20\n\nnew WebApp (condensed layout)\n\n2023-12-19\n\nreduced IP rotation time on Huawei (E3372, E8372, E55*) by approx. 4 seconds\n\n2023-12-12\n\nimproved support of Huawei E3372s modems.\nexport/import of LAN modems & other local settings during the Backup/Restore\n\n2023-12-07\n\nbumped version to 2.0.\nAndroid VPN can send UDP.\n\n2023-12-06\n\nAndroid proxies - cover reconnects.\nReboot call in the WebApp opens in new tab.\n\n2023-12-05\n\nAndroid App can read SMS.\n\n2023-12-04\n\nfix Fibcom L860 on long USB id's.\n\n2023-11-30\n\nSIMA7630C (ip rotation, SMS read, reboot)\n\n2023-11-27\n\nAndroid VPN - added.\nAndroid VPN - proper native DNS from the Mobile Carrier\n\n2023-11-25\n\nopenvpn sessions log in a separate file ( $OPENVPN_SESSIONS_LOG )\nussd on Quectel EP06\n\n2023-11-23\n\nsniffer script to log all proxies requests and original clients IP's. Can be installed either on VPS or on Proxysmart server, or both. It generates easy to read & analyze single log of who,when,what.\n\n2023-11-21\n\nsupport of LTE module - T77W595 / LT4120\n\n2023-11-12\n\nbuild for Ubuntu 24.04 Noble\nwarning about license due to expire\n\n2023-11-08\n\nimport & export mongodb – a button in the WebApp\nexport to Proxysmart v2\n\n2023-11-07\n\nmongodb uri can be set in /etc/proxysmart/conf.d/*.inc\n\n2023-11-05\n\nParallel initialization of proxies during reset_complete & reset_gently (enablable with PARALLEL_STARTUP variable)\nfix IP rotation counter logging\nReboot button in the main WebApp for rebooting the server\nAndroid proxies - automatic ports probing and detection.\n\n2023-11-03\n\nOlax U90 - support of a new modem\nnew WEB API call for storing a modem object\nQuectel modems now obey with TARGET_MODE during IP rotation\n\n2023-11-01\n\nAlcatel HH71 autoreconnect\nLAN modems: improved detection; ability to reboot\nfix for adding modems when IMEI is not properly detected\n\n2023-10-25\n\nQuectel EC20 support; Imvproved Chinese letters in SMS\nimproved stability of IK41 (stick mode)\n\n2023-10-23\n\nindividual OS TCP spoofing (per proxy)\n\n2023-10-18\n\nimproved speed on Android proxies over 27mbps\n\n2023-10-13\n\nimproved SMS on IK41 (stick mode)\n\n2023-10-09\n\nPossibility to set Bandwitdh Quota direction, one of (in, inout, out).\n\n2023-10-04\n\nimproved Huawei E3272 support.\n\n2023-10-03\n\nTarget mode of a modem during IP rotation: can be set in the WebApp\nDeleted TARGET_MODE from conf.txt (global variable)\n\n2023-09-28\n\nfixed installation in India where github is blocked\nandroid: VPN integration - done\n\n2023-09-26\n\nAdd Huawei 5g CPE H122-373\n\n2023-09-25\n\nimproved ME-906s modems IP rotation\nPrevent getting non-unique IP’s after rotation\n\n2023-09-13\n\nallow more than 1 device to use 1 VPN profile\nVodafone K5161h supported\n\n2023-09-10\n\nWebApp: show total online modems count\nWebApp: show VPN profile link in the main screen of the webapp\nFibocom L860 - custom APN fix & obeying custom Net Mode (TARGET_MODE)\ndeal with dongles Nicknames conflicts\ndeal with HTTP and SOCKS port conflicts\n\n2023-09-09\n\nAndroid proxies - improved Ping graphs; improved Carrier detection\n\n2023-08-30\n\nadded auto reconnect for DW5811e\n\n2023-08-28\n\nimproved QUIC support: add switch 3proxy/gost for Socks software in LAN ; add switch gost/gost3 as Socks software\n\n2023-08-25\n\nqmicli , added timeouts\n\n2023-08-23\n\nzteMF79NV support\n\n2023-08-21\n\nAlcatel modems (MW40,MW42,HH71) obeys $DEFAULT_HILINK_ADMIN_PASSWORD\nAlcatel MW45 improved support.\n\n2023-08-15\n\nadd Reset Counters button to the Bandwidth report page.\n\n2023-07-17\n\nimprove SMS sending on ZTE MF667, MF927, MF688V3E, MF823, MF833, MF93\n\n2023-07-13\n\nIP uniqueness report\n\n2023-07-10\n\nTop hosts report (WEB API method + WebApp table).\nAuto USB reset when modem’s WEB APP is stuck ( AUTO_USB_RESET_DONGLES=1 )\n\n2023-07-06\n\nAdd proxies built on Proxysmart android App\n\n2023-06-04\n\nSIM7600G proper SMS read\n\n2023-05-26\n\nMain proxy user:added Threads limit\nExtra users: added Threads limit & Dual_Auth IP\n\n2023-05-11\n\nadd SIM7600G (Lte cat4)\n\n2023-05-04\n\nadd Foxconn DW5821e (LTE cat 16)\n\n2023-05-02\n\nusb reset button in the WebApp\n\n2023-05-01\n\ncached list of the modems in the main screen of the WebApp\n\n2023-04-13\n\nsupport of pure old Stick(PPP) modems like Huawei E173, E156; ZTE MF110, MF193\nadd E3276 and E303 in Stick(NCM) mode\n\n2023-04-05\n\nability to hide old and new IP’s from the output of IP rotation links.\nauto reboot modems under some circumstances (check conf.txt for configuration)\n\n2023-03-29\n\nadded RequestsPerSecond graphs in modem status\nadded Pings graphs in modem status\n\n2023-03-28\n\nZyxel NR5103E support\n\n2023-03-20\n\n3proxy 0.9.x support\nability to block direct requests to IP’s (e.g. when clients resolve hostnames on their side and bypass Hostnames blocklist) and process only requests containing hostnames (Forces DNS resolution on proxy server side)\n\n2023-03-19\n\nautofix mass storage on Huawei modems\n\n2023-03-11\n\nRepeat IP rotation in case of failure (went online>offline, or the same IP received)\n\n2023-03-09\n\nfixed issue of DNS cache on modems\nBlack 4g modem – MSM8916 with /json WEB API\n\n2023-03-06\n\nability to reboot Quectel if no SIM\nNew UI\nshow graphs in modems statuses\n\n2023-03-03\n\ntimers fix (allows arbitrary nonstandard minutes like 31,61,1222)\nAlcatel IK41 in “stick” mode\n\n2023-03-02\n\nChinese MSM8916 modems support\nbug fix: JS was cached forever\nproxysmart.log - grandparent pid is logged\nproxysmart.log - timestamps preceed every line\nztemf79 : reconnect data\nNum of current connections is reported properly\nBrovi Huawei E3372-325 added support\nQuectel EC25-AFX added\nAPI for status query fixed\n\n2023-02-07\n\nMF667 modem support\n\n2023-02-06\n\nQuectel EM12G\n\n2023-02-05\n\nability to set a custom URL in secure rotation links\nability to set a custom HOST in proxy credentials ports\n\n2023-02-04\n\nQuectel EC25 can send USSD\nIK40 support + USSD\n\n2023-02-02\n\nE3276 can send USSD\nMF823 is properly shown\n\n2023-02-01\n\n3proxy logs contains IMEI and Nickname\n\n2023-01-29\n\nAPI for getting bandwidth stats within arbitrary time interval\n\n2023-01-28\n\nHuawei 3372h-325 “BROVI”\n\n2023-01-27\n\nmongodb cache bug fixed\n\n2023-01-22\n\nvpn: default UDP\n\n2023-01-19\n\nallow only DEMO version on VirtualBox\n\n2023-01-17\n\nshow anonymous link in main screen of the WebApp\n\n2023-01-15\n\nAdded new modem, Xproxy XH22 Wireless\nadded table of content in README\nProxies “Extra users”: added individual speed limits\n\n2023-01-14\n\nQuectel RM520 support (5g)\n\n2023-01-12\n\nFibocom L860, Ipv6 support\n\n2023-01-11\n\npurging sms (for Huawei in HiLink mode only)\n\n2023-01-10\n\nVodafone K5161z supported\n\n2023-01-08\n\nDocumentation: Dirty ip reset\n\n2023-01-07\n\nandroid phones integration (USB tethering mode & remote mobile tunnel mode).\n\n2023-01-04\n\nSecure IP reset links are now shown as full URL\n\n2023-01-03\n\nStatic VPN IP’s based on Index from Openvpn PKI\n\n2022-12-30\n\nWeb: editable field for Pho.Number.\nLink for downloading Openvpn profiles for modems\n\n2022-12-25\n\nQUIC support (UDP, HTTP/3.0) for SOCKS5 proxies , check README\n\n2022-12-22\n\nredefine variables in /etc/proxysmart/conf.d/*.inc\n\n2022-12-21\n\ndetect when CellOp redirects to their Billing Page\n\n2022-12-11\n\nSet minimum time between IP resets\na function for re-add a device: proxysmart.sh add_dev $DEV\nUDEV plug-n-play (hook scripts)\n\n2022-12-02\n\nHuawei dongles in NCM mode (WWAN+AT)\n\n2022-11-27\n\nFiboCom L860-gl : basic work + sms send\nHuawei ME906s : basic work + sms send\n\n2022-11-20\n\nSierraWireless EM7455\n\n2022-11-18\n\nCLR900A\nFranklinT10\n\n2022-11-12\n\nBW quota with PMACCTD, block outgoing access\nignore extra user when it matches with existing user (main user or another extra user)\nAlcatel MW40 - proper operation now\n\n2022-11-09\n\nVerizone Jetpack AC791L\nmore fast status for unknown modems models\n\n2022-11-08\n\nalcatel IK41: reboot, sms list, helper\n\n2022-11-01\n\nsolve issue when run_cache xxx times out and prints nothing, thus is executed over and over again\n\n2022-10-29\n\nDocumentation: secure rotation links\nget_ConnectionStatus_n response must contain OK\ncell op 425 02 ⇒ must be converted too\nmodel_shown returns “” sometimes ⇒ won’t fix, it happens when MAX_PARALLEL_WORKERS_STATUS is too large\n\n2022-10-27\n\nNovatel MIFI\nFranklin T9 (R717)\ncustom DNS servers for proxies\n\n2022-10-25\n\nNM disable modem*\n\n2022-10-19\n\nxproxy.io modems support\nbug fixed: Configuration file /etc/systemd/system/proxysmart.service is marked executable.\nwhen main proxy user == one of extra users, use extra user password\n\n2022-10-16\n\nvpn: blocklist of domains. make sniproxy enablable in conf.txt\nlicense revoking status checked online\n\n2022-10-15\n\nrework of denied domains list, *.domains are added automatically\n\n2022-10-12\n\nUF906 (KuWfi, Anydata, TianJie) modems integration\n\n2022-10-10\n\ndirty ip rotation support\nsecure ip rotation links with auto expiration\n\n2022-10-06\n\nOS spoofing for VPN users\nvpn: mongodb integration\n\n2022-10-04\n\nzte mf688T proxidize can send SMS\nd-link dwm222 basic support (beta)\nsignal reported in main table of the WebApp\n\n2022-09-30\n\nadded OS TCP spoofing with p0f3 signatures (WOW!!!), including Mac OS X, iOS, Android, Windows. Total rework of OSfooler-ng!\nmodem WEB server warm-up (when 1st request is ignored and 2nd and subsequent are processed)\n\n2022-09-22\n\nmodems helpers reorg, make them more fast & cached\nadded hourly IP rotation\n\n2022-09-19\n\nget by mongodb - get 1st value\n\n2022-09-16\n\nzte mf79VIVO support\nextra delay after IP rotation\n\n2022-09-12\n\nreport APN\nzte mf79 - sms send\n\n2022-09-08\n\nimproved support of ZTE MF927\n\n2022-09-03\n\nreport LTE band in full status\n\n2022-09-02\n\nsupport of 4g LAN routers like Huawei Bxxx\n\n2022-08-27\n\nreport IP rotation history as table\n\n2022-08-24\n\nreport IP rotation history\nWebApp can edit modems\nminor fixes: [ ignoring cell fwding in altnetworking2, dns in vpn, etc ]\nshow_model - more correct modems model showing\n\n2022-08-09\n\nopenvpn support (residential VPN!)\n\n2022-08-04\n\nipv6 bug fixed (ipv6 not assigned)\nchange mongodb uri in conf.txt\n\n2022-08-02\n\nnagios plugin exit code fixed ; nagios plugin moved to the main codebase\n\n2022-07-30\n\nfix: del symlink of altnetworking on installation\n\n2022-07-28\n\nhaproxy check >= 2.2\nDocumentation: – periodic IP rotation – ipv6 support – VPS integration\n\n2022-07-22\n\napply_settings = > if absent in DB, assign random creds.\n\n2022-07-20\n\nfixed bug when license stopped working because of floating (??) disk size and RAM size.\n\n2022-07-01\n\nconvert numeric cellop MCCMNC to Letters\ndel old show_status\n\n2022-06-23\n\nipv6, haproxy integration, systemd-slices (altnetworking2).\n\n2022-06-22\n\nmongodb: timeout handling\n\n2022-06-18\n\nmodem_names OR static udev map ⇒ make configurable\nis syslog-ng needed for Haproxy ?? revert back to rsyslog.\n\n2022-06-11\n\ndouble get_external_ip when doing IP reset via API\n\n2022-06-07\n\nPeriodic automatic IP rotation , set AUTO_IP_ROTATION per modem or globally\n\n2022-05-30\n\nadd usb_reset ( usb_reset_individual_DEV ) API, by nick\n\n2022-05-22\n\nadd proxy live counters based on RRD\n\n2022-05-07\n\nwait UDEV_xx after usb reset  reboot\nadd reboot API (CLI|WEB)\n\n2022-05-03\n\nreboot doesn’t wait till modem is added to the system; use reset_gently for that\n\n2022-05-01\n\nbug fixed when a modem has the same LAN as EthLAN, so the modem took priority over LAN\n\n2022-04-26\n\nzte MF971\nadd Quectel modems support\nreport ICCID\n\n2022-04-23\n\nbw_quota bug - quota restrictions are applied on next proxy request only\nread bw_quota, bandlimin, bandlimout, DENIED_SITES_ENABLE, DENIED_SITES_LIST, mtu, extra_users : from Mongodb\n\n2022-04-18\n\ncx /usr/lib/nagios/plugins/proxysmart-nagios-helper.sh\nDNS in Jinja2\n\n2022-04-08\n\nFIX: error 500 when modem not found\nlicense in web\nImei not unique ⇒ show in dash why\nIgnore reset if IMEI is in IGNORED_IMEIS\nDEV not added ⇒ why? show in status\nshow_status_brief, header must be 1st row\nNagios plugin doesn’t work with encrypted source\nZTE MF93: reboot call\n\n2022-03-31\n\nhuawei K5150: reset_ip , list_sms, reboot, send_sms\n\n2022-03-29\n\nmore UWSGI workers\ndemo license\ndeb package building\nmongodb template\n\n2022-03-28\n\nzte mf79: sms list, reboot, IP reset\n\n2022-03-22\n\nindividual blocklists( DENIED_SITES_ENABLE, DENIED_SITES_LIST )\n\n2022-03-21 (PRE)\n\nSms read\nDefault Modem admin pw in config.\nForwarder type in config ( WAN / CELL )\nusb reset yin config ( kris )\nworkmode: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.74 hipi.pl admin123 | grep -i workmode | cut -d“’” -f4\ncell_operator name: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.59 hlcli NetworkInfo | grep ‘ShortName’ | cut -d“ -f4\nadd_indiv.. and reset_gently\nN by modemN\nRandom port and nickname assignment if absent in the db\nlocks\nmake reset_gently WAIT till reset_complete is done\nJSON: report_bandwidth\nJSON function: apply settings for a modem\nstop redirector BEFORE ip rotation to prevent stalled SSH connection on remote SSH server\nleave checking global lock but never acquire it.\nJSON for list modems\nTABLEd status\nshow_status: model & cell_operator & workmode:\nreset_complete: del all Cgroups & ip rules\nJSON function: reset IP\nHipi scripts accept GW & AdminPW args\nmake screenshort w. CutyCapt\nVnstat autorestart\nflush ipv6 on modems\nmake show_status default\norder: curl, hipi w. pass, hipi w/o pass, hlcli, ETC\nOptional ttl fix: Y\nsms sort\nre-connect DATA on modems, if ext_ip empty ⇒ <NAME_EMAIL>\nre-connect DATA on modems, at reset_complete ⇒ <NAME_EMAIL>\nmtu. Report via Valdik\nmtu. Set\nACL : optional: allow admin only to browse HiLink webUI\nstart with no modems reset_complete doesn’t touch ⇒ adding a modem ⇒ reset_gently doesn’t work\ndel ipv6 leak from modems\nACL : optional: deny some sites\nProper logging , with ssh connection, stderr, as one SHOT\nno LAN test\ndefault login/pw for autogenerated proxies\nreport rotation time\nsafe way to read vars from included files\ndeploy script\nN from modemN May not work when iflink is not like modemXXX\ntrim CELLOP\nmanual: tell what http and socks5 ports to use\nmanual: how to use proxy, from internet, from lan\nallow ::1 in ip6tables\ndef.gw via 1 available modem – add to reset_complete\nrun_cached , to prevent multiple hipi runs\nfix EVAL() of commented lines like ‘v=v #ddd’\nregenerate ssh keys for FWD, if absent\nget rid of sourcing files: constants\nrun cached: log hit or miss\nmanual- check if your vps has firewall. disable it. both inside and in hoster panel.\nshow_status: If CurrNet is ’’, use workmode\ncheck faked route on reset_gnelty\nshow_status_json : redirector statuses\nupdate Manual: api calls & sample output\nspeedtest , up & down\ninclude redirector status in status. enabled, failed/running, uptime\nshow_status: list all proxies std format. depends on FWD_ENABLE, list $VPS:\nreplace XARGS+source to just xargs\nlog TimeTook for /var/log/proxysmart.log\ncrypted source autoGen\nbw report change\ncheck /var/run/proxysmart/reset_complete_done ⇒ show_status_json & show_status BREAK\nignored IMEI\nignored devices\nencode with shc.\nanti-p0f\nintegrate ZTE_MF6xx api ( goform/goprocess)\ncheck whether modem WebUI is available, for get_imei(), show_status_json(), get_model()\nadded Anydata UF906, Alcatel IK41 & MW40\nis_locked to JSON status\nglobal RESET LOCK\nzte MF823 ip change\nUSSD\nSMS\nreport phone uptime\nreport USB uptime\nIgnore FRESHLY inserted modems in reset_gently. Let them init\ndon’t rely on bash RANDOM:\nnagios plugin\nzte MF93D\nindividual JSON status for a modem\nWebapp: +GW, +Local_IP, +current hostname, send sms , send ussd\nDHCP more attempts\nWeb: brief status\nbrief status\nreconnect data - additional command, separate from reset_gently\nIP-based ACL\nspeed limits for proxies\nopen proxy (y)\ncheck modem WEB ⇒ change check_tcp to curl.\nDEB=mktemp /tmp/reset_modem_by_imei.XXXXXX – purge it\ndhcp → migrate to dhcpcd from Nmap\nQUICK_STATUS=0\\1, show only few details in show_status..\nreconnect data must give up when reset_complete not finished\nfix: add iptables -w to altnetowrking and Source\nget model ⇒ depend on manufacturer (from lsusb) for ZTE\nWEB API: report bandwidth\nCollectd + Web gui\nttl for _run_cached\nignore Ethernet-USB adapters\nmtu common and individual per modem\nhuman readable bytes counters in bandwidth reports\nmongodb: cached responses\nmake all functions accept IMEI|NICK\nmonthly Quota per user\nWebApp fix : Main WEB screen shows “Expecting value: line 1 column 1 (char 0)” and stucks and doesn’t do further refreshes\nExtra users in addition to main proxy users\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:changelog ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nChangelog\n\n2024-06-28\n\nimproved reboot of stuck Proxidize modems.\n\n2024-06-27\n\nSierraWireless on USA Verizon - improved ( WWAN_MODEMS_MSS_FIX setting)\n\n2024-06-26\n\nadded LAN 4G router modem, Tenda 5G03\n\n2024-06-23\n\nfix - p0f spoofing stopped working after IP rotation.\n\n2024-06-22\n\nQuectel EG91.\n\n2024-06-21\n\nAlcatel (TCL) MW43.\n\n2024-06-17\n\nImproved sniffer proxy logging (memory & disk usage).\n\n2024-06-16\n\nQuectel RM520N-GL - improved SMS reading.\n\n2024-06-13\n\nimproved IP leak prevention.\n\n2024-06-07\n\nZTE MF79 - support TARGET_MODE during IP rotation.\n\n2024-06-06\n\nWebApp - Ports bandwidth stats - show previous month usage.\nWebApp - System status - show overall TCP connections count.\n\n2024-06-05\n\nImproved IP-rotation on IPv4-only modems in IPv6-enabled system.\n\n2024-06-03\n\nBug fixed of Openvpn connections stuck on Ubuntu 20.04.\n\n2024-06-02\n\nImproved Debian 11 and 12 support.\n\n2024-05-28\n\nWebApp>SystemStatus - show main server external IPv4 and IPv6\n\n2024-05-24\n\nGlobal IP uniqueness check (if enabled, checking IP uniqueness across all present modems)\n\n2024-05-24\n\nHuawei stick mode (E303, E3272, E3276), improved APN detection.\n\n2024-05-23\n\nUbuntu 18.04, Debian 10 support dropped\n\n2024-05-21\n\nModems Autoreboot - bug fixed.\n\n2024-05-18\n\nWebApp - show ping latency and packets loss.\n\n2024-05-17\n\nWebApp - a button to download the license (works if the license is issued after 2024-05-17)\n\n2024-05-09\n\nnew p0f signatures - android 14, IPhone 12 max pro, MacOSX 13.5, Windows 11 х64 Desktop\n\n2024-05-06\n\nAndroid phones in raw ADB mode (no extra apps), in particular Motorola Stylus 5G on Android 12\n\n2024-04-29\n\nadded LAN 5G/4G router modem, ZTE MC7010CA\nadded LAN 4G router modem, ZTE MF289\n\n2024-04-27\n\nAnsible playbok for setting up a VPS\n\n2024-04-25\n\nWebApp - new Desktop/Mobile layout\n\n2024-04-19\n\nadded LAN 4G router modem, Tenda 4G03Pro\n\n2024-04-18\n\nadded LAN 5G/4G router modem, ZTE MC801A and ZTE MC8010CA\n\n2024-04-16\n\nadded p0f signature for “Windows 10 64bit Desktop”\n\n2024-04-12\n\nadded LAN 4G router modem, Cudy LT500\n\n2024-04-06\n\nWebApp - expired and overquota ports are shown in pink.\nImproved Ubuntu 24.04 support\n\n2024-04-01\n\nadded new modem - Huawei K5160\n\n2024-03-28\n\nan option to download only last month proxy logs\n\n2024-03-26\n\nLong running tasks are now queued up - Modem Rebooting, USB resetting a modem, downloading proxy logs.\n\n2024-03-25\n\nimproved Tshark memory & disk consumption\n\n2024-03-21\n\nSierraWireless EM7455 now can work in PPP mode\n\n2024-03-02\n\nV2.1 release\nWebApp - improved speed\nWebApp - show next IP rotation time\nWebApp - cached statuses of modems + background statuses updater\nWebApp - choose Openvpn profiles format, default or OpenvpnConnnect3.4+\nWebApp - choose how proxy credentials are shown ( host:port:login:password or proto://login:password@host:port )\nWebApp - ApplySettings replaced with Re-Add Device - which is useful when a device is stuck and settings can't be applied\nWebApp - Link to IP info\noption to use xt-tls iptables module for more reliable domains blocking\ndaily/monthly quotas are automatically unblocked on the next day/month\nImproved support of Socks5 UDP behind DMZ in LAN's\nRefactored Ping graphs (single daemon instead of multiple per-modem processes)\n\n2024-02-27\n\nHuawei K5150 can work in Stick (NCM) mode\n\n2024-02-19\n\nlog Caller of IP rotation (schedule or manual or WebAppClick or ByLink)\n\n2024-02-17\n\nprevent IP rotation by link if proxy is expired or over quota\n\n2024-02-15\n\nSupport of SierraWireless EM7565, EM7511\n\n2024-02-07\n\nshow live Reboot scores in the WebApp\n\n2024-02-05\n\nUSB devices tree and USB errors log, are shown in the WebApp\n\n2024-01-31\n\nImproved Fibocom L860 ip rotation\n\n2024-01-29\n\nMax connections test\n\n2024-01-24\n\nHuawei B535 support\n\n2024-01-23\n\nMTU fix (slow speed fix) on USA Verizone on LTE chips\n\n2024-01-19\n\nWebApp - dynamic search in the table\nWebApp - show failed modems on top\n\n2024-01-18\n\nReplace product name & company name in the WebApp (WhiteLabel'ing)\n\n2024-01-17\n\nAlcatel IK41, improved IP rotation\n\n2024-01-10\n\nCompacted layout of HTML tables\n\n2024-01-08\n\nSierra Wireless EM7455: force set APN even before checking LTE status\n\n2024-01-04\n\nTelegram alerts for expiring proxies\nAlerts to a WebHook\nAlerts are saved in a local log file.\n\n2024-01-01\n\ngraphs for VPN bandwidth\n\n2023-12-30\n\nglobal sites blocklist can be edited in the WebApp\nWebApp improved, true\\false settings are shown as radio buttons\n\n2023-12-27\n\nAlerts to Telegram\n\n2023-12-27\n\nWebApp: adding modems Notes.\n\n2023-12-25\n\nWebApp: mobile signal is shown as colored bars\n\n2023-12-22\n\nsupport of Huawei 5G CPE H112-370 (ip rotation, sms read)\n\n2023-12-21\n\nadded 'modems states' : adding queued→adding→added→deleting , failed_to_add.\n\n2023-12-20\n\nnew WebApp (condensed layout)\n\n2023-12-19\n\nreduced IP rotation time on Huawei (E3372, E8372, E55*) by approx. 4 seconds\n\n2023-12-12\n\nimproved support of Huawei E3372s modems.\nexport/import of LAN modems & other local settings during the Backup/Restore\n\n2023-12-07\n\nbumped version to 2.0.\nAndroid VPN can send UDP.\n\n2023-12-06\n\nAndroid proxies - cover reconnects.\nReboot call in the WebApp opens in new tab.\n\n2023-12-05\n\nAndroid App can read SMS.\n\n2023-12-04\n\nfix Fibcom L860 on long USB id's.\n\n2023-11-30\n\nSIMA7630C (ip rotation, SMS read, reboot)\n\n2023-11-27\n\nAndroid VPN - added.\nAndroid VPN - proper native DNS from the Mobile Carrier\n\n2023-11-25\n\nopenvpn sessions log in a separate file ( $OPENVPN_SESSIONS_LOG )\nussd on Quectel EP06\n\n2023-11-23\n\nsniffer script to log all proxies requests and original clients IP's. Can be installed either on VPS or on Proxysmart server, or both. It generates easy to read & analyze single log of who,when,what.\n\n2023-11-21\n\nsupport of LTE module - T77W595 / LT4120\n\n2023-11-12\n\nbuild for Ubuntu 24.04 Noble\nwarning about license due to expire\n\n2023-11-08\n\nimport & export mongodb – a button in the WebApp\nexport to Proxysmart v2\n\n2023-11-07\n\nmongodb uri can be set in /etc/proxysmart/conf.d/*.inc\n\n2023-11-05\n\nParallel initialization of proxies during reset_complete & reset_gently (enablable with PARALLEL_STARTUP variable)\nfix IP rotation counter logging\nReboot button in the main WebApp for rebooting the server\nAndroid proxies - automatic ports probing and detection.\n\n2023-11-03\n\nOlax U90 - support of a new modem\nnew WEB API call for storing a modem object\nQuectel modems now obey with TARGET_MODE during IP rotation\n\n2023-11-01\n\nAlcatel HH71 autoreconnect\nLAN modems: improved detection; ability to reboot\nfix for adding modems when IMEI is not properly detected\n\n2023-10-25\n\nQuectel EC20 support; Imvproved Chinese letters in SMS\nimproved stability of IK41 (stick mode)\n\n2023-10-23\n\nindividual OS TCP spoofing (per proxy)\n\n2023-10-18\n\nimproved speed on Android proxies over 27mbps\n\n2023-10-13\n\nimproved SMS on IK41 (stick mode)\n\n2023-10-09\n\nPossibility to set Bandwitdh Quota direction, one of (in, inout, out).\n\n2023-10-04\n\nimproved Huawei E3272 support.\n\n2023-10-03\n\nTarget mode of a modem during IP rotation: can be set in the WebApp\nDeleted TARGET_MODE from conf.txt (global variable)\n\n2023-09-28\n\nfixed installation in India where github is blocked\nandroid: VPN integration - done\n\n2023-09-26\n\nAdd Huawei 5g CPE H122-373\n\n2023-09-25\n\nimproved ME-906s modems IP rotation\nPrevent getting non-unique IP’s after rotation\n\n2023-09-13\n\nallow more than 1 device to use 1 VPN profile\nVodafone K5161h supported\n\n2023-09-10\n\nWebApp: show total online modems count\nWebApp: show VPN profile link in the main screen of the webapp\nFibocom L860 - custom APN fix & obeying custom Net Mode (TARGET_MODE)\ndeal with dongles Nicknames conflicts\ndeal with HTTP and SOCKS port conflicts\n\n2023-09-09\n\nAndroid proxies - improved Ping graphs; improved Carrier detection\n\n2023-08-30\n\nadded auto reconnect for DW5811e\n\n2023-08-28\n\nimproved QUIC support: add switch 3proxy/gost for Socks software in LAN ; add switch gost/gost3 as Socks software\n\n2023-08-25\n\nqmicli , added timeouts\n\n2023-08-23\n\nzteMF79NV support\n\n2023-08-21\n\nAlcatel modems (MW40,MW42,HH71) obeys $DEFAULT_HILINK_ADMIN_PASSWORD\nAlcatel MW45 improved support.\n\n2023-08-15\n\nadd Reset Counters button to the Bandwidth report page.\n\n2023-07-17\n\nimprove SMS sending on ZTE MF667, MF927, MF688V3E, MF823, MF833, MF93\n\n2023-07-13\n\nIP uniqueness report\n\n2023-07-10\n\nTop hosts report (WEB API method + WebApp table).\nAuto USB reset when modem’s WEB APP is stuck ( AUTO_USB_RESET_DONGLES=1 )\n\n2023-07-06\n\nAdd proxies built on Proxysmart android App\n\n2023-06-04\n\nSIM7600G proper SMS read\n\n2023-05-26\n\nMain proxy user:added Threads limit\nExtra users: added Threads limit & Dual_Auth IP\n\n2023-05-11\n\nadd SIM7600G (Lte cat4)\n\n2023-05-04\n\nadd Foxconn DW5821e (LTE cat 16)\n\n2023-05-02\n\nusb reset button in the WebApp\n\n2023-05-01\n\ncached list of the modems in the main screen of the WebApp\n\n2023-04-13\n\nsupport of pure old Stick(PPP) modems like Huawei E173, E156; ZTE MF110, MF193\nadd E3276 and E303 in Stick(NCM) mode\n\n2023-04-05\n\nability to hide old and new IP’s from the output of IP rotation links.\nauto reboot modems under some circumstances (check conf.txt for configuration)\n\n2023-03-29\n\nadded RequestsPerSecond graphs in modem status\nadded Pings graphs in modem status\n\n2023-03-28\n\nZyxel NR5103E support\n\n2023-03-20\n\n3proxy 0.9.x support\nability to block direct requests to IP’s (e.g. when clients resolve hostnames on their side and bypass Hostnames blocklist) and process only requests containing hostnames (Forces DNS resolution on proxy server side)\n\n2023-03-19\n\nautofix mass storage on Huawei modems\n\n2023-03-11\n\nRepeat IP rotation in case of failure (went online>offline, or the same IP received)\n\n2023-03-09\n\nfixed issue of DNS cache on modems\nBlack 4g modem – MSM8916 with /json WEB API\n\n2023-03-06\n\nability to reboot Quectel if no SIM\nNew UI\nshow graphs in modems statuses\n\n2023-03-03\n\ntimers fix (allows arbitrary nonstandard minutes like 31,61,1222)\nAlcatel IK41 in “stick” mode\n\n2023-03-02\n\nChinese MSM8916 modems support\nbug fix: JS was cached forever\nproxysmart.log - grandparent pid is logged\nproxysmart.log - timestamps preceed every line\nztemf79 : reconnect data\nNum of current connections is reported properly\nBrovi Huawei E3372-325 added support\nQuectel EC25-AFX added\nAPI for status query fixed\n\n2023-02-07\n\nMF667 modem support\n\n2023-02-06\n\nQuectel EM12G\n\n2023-02-05\n\nability to set a custom URL in secure rotation links\nability to set a custom HOST in proxy credentials ports\n\n2023-02-04\n\nQuectel EC25 can send USSD\nIK40 support + USSD\n\n2023-02-02\n\nE3276 can send USSD\nMF823 is properly shown\n\n2023-02-01\n\n3proxy logs contains IMEI and Nickname\n\n2023-01-29\n\nAPI for getting bandwidth stats within arbitrary time interval\n\n2023-01-28\n\nHuawei 3372h-325 “BROVI”\n\n2023-01-27\n\nmongodb cache bug fixed\n\n2023-01-22\n\nvpn: default UDP\n\n2023-01-19\n\nallow only DEMO version on VirtualBox\n\n2023-01-17\n\nshow anonymous link in main screen of the WebApp\n\n2023-01-15\n\nAdded new modem, Xproxy XH22 Wireless\nadded table of content in README\nProxies “Extra users”: added individual speed limits\n\n2023-01-14\n\nQuectel RM520 support (5g)\n\n2023-01-12\n\nFibocom L860, Ipv6 support\n\n2023-01-11\n\npurging sms (for Huawei in HiLink mode only)\n\n2023-01-10\n\nVodafone K5161z supported\n\n2023-01-08\n\nDocumentation: Dirty ip reset\n\n2023-01-07\n\nandroid phones integration (USB tethering mode & remote mobile tunnel mode).\n\n2023-01-04\n\nSecure IP reset links are now shown as full URL\n\n2023-01-03\n\nStatic VPN IP’s based on Index from Openvpn PKI\n\n2022-12-30\n\nWeb: editable field for Pho.Number.\nLink for downloading Openvpn profiles for modems\n\n2022-12-25\n\nQUIC support (UDP, HTTP/3.0) for SOCKS5 proxies , check README\n\n2022-12-22\n\nredefine variables in /etc/proxysmart/conf.d/*.inc\n\n2022-12-21\n\ndetect when CellOp redirects to their Billing Page\n\n2022-12-11\n\nSet minimum time between IP resets\na function for re-add a device: proxysmart.sh add_dev $DEV\nUDEV plug-n-play (hook scripts)\n\n2022-12-02\n\nHuawei dongles in NCM mode (WWAN+AT)\n\n2022-11-27\n\nFiboCom L860-gl : basic work + sms send\nHuawei ME906s : basic work + sms send\n\n2022-11-20\n\nSierraWireless EM7455\n\n2022-11-18\n\nCLR900A\nFranklinT10\n\n2022-11-12\n\nBW quota with PMACCTD, block outgoing access\nignore extra user when it matches with existing user (main user or another extra user)\nAlcatel MW40 - proper operation now\n\n2022-11-09\n\nVerizone Jetpack AC791L\nmore fast status for unknown modems models\n\n2022-11-08\n\nalcatel IK41: reboot, sms list, helper\n\n2022-11-01\n\nsolve issue when run_cache xxx times out and prints nothing, thus is executed over and over again\n\n2022-10-29\n\nDocumentation: secure rotation links\nget_ConnectionStatus_n response must contain OK\ncell op 425 02 ⇒ must be converted too\nmodel_shown returns “” sometimes ⇒ won’t fix, it happens when MAX_PARALLEL_WORKERS_STATUS is too large\n\n2022-10-27\n\nNovatel MIFI\nFranklin T9 (R717)\ncustom DNS servers for proxies\n\n2022-10-25\n\nNM disable modem*\n\n2022-10-19\n\nxproxy.io modems support\nbug fixed: Configuration file /etc/systemd/system/proxysmart.service is marked executable.\nwhen main proxy user == one of extra users, use extra user password\n\n2022-10-16\n\nvpn: blocklist of domains. make sniproxy enablable in conf.txt\nlicense revoking status checked online\n\n2022-10-15\n\nrework of denied domains list, *.domains are added automatically\n\n2022-10-12\n\nUF906 (KuWfi, Anydata, TianJie) modems integration\n\n2022-10-10\n\ndirty ip rotation support\nsecure ip rotation links with auto expiration\n\n2022-10-06\n\nOS spoofing for VPN users\nvpn: mongodb integration\n\n2022-10-04\n\nzte mf688T proxidize can send SMS\nd-link dwm222 basic support (beta)\nsignal reported in main table of the WebApp\n\n2022-09-30\n\nadded OS TCP spoofing with p0f3 signatures (WOW!!!), including Mac OS X, iOS, Android, Windows. Total rework of OSfooler-ng!\nmodem WEB server warm-up (when 1st request is ignored and 2nd and subsequent are processed)\n\n2022-09-22\n\nmodems helpers reorg, make them more fast & cached\nadded hourly IP rotation\n\n2022-09-19\n\nget by mongodb - get 1st value\n\n2022-09-16\n\nzte mf79VIVO support\nextra delay after IP rotation\n\n2022-09-12\n\nreport APN\nzte mf79 - sms send\n\n2022-09-08\n\nimproved support of ZTE MF927\n\n2022-09-03\n\nreport LTE band in full status\n\n2022-09-02\n\nsupport of 4g LAN routers like Huawei Bxxx\n\n2022-08-27\n\nreport IP rotation history as table\n\n2022-08-24\n\nreport IP rotation history\nWebApp can edit modems\nminor fixes: [ ignoring cell fwding in altnetworking2, dns in vpn, etc ]\nshow_model - more correct modems model showing\n\n2022-08-09\n\nopenvpn support (residential VPN!)\n\n2022-08-04\n\nipv6 bug fixed (ipv6 not assigned)\nchange mongodb uri in conf.txt\n\n2022-08-02\n\nnagios plugin exit code fixed ; nagios plugin moved to the main codebase\n\n2022-07-30\n\nfix: del symlink of altnetworking on installation\n\n2022-07-28\n\nhaproxy check >= 2.2\nDocumentation: – periodic IP rotation – ipv6 support – VPS integration\n\n2022-07-22\n\napply_settings = > if absent in DB, assign random creds.\n\n2022-07-20\n\nfixed bug when license stopped working because of floating (??) disk size and RAM size.\n\n2022-07-01\n\nconvert numeric cellop MCCMNC to Letters\ndel old show_status\n\n2022-06-23\n\nipv6, haproxy integration, systemd-slices (altnetworking2).\n\n2022-06-22\n\nmongodb: timeout handling\n\n2022-06-18\n\nmodem_names OR static udev map ⇒ make configurable\nis syslog-ng needed for Haproxy ?? revert back to rsyslog.\n\n2022-06-11\n\ndouble get_external_ip when doing IP reset via API\n\n2022-06-07\n\nPeriodic automatic IP rotation , set AUTO_IP_ROTATION per modem or globally\n\n2022-05-30\n\nadd usb_reset ( usb_reset_individual_DEV ) API, by nick\n\n2022-05-22\n\nadd proxy live counters based on RRD\n\n2022-05-07\n\nwait UDEV_xx after usb reset  reboot\nadd reboot API (CLI|WEB)\n\n2022-05-03\n\nreboot doesn’t wait till modem is added to the system; use reset_gently for that\n\n2022-05-01\n\nbug fixed when a modem has the same LAN as EthLAN, so the modem took priority over LAN\n\n2022-04-26\n\nzte MF971\nadd Quectel modems support\nreport ICCID\n\n2022-04-23\n\nbw_quota bug - quota restrictions are applied on next proxy request only\nread bw_quota, bandlimin, bandlimout, DENIED_SITES_ENABLE, DENIED_SITES_LIST, mtu, extra_users : from Mongodb\n\n2022-04-18\n\ncx /usr/lib/nagios/plugins/proxysmart-nagios-helper.sh\nDNS in Jinja2\n\n2022-04-08\n\nFIX: error 500 when modem not found\nlicense in web\nImei not unique ⇒ show in dash why\nIgnore reset if IMEI is in IGNORED_IMEIS\nDEV not added ⇒ why? show in status\nshow_status_brief, header must be 1st row\nNagios plugin doesn’t work with encrypted source\nZTE MF93: reboot call\n\n2022-03-31\n\nhuawei K5150: reset_ip , list_sms, reboot, send_sms\n\n2022-03-29\n\nmore UWSGI workers\ndemo license\ndeb package building\nmongodb template\n\n2022-03-28\n\nzte mf79: sms list, reboot, IP reset\n\n2022-03-22\n\nindividual blocklists( DENIED_SITES_ENABLE, DENIED_SITES_LIST )\n\n2022-03-21 (PRE)\n\nSms read\nDefault Modem admin pw in config.\nForwarder type in config ( WAN / CELL )\nusb reset yin config ( kris )\nworkmode: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.74 hipi.pl admin123 | grep -i workmode | cut -d“’” -f4\ncell_operator name: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.59 hlcli NetworkInfo | grep ‘ShortName’ | cut -d“ -f4\nadd_indiv.. and reset_gently\nN by modemN\nRandom port and nickname assignment if absent in the db\nlocks\nmake reset_gently WAIT till reset_complete is done\nJSON: report_bandwidth\nJSON function: apply settings for a modem\nstop redirector BEFORE ip rotation to prevent stalled SSH connection on remote SSH server\nleave checking global lock but never acquire it.\nJSON for list modems\nTABLEd status\nshow_status: model & cell_operator & workmode:\nreset_complete: del all Cgroups & ip rules\nJSON function: reset IP\nHipi scripts accept GW & AdminPW args\nmake screenshort w. CutyCapt\nVnstat autorestart\nflush ipv6 on modems\nmake show_status default\norder: curl, hipi w. pass, hipi w/o pass, hlcli, ETC\nOptional ttl fix: Y\nsms sort\nre-connect DATA on modems, if ext_ip empty ⇒ <NAME_EMAIL>\nre-connect DATA on modems, at reset_complete ⇒ <NAME_EMAIL>\nmtu. Report via Valdik\nmtu. Set\nACL : optional: allow admin only to browse HiLink webUI\nstart with no modems reset_complete doesn’t touch ⇒ adding a modem ⇒ reset_gently doesn’t work\ndel ipv6 leak from modems\nACL : optional: deny some sites\nProper logging , with ssh connection, stderr, as one SHOT\nno LAN test\ndefault login/pw for autogenerated proxies\nreport rotation time\nsafe way to read vars from included files\ndeploy script\nN from modemN May not work when iflink is not like modemXXX\ntrim CELLOP\nmanual: tell what http and socks5 ports to use\nmanual: how to use proxy, from internet, from lan\nallow ::1 in ip6tables\ndef.gw via 1 available modem – add to reset_complete\nrun_cached , to prevent multiple hipi runs\nfix EVAL() of commented lines like ‘v=v #ddd’\nregenerate ssh keys for FWD, if absent\nget rid of sourcing files: constants\nrun cached: log hit or miss\nmanual- check if your vps has firewall. disable it. both inside and in hoster panel.\nshow_status: If CurrNet is ’’, use workmode\ncheck faked route on reset_gnelty\nshow_status_json : redirector statuses\nupdate Manual: api calls & sample output\nspeedtest , up & down\ninclude redirector status in status. enabled, failed/running, uptime\nshow_status: list all proxies std format. depends on FWD_ENABLE, list $VPS:\nreplace XARGS+source to just xargs\nlog TimeTook for /var/log/proxysmart.log\ncrypted source autoGen\nbw report change\ncheck /var/run/proxysmart/reset_complete_done ⇒ show_status_json & show_status BREAK\nignored IMEI\nignored devices\nencode with shc.\nanti-p0f\nintegrate ZTE_MF6xx api ( goform/goprocess)\ncheck whether modem WebUI is available, for get_imei(), show_status_json(), get_model()\nadded Anydata UF906, Alcatel IK41 & MW40\nis_locked to JSON status\nglobal RESET LOCK\nzte MF823 ip change\nUSSD\nSMS\nreport phone uptime\nreport USB uptime\nIgnore FRESHLY inserted modems in reset_gently. Let them init\ndon’t rely on bash RANDOM:\nnagios plugin\nzte MF93D\nindividual JSON status for a modem\nWebapp: +GW, +Local_IP, +current hostname, send sms , send ussd\nDHCP more attempts\nWeb: brief status\nbrief status\nreconnect data - additional command, separate from reset_gently\nIP-based ACL\nspeed limits for proxies\nopen proxy (y)\ncheck modem WEB ⇒ change check_tcp to curl.\nDEB=mktemp /tmp/reset_modem_by_imei.XXXXXX – purge it\ndhcp → migrate to dhcpcd from Nmap\nQUICK_STATUS=0\\1, show only few details in show_status..\nreconnect data must give up when reset_complete not finished\nfix: add iptables -w to altnetowrking and Source\nget model ⇒ depend on manufacturer (from lsusb) for ZTE\nWEB API: report bandwidth\nCollectd + Web gui\nttl for _run_cached\nignore Ethernet-USB adapters\nmtu common and individual per modem\nhuman readable bytes counters in bandwidth reports\nmongodb: cached responses\nmake all functions accept IMEI|NICK\nmonthly Quota per user\nWebApp fix : Main WEB screen shows “Expecting value: line 1 column 1 (char 0)” and stucks and doesn’t do further refreshes\nExtra users in addition to main proxy users\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?idx=v1", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?idx=v2", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:android ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\n\nThis page is read only. You can view the source, but not change it. Ask your administrator if you think this is wrong.\n\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Proxies on Android phones – Proxysmart", "url": "https://proxysmart.org/android/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxies on Android phones\n\nBuilding proxies on Android phones is still in BETA.\n\nMethod1 – with the App\n1.1 – the App + cloud VPS\n\nDownload the APP\n\nFollow the guide (section 1.1)\n\nThe app is installed on phones, it connects to a VPS.\nProxysmart software is installed on the VPS.\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports – so it works bidirectionally\n1.2 – the App + Proxysmart in the LAN (“WIFI-Split”)\n\nDownload the APP\n\nFollow the guide (section 1.2)\n\nThe app is installed on phones, it connects to a Proxysmart server installed in the LAN by its local WIFI IP\nProxysmart software is installed on the local server in the LAN\nProxy ports forwarded either through a VPS, or through LAN router\nPRO: no USB hubs, just phones wall chargers\nPRO: Home WAN can be used for forwarding the proxy ports\nCON: no way to control a phone if MobileData goes off\nMethod2 – with Termux\n2.1 – Termux+USB tethering\n\nFollow the guide (section 2.1)\n\nAn app (Termux) is installed on phones.\nThey are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: phone can be controlled even if its MobileData goes off\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\n2.2 – Termux+VPS\n\nFollow the guide (section 2.2)\n\nAn app (Termux) is installed on phones.\nThe phones are connected to the Linux VPS running in the cloud\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports – so it works bidirectionally\nMethod 3 : USB tethering + ADB\n\nFollow the guide (section 3)\n\nPhones enable ADB and are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: no extra apps are installed on the phone, very simple to setup\nPRO: a phone can be controlled even if its MobileData goes off\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?do=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nTable of Contents\n1. Proxysmart manual [v2].\n1. Brief details\n2. Adding modems\n2.1 Adding a new modem (USB)\n2.2. Adding a LAN modem.\n2.3. Adding an Android phone\n2.4. Adding a virtual modem (backend proxy).\n3. Proxy credentials for new modems\n4. Where is WebApp\n5. How to use proxies\n6. del\n7. Reconfigure all modems & proxies.\n8. How to change proxy credentials for a modem. How to rename a modem.\n9. Reset (change) IP on a modem.\n10. How many modems can I run on a single computer?\n11. How to set TTL and why?\n12. How to set MTU and why?\n13. How to set extra settings for a proxy port.\n14. How can I access the web interface admin panel of each modem?\n14.1. How can I prevent access to modems web interface via proxy?\n15. How to set monthly traffic quota per modem?\n16. How to make my proxes Open (i.e. not requiring authentication )\n17. Get monthly/daily proxy usage.\n18. How to get current number of connections for a modem?\n19. How to read SMS from a modem.\n20. How to change WebApp password\n21. OS Spoofing\n22. Performance tuning\n23. How to lock network mode per modem\n24. What if a modem connected via 3G or 2G, and I want 4G?\n25. I want to add extra users on a modem\n26. Is IPV6 supported?\n27. Nagios integration.\n28. IP rotation links.\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n30. “Dirty” IP reset.\n31. Exclude some modems\n32. Use custom Speedtest server.\n33. Minimum time between IP rotations\n34. How to block domains\n35. How to allow only whitelisted domains.\n36. How to re-rotate IP when IP doesn’t change?\n37. Prevent non-unique IP’s after IP rotation.\n38. How to forward proxy ports using HAproxy?\n39. Custom DNS server for the proxies\n40. Where are proxy logs.\n41. No logs policy\n42. My proxies are slow.\n43. My proxies are slower than the same SIM card in a Phone.\n44. How to forward proxy ports via each modem individually?\n45. Auto-rebooting modems.\n46. My proxy is offline and showing Red in the WebApp.\n47. del\n48. IP's are rotated on their own\n49. Install logging of all requests in single place\n50. PPP modems\n51. Alerts to Telegram\n2. Project description\n1. architecture\n2. Online services are used:\n3. CLI API\n1. show status\n2. full reconfiguration\n3. apply setting for a modem by IMEI\n4. reset IP on a modem\n5. reboot a modem\n6.1. Reset a modem via USB\n6. Run speedtest\n7. report bandwitdh\n8. reset bandwidth counter on a port\n9. list sms on a modem\n10. send sms\n11. purge SMS\n12. send ussd\n13. get bandwidth counters from a modem\n14. Get IP rotations log for a modem\n15. Get Top hosts from a modem\n16. Report IP uniqueness\n4. WEB API\n1. Web API description.\n2. List all modems ( full status, slow)\n3. List all modems ( brief status, fast )\n3.1. List all active ports\n4. Single modem status\n5. Reset (change) IP on a modem.\n6. Reboot a modem\n7. Send SMS\n8. Send USSD and read response\n9. Read SMS from a modem\n10. Read bandwidth stats from a port\n11. del\n12. Reset bandwidth stats for a port\n13. Reset a modem via USB\n14. Get IP rotations log for a modem\n15. Apply settings for a modem\n15.1. Apply settings for a port\n15.2. Purge a port\n16. Purge SMS from a modem\n17. Get Top hosts from a modem\n18. Report IP uniquness\n19. Store a modem object in Mongodb\n20. Store a port object in Mongodb\n21. Export backup\n5. Mongodb integration\n5.1. Schema\n5.1.1. Modems\n5.1.2. Ports\n5.2 Workflow\n5.3 Configuration\n5.4 Moving Mongodb to other server\n6. Installation\n1. Initial installation\nDevelopment version installation\n2. Upgrade\n2.1. Upgrade from older V2\n2.2 Upgrade from V1\n3. Post Installation\n4. Cloud VPS integration.\nDo I need a VPS?\nVPS setup steps.\nCloud VPS IP change\n5. Forwarding ports through your own LAN router.\n7. License\n1. Demo license\n2. Requesting a License\n2. License installation\n3. Restoring Demo license.\n8. Mobile (4G/5G) VPN\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n8.1.2. Installation with TCP protocol (through LAN router)\n8.1.3. Installation with UDP protocol (through VPS)\n8.1.4. Installation with UDP protocol (through LAN router)\n8.2. Many users with the same profile\n8.3. Mobile VPN, how to connect\n8.4. Mobile VPN, FAQ\n8.4.1. Switch Openvpn protocol\n8.5. Mobile VPN logs\n9. Bugs and Limitations\nLTE modules\nAndroid phones\nVPN users\nPort ranges\nOS TCP Fingerprint spoofing\n1. Proxysmart manual [v2].\n1. Brief details\n\nThe software allows running your own 4g proxy farm. It runs on a Linux box (PC) with USB hub and the modems.\n\nFunctions:\n\nIP resets on modems (+ automatic rotation + checking IP uniqueness)\nWebApp for checking statuses of the modems, for creating users and ports, IP rotations\nWEB API for all actions\nBandwidth quotas and Speed limits per proxy\nExposing proxy ports, so they are available from world wide\nReading,sending SMS and USSD codes\nOS spoofing, to simulate OS TCP fingerprints of: MacOS, iOS, Windows,  Android (+any other OS)\nProxy ACLs (what to allow/deny to proxy users) - blacklists\nCreating mobile VPN together with proxies\nSocks5 supports UDP and QUIC (HTTP/3.0)\nNo leaks\nNative DNS from mobile carriers\nLarge set of supported USB modems, LAN routers, LTE modules, Android phones.\nBasic configuration.\n\nVariables are set in the WebApp→Global_settings and in /etc/proxysmart/conf.txt.\n\nEach variable has brief description in place.\n\n2. Adding modems\n2.1 Adding a new modem (USB)\nremove PIN from the modem’s SIM card and plug in the modem into USB port or USB hub.\nCheck whether your modem Web App (e.g. Huawei’s E8372 / E5xxx or ZTE MF79 or Alcatel MW4x ) requires authentication, and if it does, set its admin password to admin123. Basically to the value of DEFAULT_HILINK_ADMIN_PASSWORD variable in WebApp→GlobalSettings . Otherwise many functions will not work, and its IMEI will be detected similarly to 2-1.1.2\nPlug in the modem\nwait ~5 minutes or run sudo proxysmart.sh reset_gently\nthe modem will appear in the WebApp, click EDIT on it, assign some unique Nickname, HTTP & SOCKS5 ports, Login and Password, then click APPLY\nrefresh the WebApp\ndone!\n2.2. Adding a LAN modem.\n\nConfigure the server with 2 LAN cards\n\nAssume you have 2 LAN cards, enp6s0 main LAN, enp2s0 is dedicated for LAN modems:\n\nnmcli con\n\nNAME                UUID                                  TYPE      DEVICE \nWired connection 1  bbbee134-51c3-3830-801f-9636470e0708  ethernet  enp6s0\nWired connection 2  000ed912-2d99-3f37-882b-d79ad13102e7  ethernet  enp2s0 \nRename Wired connection 2 → HUBS\nnmcli con modify Wired\\ connection\\ 2 con-name HUBS\nDisable DHCP and IPV6 on HUBS and assign static IPv4 address\nnmcli con modify HUBS ipv4.method manual ipv4.addresses *************0/24 ipv6.method link-only ipv4.route-metric 300 \n\nSo you will add the LAN modems to ************/24 network as ************, ************ etc.\n\nsystemctl restart NetworkManager\n\nDelete old route\n\nip ro del default via ************\n\nConfirm you have only 1 default route via main LAN:\n\nip ro\n\nOutput\n\ndefault via *********** dev enp6s0 proto static metric 100 \n\nAdd the modem\n\nChange the modem’s web admin password to something stored in WebApp→GlobalSettings as DEFAULT_HILINK_ADMIN_PASSWORD variable.\nChange the modem’s IP to something unique e.g. *************\nPut the modem's LAN outlet into Ethernet switch together with the Proxysmart server.\nOn the Proxysmart server make sure you can ping the new modem by its IP you set in previous step.\nMake sure LAN_MODEMS_ENABLE=1 is in WebApp→GlobalSettings.\nAdd Lan modem in the Webapp→Edit_modems , scroll to the bottom, and add as lanmodem10 , ************* .\n\nThen either wait 5 minutes or run the command sudo proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n2.3. Adding an Android phone\n\nMain guide dedicated to adding Android Phones: Android phones guide\n\n2.4. Adding a virtual modem (backend proxy).\n\nA virtual modem is a in fact a redirect to a 3rd party proxy (HTTP or SOCKS5) so you can build own proxies based on that and resell them.\n\nThey even can be rotated if the backend proxy supports it.\n\nHow to add?\n\nMake sure BACKEND_PROXIES_ENABLE=1 is in WebApp→Global_settings .\n\nAdd them the Webapp→Edit_modems→Virtual modems\n\n, scroll to the bottom, and add each with the following fields\n\nid has to be in the form 'bproxy' + a number e.g. bproxy1 or bproxy2\ncreds is a line with credentials of the backend proxy e.g. ************************************* or socks5://Mylogin:Mypassword@Server:1080\nip_reset is an optional parameter , the URL for triggering IP rotation of the backend proxy\n\nClick SAVE\n\nThen either wait 5 minutes or run the command sudo proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n3. Proxy credentials for new modems\n\nWhen adding new modems, please use\n\nunique HTTP ports from 8001 to 8999,\nunique SOCKS ports from 5001 to 5999.\nunique nicknames like dongleXXX or whatever else. Don’t use nicknames like randomXXX, that are assigned automatically.\n4. Where is WebApp\n\nOne of\n\nhttp://localhost:8080/\nhttp://LAN_IP:8080/\nhttp://VPS_IP:8080/\n\nBy default login/password are proxy / proxy.\n\n5. How to use proxies\nIf proxy ports are forwarded via remote cloud VPS: then the proxies can be used from all over the Internet, by that VPS IP and proxy port numbers.\nFrom the same LAN where multimodem server is located: by the server’s LAN IP and proxy port numbers.\n6. del\n\ndel\n\n7. Reconfigure all modems & proxies.\n\nMethod1. Click the button “Reset Complete” on the main screen of the WebApp in the bottom.\n\nMethod2. In linux console, run: proxysmart.sh reset_complete\n\nAlso it is done after reboot automatically by a Cron job.\n\n8. How to change proxy credentials for a modem. How to rename a modem.\n\nWebApp method\n\nclick EDIT on a modem, set new port or password or nickname for a modem\nclick APPLY\n9. Reset (change) IP on a modem.\n\nThe options are below.\n\nFrom Web App\n\nClick Reset Ip button.\n\nFrom command line.\n\nRun: proxysmart.sh reset_quick_nick dongle1\n\nWhere dongle1 is a Dongle “nickname” that is seen from output of proxysmart.sh show_status\n\nFrom Web API.\n\ncheck WEB API section of this manual.\n\nHow to rotate a modem periodically?\n\nWebApp method\n\nUpdate modem’s settings in the WebApp and click APPLY.\n\nCron method\n\nInstall a Cron job. Edit a file /etc/cron.d/proxysmart, add a line ( or uncomment a commented line.. )\n\n*/10 * * * * root run-one /usr/local/bin/proxysmart.sh reset_quick_nick dongle3\n\nso that a modem with the Nickname dongle3 is rotated every 10 min.\n\nRepeat for each modem you want to rotate periodically.\n\n10. How many modems can I run on a single computer?\n\nHi , technically it depends on how powerful this PC is, and how intensively proxies are used.\n\nRaspberry PI - 4 proxies (roughly)\na miniPC (Intel NUC or similar) - up to 10\na Laptop like Core i5 - up to 30.\n\nAlso it depends on what Plan you buy.\n\nAlso it depends on USB configuration, for maximum number of modems:\n\ndisable USB3.0 in BIOS\nuse USB2.0 hubs\n11. How to set TTL and why?\n\nIn some cases custom TTL must be set in order to have Cell Operator think we are not using the modem in hotsport  tethering mode. I.e. we don’t share its data. By default Linux OS has ttl = 64. To change Cell Operator perception of the situation, we want to set it +1 i.e. 65.\n\nEdit WebApp→GlobalSettings and set CUSTOM_TTL_SET=1 and CUSTOM_TTL_VALUE=65 and regenerate settings.\n\n12. How to set MTU and why?\n\nIn some cases different MTU values connect with different types of ISP’s. You may want to change it.\n\nMtu can be only lowered. E.g. if you have MTU 1390, you can set 1340. Not opposite.\n\n- Edit /etc/proxysmart/conf.txt and set CUSTOM_MTU_SET=1 . - Set MTU in the WebApp for each modem.\n\n13. How to set extra settings for a proxy port.\n\nThose are optional and are set in the WebApp\n\nWHITELIST - allowed customers IP’s who are not required to type in proxy password (IP-based auth).\nbandwidth (speed) limit. Values are in mbps (megabits per second).\nDENIED_SITES_ENABLE (on/off) and DENIED_SITES_LIST (list of blocked sites patterns).\nBandwidth Quota (Megabytes) and Bandwidth Quota Type (daily/monthly/lifetime)\n14. How can I access the web interface admin panel of each modem?\n\nOpen WebApp. Locate the modem. Configure a proxy on your desktop browser.\n\nUse proxy login & password as desribed below (14.1 chapter).\n\nVisit modem IP via that proxy.\n\n14.1. How can I prevent access to modems web interface via proxy?\n\nSince 2023-09-10 it is enabled by default.\n\nEdit WebApp→GlobalSettings and set\n\nPROXY_ADMIN_ENABLE=1\nPROXY_ADMIN_LOGIN=SuperAdmin\nPROXY_ADMIN_PASS=Hqmz81mmZr\n\nAnd regenerate configs. So only admin user is allowed to use modems web interfaces, and normal proxy users are not.\n\n15. How to set monthly traffic quota per modem?\n\nIn the WebApp, set monthly traffic quota. Click EDIT & APPLY.\n\n16. How to make my proxes Open (i.e. not requiring authentication )\n\nSet OPEN_PROXIES=1 in WebApp→GlobalSettings and regenerate all configs.\n\nNote, when proxy ports are forrwarded via a VPS, the proxies are available to any internet user. Use it with caution.\n\n17. Get monthly/daily proxy usage.\n\nClick bandwitdh stats in the WebApp, or run proxysmart.sh bandwidth_report_json portIDXXX, you will see these columns:\n\n“bandwidth_bytes_day_in”\n“bandwidth_bytes_day_out”\n“bandwidth_bytes_month_in”\n“bandwidth_bytes_month_out”\n“bandwidth_bytes_yesterday_in”\n“bandwidth_bytes_yesterday_out”\n18. How to get current number of connections for a modem?\n\nRun a command\n\nss -o state established | grep -c :8038\n\nBut change 8038 with HTTP port of a desired proxy\n\n19. How to read SMS from a modem.\n\nYou have these options.\n\nClick Read SMS in the WebApp\nrun proxysmart.sh list_sms_for_a_modem_by_imei_json 999999999999999 i.e. IMEI of required modem.\nBrowse to the modem IP ( it is shown as GW in proxysmart.sh show_status ) through the proxy. Click SMS button.\n20. How to change WebApp password\n\nBy default it is set to proxy / proxy.\n\nIn the WebApp→GlobalSettings scroll to the bottom, set new WebApp password. NOTE: login remains proxy.\n\nCommand line method.\n\nsudo htpasswd -b /etc/nginx/htpasswd proxy NewAweSomePassword999999\n\nIf you want to change username as well, just delete the file and then assign new password\n\nsudo rm /etc/nginx/htpasswd\nsudo htpasswd -b -c /etc/nginx/htpasswd MyNewUsername NewAweSomePassword999999\n21. OS Spoofing\n\nOs Spoofing is used to simulate other OS TCP fingerprints.\n\nWhat OS can I spoof?\n\nMacOSX, iOS,  Windows,  Android.\n\nHow to enable OS Spoofing?\n\nIn the WebApp set the needed OS per each proxy port (click EDIT PORT).\n\nHow to test OS Spoofing ?\n\nVisit one of these websites (IP checkers) through a proxy. Find something like “OS TCP fingerprints”.\n\nhttp://witch.valdikss.org.ru/\nhttps://thesafety.us/\nhttps://whoer.net → extended results\nhttps://browserleaks.com/ip\n\nCan I dump OS TCP fingerprint from a real device and use it?\n\nYes, contact me.\n\nI enabled OS TCP spoofing, but it is not working!\n\nThe reason may be that the operator passes all traffic through its internal proxy, or in other way modifies TCP signatures. Then local OS TCP modifications are overwritten. Is it bad? No! Because still traffic looks natural as it was coming from this operator network.\n\nTry other operator.\n\n22. Performance tuning\n\nWhen >10 modems are added, and when modem list is generated slowly, play with MAX_PARALLEL_WORKERS_STATUS variable, on faster CPU’s it can be set to 8 or 16.\n\n23. How to lock network mode per modem\n\nSet TARGET_MODE in its settings in the Proxysmart WebApp. Allowed values:\n\nauto\n3g\n4g\n24. What if a modem connected via 3G or 2G, and I want 4G?\n\nRotate its IP.\n\n25. I want to add extra users on a modem\n\nIn the WebApp, create more ports on the modem, each port means a dedicated proxy.\n\n26. Is IPV6 supported?\n\nYes but it’s off by default.\n\nOn modems , edit APN and set APN type for both IPv4 and IPv6 , e.g. Ip4Ip6 or Ip4+ip6, there is a dropdown list for that.\n\nOn Proxysmart box: Update WebApp→GlobalSettings → IPV6_SUPPORT On\n\nand reset configuration proxysmart.sh reset_complete ; or even better do a reboot.\n\n27. Nagios integration.\n\nThere is a plugin embedded, run it as root,\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh IMEI\n\nor\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh NICKNAME\n\nso it will return OK/WARN/CRIT/UNKNOWN and corresponding exit code.\n\n28. IP rotation links.\n\nThese links\n\nCan be safely passed to your customers. They don’t reveal real dongle parameters like IMEI or Nickname.\nThey don’t require HTTP basic authentication\nThey have limited lifetime , it is set in WebApp→GlobalSettings as RESET_LINK_VALIDITY variable, (default value : 5years).\nThey depend on the proxy password. So, when you change the proxy password - old IP rotation links, associated with that proxy, will stop working.\n\nA link can be copied from the WebApp→Ports list. Each Port has its own IP rotation link. If one port rotates IP, then other ports of the same modem affected too.\n\nIf you realized you gave a link to a customer, and want to revoke it, just set new password for the proxy.\n\nIf you want to invalidate all links of all modems, set a new secret: set RESET_LINK_SECRET in WebApp→GlobalSettings .\n\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n\nIt is needed for proper work of HTTP/3.0 which uses UDP.\n\nQUIC (UDP over socks5) will work either in your LAN or via a VPS. Steps are below.\n\nSteps on VPS :\n\nMake sure you finished the Cloud VPS setup part, with Ansible\n\ncd /root/proxysmart-vps/\nnano vars.txt\n\n- set vps_socks5_udp: 1\n\nSave the file (press Control O) and exit the editor (Control x)\n\nRun Ansible again\n\nansible-playbook ./proxysmart-vps.yml\nSteps on Proxysmart server :\n\nset in WebApp->GlobalSettings → QUIC_SUPPORT : On.\n\nand reboot or reconfigure all proxies (run proxysmart.sh reset_complete ).\n\nNote: make sure the VPS has enough RAM, each proxy needs 50MB of RAM. Also add swap if needed.\n\n30. “Dirty” IP reset.\n\nIt may be needed when you need even faster IP reset. In this case, post-checks are not made, so it is not sure if the modem really went online after IP reset. It can be activated by DIRTY_IP_ROTATION=1 in WebApp→GlobalSettings\n\n31. Exclude some modems\n\nIn /etc/proxysmart/conf.txt\n\nby Device name, populate this array IGNORED_DEV=( modem132 modem0000000002) – array of Network Interfaces that are not processed\nby IMEI, populate this array IGNORED_IMEI=( 9999999999999999 8888888888888888 ) – array of IMEI that are not processed\n32. Use custom Speedtest server.\n\nIt is useful when for some reason you want to run speed tests towards a custom server, instead of Ookla servers. So set up a Apache web server with a large file (500MB) and get 2 URL’s, one will test download and 2nd will test upload. The latter must accept large POST data.\n\nThe commands to setup a server part\n\napt install apache2\ndd if=/dev/urandom  of=/var/www/html/file.bin bs=1M count=500\n\nUpdate WebApp→Global_settings with IP of the WEB server:\n\nSPEEDTEST_CUSTOM=1  \nDL_URL=http://v.v.v.v/file.bin\nUL_URL=http://v.v.v.v/i.php\n\nWhere v.v.v.v is VPS IP.\n\nDL_URL can be an URL of a large enough file (~100Mb+). And UL_URL is an URL that accepts large enough POST request.\n\n33. Minimum time between IP rotations\n\nIf you want to avoid too frequent IP rotations triggered by your users – set MINIMUM_TIME_BETWEEN_ROTATIONS=120 e.g. for 120 seconds minimum delay in WebApp→Global_settings .\n\n34. How to block domains\n\nIndividual (per proxy) block lists\n\nCheck (enable) DENIED_SITES_ENABLE in the WebApp\nDENIED_SITES_LIST is a list of domains that will be blocked, both HTTP and HTTPS, plus their subdomains. E.g. if you list porn.com, then also www1.porn.com,www.porn.com,porn.com are blocked.\n\nGlobal block list - for all proxies\n\nin WebApp→Global_settings set DENIED_SITES_ENABLE and paste domains or IP's into DENIED_SITES_LIST , click SAVE and re-apply all modems settings.\n\nNote for Socks5 proxies\n\nWhen a domain blacklist is imposed, then by default users still can access blocked sites by their IP’s.\n\nIn order to prevent it, set DENY_IP_REQUESTS=1 in WebApp→Global_settings and run proxysmart.sh reset_complete for resetting all configuration (or reboot).\n\n35. How to allow only whitelisted domains.\n\nThe feaure it not ready.\n\n36. How to re-rotate IP when IP doesn’t change?\n\nIn WebApp→Global_settings set RETRY_IP_ROTATIONS=1 .\n\nSo when Old_IP == New_IP, then IP rotation is retried. Up to MAX_RETRY_IP_ROTATIONS attempts which is by default 3.\n\n37. Prevent non-unique IP’s after IP rotation.\n\nFor example to prevent using IP’s that were in use 1 time (or more) within last 24h: set in WebApp→Global_settings :\n\nRETRY_IP_ROTATIONS=1                 # enables Re-rotation\nNON_UNIQUE_IP_OCCURS=\"1\"             # how many times an IP must occur to be considered NonUnique. E.g. 1\nNON_UNIQUE_IP_PERIOD=\"24hour\"        # during which period an IP must occur to be considered NonUnique. E.g. 1day or 1hour\n38. How to forward proxy ports using HAproxy?\n\nWhy? In order to enable client IP whitelisting, i.e. 3proxy on proxysmart server will see original client IP and will be able to use whitelising.\n\nSteps:\n\n1. On Proxysmart server\n\nset PROXY_PORTS_FORWARDER_SOFTWARE=ssh+haproxy in WebApp→Global_settings\nrun proxysmart.sh reset_complete for resetting all configuration.\n\n2. On the VPS\n\ncd /root/proxysmart-vps/\nnano vars.txt\n\n- set haproxy_enabled: 1\n\nSave the file (press Control O) and exit the editor (Control x)\n\nRun Ansible again\n\nansible-playbook ./proxysmart-vps.yml\n\n3. Post check\n\nTest a proxy via VPS IP and you will original client IP in 3proxy logs.\n\n39. Custom DNS server for the proxies\n\nEdit /etc/proxysmart/conf.txt and set DNS_SERVER_PROXIES=“*******” where `*******` is a custom DNS server, it must be publicly available.\n\nClick the button “Reset Complete” on the main screen of the WebApp in the bottom or in the console, run: sudo proxysmart.sh reset_complete or reboot the server.\n\n40. Where are proxy logs.\n\nOn the Proxysmart server in a folder /var/log/3proxy/ , each filename is named for HTTP proxy port.\n\nLogs are rotated daily and 90 copies are saved, details are in /etc/logrotate.d/3proxy.\n\nLogs of IP rotations are in a folder /var/log/proxysmart/dongle_rotations/.\n\n41. No logs policy\n\nIf you want to run NoLogs policy, create a cron script that deletes the logs, i.e. the files\n\n/var/log/gost/*\n/var/log/3proxy/*\n/var/log/sniproxy*\n/var/log/haproxy*\n42. My proxies are slow.\n\nAssume a chain UsbModem→PC→VPS→ProxyUser. Final Proxy speed is limited by:\n\nDownload speed of the modem\nUpload speed from PC to VPS\nDownload speed from VPS to the ProxyUser\nDownload speed of the modem.\n\nIt can be measured on the side of the PC e.g. in the Proxysmart WebApp by clicking the Speedtest button.\n\nHow to improve it?\n\ntry other carriers\ntry other modems\ntry better location with better signal (i.e. not your Home)\nUpload speed from PC to VPS.\n\nNormally it correlates with quality of home internet (Fiber/xDSL) and can be measured by running speedtest on the PC in browser or in Terminal (speedtest-cli). Upload value has to be high.\n\nWith different types of port forwardings:\n\nwan (Home Internet is used for ports forwarding) : remote proxy user's DownloadSpeed is limited to minimum of (ModemDownloadSpeed, HomeInternetUploadSpeed )\n\ncell (each modem forwards its proxies through its internet) : remote proxy user's DownloadSpeed is limited to minimum of (ModemDownloadSpeed, ModemUploadSpeed )\n\nHow to improve it?\n\nget a better home internet with better upload\nswitch from WiFi to Ethernet\nDownload speed from VPS to the ProxyUser\n\nIt can be measured by downloading a file from VPS to the Proxyuser.\n\nHow to improve it?\n\nChange location of the VPS to a Cloud Hoster that has better reachability to the clients from all over the world\n43. My proxies are slower than the same SIM card in a Phone.\n\nReason 1: Compare LTE category of the modem and the phone. Phone has higher LTE cat e.g. 12..20, while modem has LTE cat 4..6 (depends).\n\nReason 2: when the speed is really bad (about 1mbps) then it is Operator's throttling. Perhaps you bought a plan that allows only phones/tablets and doesn't allow modems.\n\n44. How to forward proxy ports via each modem individually?\n\nWhy is it needed? When home base internet is unstable or its upload speed <15mbps.\n\nA VPS is needed in order to expose the ports this way ( see VPS integration chapter ).\n\nHow it works\n\nEach proxy forwards its port through its modem, not using base internet.\n\nPRO's :\n\nHome base internet speed & stability is not important\n\nCON's :\n\neach modem is working in bidirectional mode\nproxy speed is limited to 4G Upload speed which is slow\n\nSteps: on Proxysmart server\n\nset PROXY_PORTS_FORWARDER_TYPE=cell in WebApp→Global_settings\nrun proxysmart.sh reset_complete for resetting all configuration.\n45. Auto-rebooting modems.\n\nSometimes only a reboot can fix a modem. In order to enable, set AUTOREBOOT_DONGLES=1 in WebApp→Global_settings. How it works:\n\nif a situation occurs , “reboot score” of a modem is increased by the value, according to the situation:\nSCORE_IP_ROTATION_FAIL=10                   # score increments when IP rotation failed\nSCORE_IP_NOT_DETECTED=2                     # score increments when IP not detected\nSCORE_IP_RECONNECT_FAIL=10                  # score increments when IP not auto-reconnected\nSCORE_WWAN_DATA_FAIL=10                     # score increments when WWAN device can't establish Data connection\nSCORE_WEBAPP_FAIL=20                        # score increments when the modem's WebApp is stuck\nwhen the modem’s reboot score reaches MAX_REBOOT_SCORE then the modem is rebooted.\nspecial case, do USB reset instead of a reboot, when AUTO_USB_RESET_DONGLES is 1, it is useful when modems’ WEB APP is not available.\n46. My proxy is offline and showing Red in the WebApp.\nCheck if the modem has good signal.\nCheck if the modem has correct APN (set in its Web Dashboard).\nCheck if its SIM card is active (not blocked on Operator side) and is topped up.\nCheck the modem on another PC (e.g. your own desktop).\n47. del\n48. IP's are rotated on their own\n\nIf you don't rotate IP's and they are detected each time as a new IP - it is natural behaviour of mobile provider, when it routes its clients through random different gateways every 1 minute or so. T-Mobile USA is known of doing so.\n\n49. Install logging of all requests in single place\n\nWhy? Get single log of all requests from Proxies (HTTP/Socks5) clients and VPN clients.\n\nInstallation On Proxysmart server\n\nIn the WebApp→GlobalSettings set SNIFFER_ENABLED=1 and click Apply.\n\nrun proxysmart.sh reset_complete\n\nWatch the log /var/log/proxy_log.log on Proxysmart server.\n\nIt is rotated and 365 daily copies are stored on disk.\n\nThen it is bound to a button “Download Proxy Logs”.\n\nIt can also be installed on a VPS if the VPS is working as proxies frontend.\n\nInstallation On VPS\n\nnot supported yet.\n\nLog format\n\nFile: /var/log/proxy_log.log\n\n    _ws.col.Time  frame.interface_name   ip.src  tcp.srcport   ip.dst   tcp.dstport  \n    #   1          2                        3       4           5           6\n    \n    socks.remote_name    socks.dst    socks.port   socks.dstport \n    # 7                         8         9         10\n    \n     http.request.method    http.host  \n    #   11                  12        \n\n     tls.handshake.extensions_server_name  x509ce.dNSName\n    #   13                                  14\n50. PPP modems\n\nThese are very old 3g modems like Huawei E303, E173, E156; ZTE MF110, MF193, MF190. In order to make them work with proxysmart,\n\nedit WebApp→GlobalSettings and set PPP_MODEMS_ENABLE=1 .\n\nMake Quectel / Sierra Wireless LTE modules work in PPP mode\n\nWhy? sometimes they fail working in QMI mode. So:\n\nedit WebApp→GlobalSettings and set PPP_MODEMS_ENABLE=1\nplace a file /etc/udev/rules.d/21-wwan.rules\n# ignore QMI_WWAN endpoints on Quectel, to make it work in PPP mode.\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"2c7c\" , ATTRS{idProduct}==\"0125\",  ENV{.LOCAL_ifNum}==\"04\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\n# ignore QMI_WWAN endpoints on SierraWireless  , to make it work in PPP mode. Save to 21-wwan.rules:\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"413c\" , ATTRS{idProduct}==\"81b6\",  ENV{.LOCAL_ifNum}==\"08\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\nre-plug modems or reboot Proxysmart server\n51. Alerts to Telegram\n\nIn Telegram start a chat with a bot https://t.me/userinfobot and get your Telegram numeric ID.\n\nIn Proxysmart WebApp→GlobalSettings , set TG_ALERTS_ENABLE ; and set TG_ALERTS_RECEIVER to your Telegram numeric ID.\n\nIn Telegram start a chat with Proxysmart bot https://t.me/nagios737bot and send 'hi'.\n\nAfter that the bot will send you alerts.\n\n2. Project description\n1. architecture\nonsite: box with Ubuntu, USB hub and modems\nremote: VPS with proxy ports (optional)\n2. Online services are used:\nhttp://ip.tanatos.org/ip.php which is simple PHP script that returns visitor’s IP. It is used to detect whether a modem is really online. Can be replaced with one of https://ifconfig.co or similar, but I was not happy with their reliabiality, they are down sometimes. The URL is defined in WebApp→Global_settings\nhttp://witch.valdikss.org.ru/ : used for detecting p0f and MTU\n3. CLI API\n1. show status\n\nShow full status of all modems, table (slower).\n\n# proxysmart.sh  show_status \n\nOutput:\n\nShow brief status of all modems, table, (faster)\n\nRun\n\n# proxysmart.sh  show_status_brief\n\nOutput:\n\nShow full status of all modems , json\n\n# proxysmart.sh  show_status_json \n\nOutput:\n\nShow status for a single modem, JSON\n\nArguements - NICK or IMEI.\n\n# proxysmart.sh  show_single_status_json dongle111 \n\nOutput:\n\n2. full reconfiguration\n\nRun\n\n# proxysmart.sh reset_complete  \n\nOutput:\n\n3. apply setting for a modem by IMEI\n\nJSON output\n\n# proxysmart.sh   apply_settings_for_a_modem_by_imei  868723029999406 \n\nOutput:\n\nPlain text output:\n\n proxysmart.sh  apply_settings_for_a_modem_by_imei_raw    359999999999999 \n\noutput:\n\n4. reset IP on a modem\n\nArgs: IMEI or NICKNAME.\n\nJSON output:\n\n# proxysmart.sh   reset_modem_by_imei    899999999999999 \n# proxysmart.sh   reset_modem_by_imei    Dongle222\n\nOutput:\n\nPlain text output:\n\n# proxysmart.sh  reset_quick_nick  899999999999999\n# proxysmart.sh  reset_quick_nick  Dongle222\n\nOutput:\n\n5. reboot a modem\n\nArgs: Nickname or IMEI.\n\nTEXT Output\n\nJSON Output\n\n6.1. Reset a modem via USB\n\nCan accept DEV name, IMEI or Nickname. So\n\nFor Text output:\n\nFor Json output.\n\n6. Run speedtest\n\nOn a single modem:\n\nArgs: NICKNAME or IMEI.\n\n# proxysmart.sh  speedtest 353990074160000\n# proxysmart.sh  speedtest sierra\n\nResponse:\n\n7. report bandwitdh\n\nOn a single port\n\nWith arbitrary time interval.\n\n8. reset bandwidth counter on a port\n\nARGS: portID\n\nJSON output\n\n9. list sms on a modem\n\nJSON output\n\n10. send sms\n\nPlain output:\n\nJSON output:\n\n11. purge SMS\n\nPurges SMS from all folders.\n\nCall by IMEI or nickname,\n\njson output:\n\n12. send ussd\n\nPlain output\n\nJSON output:\n\n13. get bandwidth counters from a modem\n\n..use bandwidth stats..\n\n14. Get IP rotations log for a modem\n\nBy Nickname or IMEI\n\n15. Get Top hosts from a modem\n\nBy Nickname or IMEI\n\n16. Report IP uniqueness\n\nJSON output.\n\nTEXT output.\n\n4. WEB API\n1. Web API description.\n\nWEB API endpoint is the URL that Proxysmart WebApp available at.\n\nIt can be\n\nLAN_IP:8080 when you call it from the same LAN\nVPS_IP:8080 when you forwardded ports to the Cloud VPS\nSTATIC_IP:8080 when you forwarded ports via your LAN router and your ISP gave you STATIC_IP\n\nAlso attach proper username:password (the -u parameter).\n\nWhenever below you are seeing localhost:8080, replace it with the actual WEB API endpoint.\n\n2. List all modems ( full status, slow)\n\nRequest:\n\ncurl 'http://localhost:8080/apix/show_status_json' -u proxy:proxy \n\nResponse:\n\n3. List all modems ( brief status, fast )\n\nRequest:\n\ncurl localhost:8080/apix/show_status_brief_json -u proxy:proxy\n\nResponse:\n\n3.1. List all active ports\n\nRequest:\n\ncurl http://localhost:8080/apix/list_ports_json -u proxy:proxy\n\nResponse:\n\n4. Single modem status\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/show_single_status_json?arg=dongle111    -u proxy:proxy\ncurl http://localhost:8080/apix/show_single_status_json?arg=899999999999999    -u proxy:proxy\n\nResponse:\n\n5. Reset (change) IP on a modem.\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reset_modem_by_imei?IMEI=899999999999999 -u proxy:proxy\ncurl http://localhost:8080/apix/reset_modem_by_nick?NICK=dongle22 -u proxy:proxy\n\nResponse:\n\n6. Reboot a modem\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reboot_modem_by_imei -d IMEI=860493043888886 -u proxy:proxy\ncurl http://localhost:8080/apix/reboot_modem_by_nick -d NICK=dongle2 -u proxy:proxy\n\nResponse:\n\nETA: ~ 1.5 minute\n\n7. Send SMS\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-sms' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' \\\n    --data-urlencode 'phone=+11111111111' \\\n    --data-urlencode \"sms=txt txt fff\"\n\nResponse:\n\n8. Send USSD and read response\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-ussd' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' --data-urlencode 'ussd=*100#'\n\nResponse:\n\n9. Read SMS from a modem\n\nRequest:\n\ncurl 'http://localhost:8080/modem/sms/862329888888888?json=1' -u proxy:proxy\n\nResponse:\n\n10. Read bandwidth stats from a port\n\nArgs: porID\n\nRequest:\n\ncurl localhost:8080/apix/bandwidth_report_json?arg=portJFJHFHJ -u proxy:proxy\n\nResponse:\n\nWith arbitrary time interval:\n\nARGS: portID, start time, end time.\n\nRequest:\n\ncurl -G http://localhost:8080/apix/get_counters_port -X GET -d PORTID=portKFJKJKDD --data-urlencode 'START=2023-01-28 18:10' --data-urlencode 'END=2023-01-28 19:20:01' -u proxy:proxy \n\nResponse:\n\n11. del\n\ndel\n\n12. Reset bandwidth stats for a port\n\nRequest (by portID ):\n\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=portJKJKDHJ83  -u proxy:proxy\n\nResponse:\n\n{\"result\":\"success\",\"debug\":null}\n13. Reset a modem via USB\n\nRequest either - by network interface e.g. modem77 - by Nickname - by IMEI\n\ncurl localhost:8080/apix/usb_reset_modem_json?arg=modem77      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=dongle22      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n14. Get IP rotations log for a modem\n\nRequest - by Nickname - by IMEI\n\ncurl localhost:8080/apix/get_rotation_log?arg=899999999999999  -u proxy:proxy \ncurl localhost:8080/apix/get_rotation_log?arg=dongle2          -u proxy:proxy \n\nResponse:\n\n15. Apply settings for a modem\n\nRequest:\n\ncurl http://localhost:8080/modem/settings -d imei=862329099999999 -u proxy:proxy\n\nResponse:\n\n15.1. Apply settings for a port\n\nArgs: portID\n\nRequest:\n\ncurl http://localhost:8080/apix/apply_port?arg=port029348 -u proxy:proxy\n\nResponse:\n\n15.2. Purge a port\nit deletes the port object from the DB\nit stops its proxies\n\nArgs: portID\n\nRequest:\n\ncurl http://localhost:8080/apix/purge_port?arg=port029348 -u proxy:proxy\n\nResponse:\n\n16. Purge SMS from a modem\n\nRequest either - by Nickname - by IMEI\n\ncurl localhost:8080/apix/purge_sms_json?arg=Nick77      -u proxy:proxy\ncurl localhost:8080/apix/purge_sms_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n{ \"result\": \"success\", \"msg\": \"\" }\n17. Get Top hosts from a modem\n\nRequest:\n\n18. Report IP uniquness\n\nRequest:\n\n19. Store a modem object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a modem”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n20. Store a port object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a port”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n21. Export backup\n\nDestination format: v2\n\nSo it can be later imported in V2 version of Proxysmart.\n\nRequest:\n\n5. Mongodb integration\n5.1. Schema\n\nMongodb contains 2 collections: modems and ports.\n\n5.1.1. Modems\n\nIt contains real modems.\n\nArray of elements, 1 element = 1 modem.\n\n1st element contains only mandatory keys\n2nd element contains all possible keys\n\nExample\n\nNotes:\n\nTARGET_MODE - the mode (3g/4g/auto/default) the mode will work in.\n\n5.1.2. Ports\n\nIt contains proxy ports given to the users. Each port is connected to a modem by the IMEI key. So you can attach 1 or more ports to a modem.\n\nArray of elements, 1 element = 1 port.\n\n1st element contains only mandatory keys\n2nd element contains all possible keys\n\nExample\n\nNotes:\n\nbw_quota : bandwidth quota in MB\nQUOTA_TYPE can be daily/monthly/lifetime. Latter means you allocate the quota forever till it expires.\nIP_MODE: can be :\n4 : ipv4 only\n6 : ipv6 only\n46 : prefer ipv4 but also allow ipv6\n64 : prefer ipv6 but also allow ipv4\nnull : leave default\nPROXY_VALID_BEFORE: expiry of a port\nbandlimin: download speed (megabits per second)\nbandlimout: upload speed (megabits per second)\nOS - spoofed destination OS, can be\n(empty or absent field) No spoofing\n“android:1” Android, p0f compliant but slow\n“android:3” real Android, almost like Linux\n“macosx:3” macosx:3\n“macosx:4” real MacOSX 12.6 / iPhone 13 Pro Max\n“ios:1” ios:1, p0f compliant\n“ios:2” ios:2, real Iphone\n“windows:1” real Windows 10\n5.2 Workflow\n\nquick start:\n\nPopulate Modems collection with modems\nPopulate Ports collection with ports\nfor each added modem call 'Apply settings for a modem' WEB API call. It will configure each modem and its ports\n\nif you edited a modem\n\ncall 'Apply settings for a modem' WEB API call for the modem.\n\nif you edited a port\n\ncall 'Apply settings for a port' WEB API call for the port (faster)\n\nor\n\ncall 'Apply settings for a modem' WEB API call for the modem. (slower, affects all modem's ports)\n\nif you deleted a port\n\ncall 'Purge port' WEB API call for the port (faster)\n\nor\n\ncall 'Apply settings for a modem' WEB API call for the modem. (slower, affects all modem's ports))\n\n5.3 Configuration\n\nMongoDB URI is defined in /etc/proxysmart/conf.txt :\n\nMONGODB_URI=\"********************************************************************************************\"\n\nIf you want to use other Mongodb collection names instead of modems and ports , define them in /etc/proxysmart/conf.txt :\n\nMONGODB_MODEMS_COLLECTION=modemsNEW\nMONGODB_PORTS_COLLECTION=portsNEW\n\nafter changes:\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n5.4 Moving Mongodb to other server\n\nSometimes you want to move Mongodb to a cloud server.\n\nIn order to do so\n\nkeep collection name modems\nif your new mongodb is Mongodb 5+ and doesn’t have backward compatibility with the older clients, upgrade Mongodb Client to 5th version. Run on the Proxysmart box:\nsudo -i\napt purge mongo\\* -y\n. /etc/os-release \nrm -f /etc/apt/sources.list.d/mongodb*\ncurl -L https://www.mongodb.org/static/pgp/server-5.0.asc | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/mongodb-5.0.gpg\necho \"deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu $VERSION_CODENAME/mongodb-org/5.0 multiverse\" | tee /etc/apt/sources.list.d/mongodb-org-5.0.list \napt-get update\napt install mongodb-mongosh mongodb-database-tools -y\nln -sf /usr/bin/mongosh /usr/local/bin/mongo\nupdate MONGODB_URI to new Mongodb URI in /etc/proxysmart/conf.txt\nif your new mongodb URI has +srv extension , install a PIP module: /var/www/proxysmart/venv/bin/pip install \"pymongo[srv]\"\ntest new Mongodb URI (I assume you updated MONGODB_URI variable in conf.txt above):\n    . /etc/proxysmart/conf.txt;\n    mongoexport --quiet --uri=\"$MONGODB_URI\" -c modems --forceTableScan\n\nit should return array of all elements in the modems collection\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n6. Installation\n1. Initial installation\n\nInstall a fresh OS.\n\nSupported OS and architectures:\n\nUbuntu 22.04, 20.04 on amd64, arm64.\nDebian 11 or Raspberry PI OS (ex-Raspbian) on amd64, arm64, armhf ( see Raspberry PI OS Notes below).\nRaspberry PI : https://ubuntu.com/download/raspberry-pi , choose Ubuntu Server 22.04 64bit\nNormal PC/laptop: Choose Server or Desktop, https://ubuntu.com/download, choose Ubuntu 22.04\n\nArmhf (arm 32 bit) doesn’t have Mongodb support!\n\nThose steps will take 5..10 minutes.\n\nUnplug any 4g modems.\n\nAdd an APT repo.\n\nsudo -i\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update\napt install proxysmart\n\nThen follow instructions: It will tell what to do next ( run these ).\n\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\nAfter that either enjoy the Demo version at http://localhost:8080 or check License section.\n\nRockpi Notes\n\nIf LOGRAM is enabled ( a folder /var/log.hdd exists). Disable logging:\n\nmongodb, edit /etc/mongodb.conf, comment logpath directive.\n\nRaspberry PI OS (ex-Raspbian) Notes\n\nits kernel doesn't have xt_cgroup module , so you have to rebuild its kernel and include this module. It is recommended to switch to Ubuntu instead.\n\nDevelopment version installation\n\nWhy? To unlock new features that are not yet in the Main version.\n\nsudo -i\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2.dev $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update \napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\n2. Upgrade\n2.1. Upgrade from older V2\n\nI.e. minor upgrade.\n\nRun these commands:\n\nNOTE when dpkg will ask whether to replace old config file with new one, answer N (No) or just press Enter.\n\nSo old config file is saved.\n\nsudo -i\napt update \napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\n2.2 Upgrade from V1\n\nI.e. major upgrade V1>V2.\n\nIn V1, go to WebApp → “Edit modems” and and download Backup file (Export backup for V2).\nThen run\nsudo -i\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update\napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\nOpen the webapp, import the file you downloaded\nReboot or run proxysmart.sh reset_complete.\nin the webapp→Global settings, revisit all settings and set them per your needs. It is replacement for older conf.txt.\n3. Post Installation\n\nPlug in all 4g modems you have, wait ~20 sec to let them initialize.\n\nNow test if ip li shows you any modem* interfaces, otherwise reboot to apply UDEV rules.\n\nIf it does, continue next below. (Otherwise reboot to apply UDEV rules.)\n\nNow you can start all the modems:\n\nYou have to run proxysmart.sh reset_complete or reboot the multi-modem server.\n\nCommand proxysmart.sh show_status will return a table with proxy port, external IP’s.\n\nNavigate to the WebApp ( http://localhost:8080 proxy/proxy) and assign login/password/nicknames/ports to the modems.\n\nTest reboot, reboot the box, wait 1 minute, make sure the WebApp shows the modems.\n\nWebApp\n\nVisit http://your_box_lan_IP_address:8080/ or http://localhost:8080/\n\nDefault user:password pair is proxy:proxy\n\n4. Cloud VPS integration.\n\nWhy? The VPS is needed to forward proxy ports from a cloud VPS IP back to the Proxysmart multi modem server, so proxy ports are available for all users around the world.\n\nDo I need a VPS?\n\nA VPS is NOT needed when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nThe VPS server can be a cheap 1GB DigitalOcean / Linode / Vultr VPS or similar.\n\nIt has to be located as close as possible to the 4g farm server ( for lowest ping ).\n\nVPS setup steps.\nOn Proxysmart multi modem server\n\nGo to the WebApp , copy content of the SSH public key from the bottom of the page. We will refer to it as PUBKEY below.\n\nAlso it is stored on disk as /root/.ssh/fwd.pub\n\nOn VPS\n\nCheck if your VPS has no firewall. Disable it if it has – Both inside Linux OS and in hoster panel.\n\nInstall & run Ansible.\n\napt update && apt install git ansible -y\ncd ~/\ngit clone https://github.com/ezbik/proxysmart-vps.git\ncd proxysmart-vps\n\nedit the file vars.txt\n\nnano vars.txt\n\nInsert the PUBKEY inside square brackets for the ssh_pub_keys list. Save the file (press Control O) and exit the editor (Control x)\n\nRun Ansible:\n\nansible-playbook proxysmart-vps.yml\n\ndone.\n\nOn Proxysmart multi modem server\n\nin WebApp→Global_Settings:\n\nset VPS variable to VPS IP\nset PROXY_PORTS_FORWARDER_ENABLE=1\nPick a port for SSH_REMOTE_PORT, in most cases 6902 is fine. The port (TCP) has to be free on the VPS\nPick a port for WEB_REMOTE_PORT, in most cases 8080 is fine. The port (TCP) has to be free on the VPS\n\nRun proxysmart.sh reset_complete\n\nOn VPS\n\nissue the command ss -tnlp and you will see proxy ports are bound with sshd daemon. That means the ports are forwarded.\n\nOn your private desktop or any other PC\nvisit http://vps_ip:8080 for the WebApp , default login:password is proxy:proxy\nyou can ssh to VPS IP and port 6902, and that goes to the multi-modem-server:22.\nCloud VPS IP change\n\nIf CLoud VPS IP is changed, update it on multi-modem-server side by defining new VPS variable in WebApp→Global_settings and rerun proxysmart.sh reset_complete there (or reboot).\n\n5. Forwarding ports through your own LAN router.\n\nWhy? It is needed to forward proxy ports from a your ISP IP address back to the Proxysmart multi modem server, so proxy ports are available for all users around the world.\n\nIt is suitable when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nSteps\n\nConsult with documentation of your LAN router. Forward these ports from ISP IP address to the LAN IP of proxysmart server:\n\nTCP 8001-8999 for HTTP proxies\nTCP 5001-5999 for SOCKS5 pproxies\nTCP 8080 for the WebApp\nTCP 1194 for Openvpn (if it is working in TCP mode)\nUDP 1194 for Openvpn (if it is working in UDP mode)\n\nNotes\n\nAlso edit settings WebApp→GlobalSettings, replace myrouter.com with your actual Hostname or IP addresss.\n\nSo proxy credentials & links will be shown with your actual Hostname or IP addresss.\n\nPROXY_PORTS_FORWARDER_ENABLE : Off\nREWRITE_WEBAPP_URL : On\nREWRITE_WEBAPP_TO : http://myrouter.com:8080\nREWRITE_HOST_IN_PROXY_CREDS : On\nREWRITE_HOST_IN_PROXY_CREDS_TO : myrouter.com\n\nclick SAVE.\n\n.. so forwarding system ports to a VPS is disabled.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete .\n\n7. License\n1. Demo license\n\nInstallation is shipped with default demo license.\n\nIt allows you to run proxy on 1 modem.\n\nIn order to run more modems, buy a License.\n\n2. Requesting a License\n2.1. Get the machine data\n\nMethod1. From the WebApp:\n\nOpen the proxysmart WebApp at http://localhost:8080 or http://LAN_IP:8080\nScroll down to the Machine Data text.\nCopy MachineData value to the Clipboard.\n\nMethod2. From the CLI:\n\nOpen terminal\nRun sudo proxysmart.sh license_status\nCopy machine_data value\n2.2. Contact Sales Team\n\nSend the copied value to proxysmart.org\n\n2. License installation\n\nYou will be given the license and license signature. Both are sequences of numbers and characters. Then submit both either via WebApp or CLI:\n\nsubmitting via WebApp\n\nOpen the WebApp , http://localhost:8080 , expand License section and type in the keys & submit both.\n\nsubmitting via CLI\n\nrun commands\n\nproxysmart.sh submit_license LICENSE\nproxysmart.sh submit_license_signature LICENSE_SIGNATURE\n3. Restoring Demo license.\n\nIf your paid license expired or broken, restore DEMO license, run:\n\nsudo cp -v /usr/share/doc/proxysmart/examples/license.txt* /etc/proxysmart/\n\n8. Mobile (4G/5G) VPN\n\nTogether with building proxies, it is possible to build Residential VPN.\n\nAssumption is, your proxies are already available via Cloud VPS.\n\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n\nIf ports forwarded through a VPS\n\nSteps on VPS\n\nAssume the VPS is already “integrated” - see VPS integration topic.\n\nPick a free TCP port on the VPS, run ss -tnlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501. We will call it OPENVPN_REMOTE_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1501 , to the free TCP port on Cloud VPS.\nset OPENVPN_INTEGRATION=1 so that Proxysmart will understand Openvpn is in use.\nset OPENVPN_LOCAL_PORT=1194\nClick SAVE\n\nSo VPN client certificates will be generated with these values and VPN clients will connect there ( *******:1501 )\n\nGo to the WebApp main screen and download OpenVPN profiles for each port.\n\n8.1.2. Installation with TCP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded TCP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_SERVER_HOST to $EXT_IP\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/TCP )\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.3. Installation with UDP protocol (through VPS)\n\nSteps on VPS\n\nMake sure you finished the Cloud VPS setup part, with Ansible, so VPS part is done.\n\nSteps on Proxysmart server\n\nPick a free UDP port on the VPS, run ss -unlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501.\n\nWebApp→Global_Settings\nset OPENVPN_SERVER_PORT=1501 , to the free UDP port on Cloud VPS.\nset OPENVPN_INTEGRATION=1 so that Proxysmart will understand Openvpn is in use.\nset VPS_SOCKS5_SERVER to scheme with authentication on VPS e.g. socks5://px:g739Az8JYK@*******:2323\nClick SAVE\n\nSo VPN client certificates will be generated with these values and VPN clients will connect there ( *******:1501 )\n\nGo to the WebApp main screen and download OpenVPN profiles for each port.\n\n8.1.4. Installation with UDP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded UDP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_SERVER_HOST to $EXT_IP\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/UDP )\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.2. Many users with the same profile\n\nBy default only 1 device (PC, mobile, tablet) can use 1 OpenVPN profile. If you want multiple devices use 1 profile, edit /etc/openvpn/server.conf , comment out ;duplicate-cn line by removing the ; character, and run proxysmart.sh reset_complete.\n\n8.3. Mobile VPN, how to connect\n\nSo download the VPN profiles and connect using any VPN client software.\n\nDownload and install software:\n\nWindows: https://openvpn.net/community-downloads/ or https://openvpn.net/client-connect-vpn-for-windows/\n\nMacOS: https://tunnelblick.net/\n\nAndroid: https://play.google.com/store/apps/details?id=de.blinkt.openvpn or https://f-droid.org/en/packages/de.blinkt.openvpn/\n\nIOS: https://apps.apple.com/us/app/openvpn-connect/id590379981\n\nImport downloaded OpenVPN profile, tap Connect.\nuse Login and Password from the corresponding proxy.\n8.4. Mobile VPN, FAQ\n8.4.1. Switch Openvpn protocol\n\nIn WebApp→GlobalSettings set OPENVPN_PROTOCOL to tcp or udp and run proxysmart.sh reset_complete\n\nOn Clients, either download profiles again, or change protocol in client settings.\n\n8.5. Mobile VPN logs\n\nLogs of openvpn sessions - /var/log/openvpn/sessions.log. Format:\n\n'$time','$type','$local_port','$proto','$duration','$bytes_in','$bytes_out','$Real_IP','$Real_PORT','$Ovpn_CERT','$Ovpn_IP','$IMEI','$proxy_login','$auth_reject_why'\ntype - session_start / session_stop / auth_reject\nlocal_port - local port of Openvpn server\nproto - tcp-server or udp\nduration - when type is session_stop, how many the session lasted\nReal_IP, Real_PORT - of a client\nauth_reject_why - when type is session_stop, the reason why auth was rejected\n9. Bugs and Limitations\nLTE modules\nIPV6 is not supported\nAndroid phones\nNo P0f-spoofing with Proxysmart APK app.\nVPN users\nIPV6 is not supported\nPort ranges\nProxy ports range: HTTP 8001..8999, SOCKS5 5001..5999\nOS TCP Fingerprint spoofing\nNot supported on Ipv6\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Set new password ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?do=resendpwd", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nSet new password\n\nPlease enter a new password for your account in this wiki.\n\nSet new password\n\n\nUsername \n\n\nSet new password\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:changelog ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\n\nThis page is read only. You can view the source, but not change it. Ask your administrator if you think this is wrong.\n\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:single_20status_20for_201_20modem_20make_20imei_20clickable_20run_20_60show_single_status_json_60_20and_20show_20a_20table_20in_20the_20new_20window\nThis topic does not exist yet\n\nYou've followed a link to a topic that doesn't exist yet. If permissions allow, you may create it by clicking on Create this page.\n\nPage Tools\n    "}, {"title": "machine_data.png (1045×470)", "url": "https://proxysmart.org/wiki/_media/machine_data.png", "html": ""}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\n\nThis page is read only. You can view the source, but not change it. Ask your administrator if you think this is wrong.\n\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nchangelog\nreadme\nv2\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:changelog.v2dev ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\n\nThis page is read only. You can view the source, but not change it. Ask your administrator if you think this is wrong.\n\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\n\nThis page is read only. You can view the source, but not change it. Ask your administrator if you think this is wrong.\n\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:changelog ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?do=edit", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\n\nThis page is read only. You can view the source, but not change it. Ask your administrator if you think this is wrong.\n\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "Sitemap ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?do=index", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nSitemap\n\nThis is a sitemap over all available pages ordered by namespaces.\n\nv1\nv2\nandroid\nchangelog\nchangelog.v2dev\nreadme\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:android ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:android", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:android\nTable of Contents\nAll Methods: brief info\nGeneral Info\nMethod 1.1 : the App + cloud VPS\nMethod 1.2 : the App + Proxysmart server in the LAN (“WIFI-Split”)\nMethod 2.1 : USB tethering + Termux\nMethod 2.2 : Termux + VPS\nMethod 3 : USB tethering + ADB\nAll Methods: brief info\nMethod 1.1 : the App + cloud VPS\nThe App is installed on phones, it connects to a VPS.\nProxysmart software is installed on the VPS.\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nMethod 1.2 : the App + Proxysmart server in the LAN (“WIFI-Split”)\nThe App is installed on phones, it connects to a Proxysmart server installed in the LAN by its local WIFI IP\nProxysmart software is installed on the local server in the LAN\nProxy ports forwarded either through a VPS, or through LAN router\nPRO: no USB hubs, just phones wall chargers\nPRO: Home WAN can be used for forwarding the proxy ports\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nMethod 2.1 : USB tethering + Termux\nAn App (Termux) is installed on phones.\nThey are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: phone can be controlled even if its MobileData goes off\nPRO: all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nMethod 2.2 : Termux + VPS\nAn App (Termux) is installed on phones.\nThe phones are connected to the Linux VPS running in the cloud\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nMethod 3 : USB tethering + ADB\nPhones enable ADB and are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: no extra Apps are installed on the phone, very simple to setup\nPRO: a phone can be controlled even if its MobileData goes off\nCON: not all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nGeneral Info\nAvoid Android 12. It has very agressive mechanism that kills Apps running in background. More info: https://github.com/termux/termux-app/issues/2366\nNo Android Root is needed. But it's nice to have it.\nMethod 1.1 : the App + cloud VPS\nBrief info of the method:\nThe App is installed on phones, it connects to a VPS.\nProxysmart software is installed on the VPS.\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nPrepare the Proxysmart server:\nin the WebApp → Settings → set APROXY_ENABLE = On\nrun proxysmart.sh reset_complete or click Reset Complete in the WebApp or reboot the Proxysmart server\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nDisconnect USB data cable from the PC\nProxysmart App installation\nDownload & Install APK from http://proxysmart.org/android/ or https://proxysmart.org/files/proxysmart-android-agent/proxysmart-android-agent.apk\nAfter the App appears on the Desktop - long tap the App icon → Info, set its Battery properties: 1. Can use Battery in background; 2. No Battery optimization\nConnect the App to the Proxysmart server:\n\nOpen the App, enter details:\n\nDevice Name: must be u101 , u102, and so on.\nServer password: px903903 (or whatever is set in the Proxysmart server WebApp as APROXY_API_PASSWORD )\nIP address: IP or hostname of the cloud VPS with Proxysmart\nTap SAVE\nTap ENABLE IP CHANGE\nTap NO ROOT\nTap CONFIGURE, tap Google or Gear icon, set “Proxysmart” as voice assistant, tap back.\nTap TRY CHANGE IP. Confirm the phone went to AirPlane mode and came back.\nTap START\nWait until it shows “Connnected” and appears in the Proxysmart WebApp\nTest IP rotation\nTap “TRY CHANGE IP” in the App\nThe phone will go Airplane mode and back\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later), the Proxysmart App will start and connect to the Proxysmart server\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 1.2 : the App + Proxysmart server in the LAN (“WIFI-Split”)\nBrief info of the method:\nThe App is installed on phones, it connects to a Proxysmart server installed in the LAN by its local WIFI IP\nProxysmart software is installed on the local server in the LAN\nProxy ports forwarded either through a VPS, or through LAN router\nPRO: no USB hubs, just phones wall chargers\nPRO: Home WAN can be used for forwarding the proxy ports\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nPrepare the Home/Office router\nPeer-to-peer connections must be allowed between LAN clients, so LAN clients can communicate over WiFi.\nPrepare the Proxysmart server:\nin the WebApp → Settings → set APROXY_ENABLE = On\nrun proxysmart.sh reset_complete or click Reset Complete in the WebApp or reboot the Proxysmart server\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nIn Android Settings → Developer Settings → enable 'keep mobile data On even if WiFi is connected'\nConnect phone to WIFI and enable MobileData.\nDisconnect USB data cable from the PC\nProxysmart App installation\nDownload & Install APK from http://proxysmart.org/android/ or https://proxysmart.org/files/proxysmart-android-agent/proxysmart-android-agent.apk\nAfter the App appears on the Desktop - long tap the App icon → Info, set its Battery properties: 1. Can use Battery in background; 2. No Battery optimization\nConnect the App to the Proxysmart server:\n\nOpen the App, enter details:\n\nDevice Name: must be u101 , u102, and so on.\nServer password: px903903 (or whatever is set in the Proxysmart server WebApp as APROXY_API_PASSWORD )\nIP address: local LAN IP of the PC with Proxysmart\nTap SAVE\nTap ENABLE IP CHANGE\nTap NO ROOT\nTap CONFIGURE, tap Google or Gear icon, set “Proxysmart” as voice assistant, tap back.\nTap TRY CHANGE IP. Confirm the phone went to AirPlane mode and came back.\nTap START\nWait until it shows “Connnected” and appears in the Proxysmart WebApp\nTest IP rotation\nTap “TRY CHANGE IP” in the App\nThe phone will go Airplane mode and back\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later), the Proxysmart App will start and connect to the Proxysmart server\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 2.1 : USB tethering + Termux\nBrief info of the method:\nAn App (Termux) is installed on phones.\nThey are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: phone can be controlled even if its MobileData goes off\nPRO: all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nPrepare the Proxysmart server:\n\nNo steps needed\n\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nConnect with USB cable to the PC with Proxysmart\nIn Developer settings: set Default USB mode: Tethering.\nTermux App installation\nInstall F-Droid https://f-droid.org/\nOpen F-Droid App, install Termux, Termux:Api, Termux:Boot.\nRun Termux:Boot, it will show up, then switch to Home screen.\nLong press Termux icon, INFO>Battery>set Unrestricted, grant access to Location, SMS, CONTACTS, Calls\nRepeat for Termux:Boot and Termux:API Apps.\nOpen Termux, wait until it's initialized & black screen with cursos appears.\nType in:\n\ncurl modus.tanatos.org/pz/pz1 > pz; bash -x pz\n\nand press ENTER. It will ask for these details, type in everything:\nimei: IMEI of the phone\n\nExpect big green DONE in the end.\n\nMacrodroid\nInstall Macrodroid from GooglePlay.\nLong tap on Macrodroid app icon ⇒ Run in background ⇒ yes\nOpen Macrodroid app, settings ⇒ Ignore battery optimizations\n\nCreate a macro\n\nTrigger: catch notification: notification recieved: any app: contains: PX_RESET\nThen press OK.\nAction: vibrate Blip, Airplane ON, wait 2s, Airplane OFF\nGrant all permissions! (catch notification, assistent, etc).\nSave macro as IP-Reset\nTest trigger. It must vibrate and do Airplane on\\off.\n\nCreate a macro\n\nTrigger: Regular interval: 15 minutes\nActions: Airplane off, Launch application: Termux\nSave as Autofix, (grant Overlay)\nTest IP rotation\n\nOpen Termux App. Type in:\n\nbash ip_reset.md\n\nAnd press ENTER. You will see how IP is changed + micro vibration.\n\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later) USB tethering will be enabled\nAfter start-up (1-2 minute later) Termux will auto-start.\nPlug in the Proxysmart server with USB data cable\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 2.2 : Termux + VPS\nBrief info of the method:\nAn App (Termux) is installed on phones.\nThe phones are connected to the Linux VPS running in the cloud\nProxy users connect to the VPS.\nPRO: no USB hubs, just phones wall chargers\nPRO: no local PC\nPRO: all phones models are supported\nCON: no way to control a phone if MobileData goes off\nCON: phones MobileData is used for forwarding proxies ports - so it works bidirectionally\nPrepare the Proxysmart server:\n\nNo steps needed\n\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nDisconnect USB data cable from the PC\nTermux App installation\nInstall F-Droid https://f-droid.org/\nOpen F-Droid App, install Termux, Termux:Api, Termux:Boot.\nRun Termux:Boot, it will show up, then switch to Home screen.\nLong press Termux icon, INFO>Battery>set Unrestricted, grant access to Location, SMS, CONTACTS, Calls\nRepeat for Termux:Boot and Termux:API Apps.\nOpen Termux, wait until it's initialized & black screen with cursos appears.\nType in:\n\ncurl modus.tanatos.org/pz/pz2 > pz; bash -x pz\n\nand press ENTER. It will ask for these details, type in everything:\n\nimei: IMEI of the phone,\nVPS: IP of the VPS in the cloud\nVPS_PORT: 22\n\nExpect big green DONE in the end.\n\nMacrodroid\nInstall Macrodroid from GooglePlay.\nLong tap on Macrodroid app icon ⇒ Run in background ⇒ yes\nOpen Macrodroid app, settings ⇒ Ignore battery optimizations\n\nCreate a macro\n\nTrigger: catch notification: notification recieved: any app: contains: PX_RESET\nThen press OK.\nAction: vibrate Blip, Airplane ON, wait 2s, Airplane OFF\nGrant all permissions! (catch notification, assistent, etc).\nSave macro as IP-Reset\nTest trigger. It must vibrate and do Airplane on\\off.\n\nCreate a macro\n\nTrigger: Regular interval: 15 minutes\nActions: Airplane off, Launch application: Termux\nSave as Autofix, (grant Overlay)\nTest IP rotation\n\nOpen Termux App. Type in:\n\nbash ip_reset.md\n\nAnd press ENTER. You will see how IP is changed + micro vibration.\n\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later) Termux will auto-start.\nWait until the phone appears in the WebApp of the Proxysmart server\nMethod 3 : USB tethering + ADB\nBrief info of the method:\nPhones enable ADB and are connected to the Linux PC with USB tethering.\nThe PC forwards proxy ports to a VPS.\nProxy users connect to the VPS.\nPRO: no extra Apps are installed on the phone, very simple to setup\nPRO: a phone can be controlled even if its MobileData goes off\nCON: not all phones models are supported\nCON: a lot of wires;\nCON: USB tethering may switch off\nCON: expensive USB hubs\nPrepare the Proxysmart server:\nin the WebApp → Settings → set ADB_PHONES = On\nrun proxysmart.sh reset_complete or click Reset Complete in the WebApp or reboot the Proxysmart server\nPrepare a phone:\nUninstall Macrodroid, Termux, Termux:Api, Termux:Boot (if they were installed)\nRemove Screen Lock\nEnable Developers settings\nSettings - Battery - select “performance” profile , disable battery savers.\nConnect the phone to the Mobile Network\nDisconnect from Wifi and forget all saved WIFI networks\nConnect with USB cable to the PC with Proxysmart\nIn Developer settings: set Default USB mode: Tethering.\nIn Developer settings: 1. Enable USB debugging. 2. Disable ADB authorization timeout. 3. USB debugging, allow security settings: set ON.\nPost Steps\nReboot the phone\nAfter start-up (1-2 minute later) USB tethering will be enabled\nPlug in the Proxysmart server with USB data cable\nWhen a popup box appears on the phone, 'Allow USB debugging from this computer?', answer YES\nWait until the phone appears in the WebApp of the Proxysmart server\nv2/android.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Log In ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme?do=login&sectok=", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nLogin\n\nYou are currently not logged in! Enter your authentication credentials below to log in. You need to have cookies enabled to log in.\n\nLog In\nUsername \n\n\nPassword \n\n Remember me Log In\n\nForgotten your password? Get a new one: Set new password\n\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:changelog ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:changelog", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:changelog\nChangelog\n\n2024-03-21\n\nSierraWireless EM7455 in PPP mode\n\n2024-01-23\n\nMTU fix (slow speed fix) on USA Verizone on LTE chips\n\n2024-01-22\n\nAPN per modem (LTE chips and PPP sticks)\n\n2024-01-17\n\nAlcatel IK41, improved IP rotation\n\n2024-01-08\n\nSierra Wireless EM7455: force set APN even before checking LTE status\n\n2023-12-25\n\nWebApp: mobile signal is shown as colored bars\n\n2023-12-22\n\nsupport of Huawei 5G CPE H112-370 (ip rotation, sms read)\n\n2023-12-21\n\nadded 'modems states' : adding queued→adding→added→deleting , failed_to_add.\n\n2023-12-19\n\nreduced IP rotation time on Huawei (E3372, E8372, E55*) by approx. 4 seconds\n\n2023-12-12\n\nimproved support of Huawei E3372s modems.\nexport/import of LAN modems & other local settings during the Backup/Restore\n\n2023-12-07\n\nAndroid VPN can send UDP.\n\n2023-12-06\n\nAndroid proxies - cover reconnects.\nReboot call in the WebApp opens in new tab.\n\n2023-12-05\n\nAndroid App can read SMS.\n\n2023-12-04\n\nfix Fibcom L860 on long USB id's.\n\n2023-11-30\n\nSIMA7630C (ip rotation, SMS read, reboot)\n\n2023-11-27\n\nAndroid VPN - added.\nAndroid VPN - proper native DNS from the Mobile Carrier\n\n2023-11-25\n\nopenvpn sessions log in a separate file ( $OPENVPN_SESSIONS_LOG )\nussd on Quectel EP06\n\n2023-11-23\n\nsniffer script to log all proxies requests and original clients IP's. Can be installed either on VPS or on Proxysmart server, or both. It generates easy to read & analyze single log of who,when,what.\n\n2023-11-21\n\nsupport of LTE module - T77W595 / LT4120\n\n2023-11-12\n\nbuild for Ubuntu 24.04 Noble\n\n2023-11-08\n\nimport & export mongodb – a button in the WebApp\nexport to Proxysmart v2\n\n2023-11-07\n\nmongodb uri can be set in /etc/proxysmart/conf.d/*.inc\n\n2023-11-05\n\nParallel initialization of proxies during reset_complete & reset_gently (enablable with PARALLEL_STARTUP variable)\nfix IP rotation counter logging\nReboot button in the main WebApp for rebooting the server\nAndroid proxies - automatic ports probing and detection.\n\n2023-11-03\n\nOlax U90 - support of a new modem\nnew WEB API call for storing a modem object\nQuectel modems now obey with TARGET_MODE during IP rotation\n\n2023-11-01\n\nAlcatel HH71 autoreconnect\nLAN modems: improved detection; ability to reboot\nfix for adding modems when IMEI is not properly detected\n\n2023-10-25\n\nQuectel EC20 support; Imvproved Chinese letters in SMS\nimproved stability of IK41 (stick mode)\n\n2023-10-23\n\nindividual OS TCP spoofing (per proxy)\n\n2023-10-18\n\nimproved speed on Android proxies over 27mbps\n\n2023-10-13\n\nimproved SMS on IK41 (stick mode)\n\n2023-10-09\n\nPossibility to set Bandwitdh Quota direction, one of (in, inout, out).\n\n2023-10-04\n\nimproved Huawei E3272 support.\n\n2023-10-03\n\nTarget mode of a modem during IP rotation: can be set in the WebApp\nDeleted TARGET_MODE from conf.txt (global variable)\n\n2023-09-28\n\nfixed installation in India where github is blocked\nandroid: VPN integration - done\n\n2023-09-26\n\nAdd Huawei 5g CPE H122-373\n\n2023-09-25\n\nimproved ME-906s modems IP rotation\nPrevent getting non-unique IP’s after rotation\n\n2023-09-13\n\nallow more than 1 device to use 1 VPN profile\nVodafone K5161h supported\n\n2023-09-10\n\nWebApp: show total online modems count\nWebApp: show VPN profile link in the main screen of the webapp\nFibocom L860 - custom APN fix & obeying custom Net Mode (TARGET_MODE)\ndeal with dongles Nicknames conflicts\ndeal with HTTP and SOCKS port conflicts\n\n2023-09-09\n\nAndroid proxies - improved Ping graphs; improved Carrier detection\n\n2023-08-30\n\nadded auto reconnect for DW5811e\n\n2023-08-28\n\nimproved QUIC support: add switch 3proxy/gost for Socks software in LAN ; add switch gost/gost3 as Socks software\n\n2023-08-25\n\nqmicli , added timeouts\n\n2023-08-23\n\nzteMF79NV support\n\n2023-08-21\n\nAlcatel modems (MW40,MW42,HH71) obeys $DEFAULT_HILINK_ADMIN_PASSWORD\nAlcatel MW45 improved support.\n\n2023-08-15\n\nadd Reset Counters button to the Bandwidth report page.\n\n2023-07-17\n\nimprove SMS sending on ZTE MF667, MF927, MF688V3E, MF823, MF833, MF93\n\n2023-07-13\n\nIP uniqueness report\n\n2023-07-10\n\nTop hosts report (WEB API method + WebApp table).\nAuto USB reset when modem’s WEB APP is stuck ( AUTO_USB_RESET_DONGLES=1 )\n\n2023-07-06\n\nAdd proxies built on Proxysmart android App\n\n2023-06-04\n\nSIM7600G proper SMS read\n\n2023-05-26\n\nMain proxy user:added Threads limit\nExtra users: added Threads limit & Dual_Auth IP\n\n2023-05-11\n\nadd SIM7600G (Lte cat4)\n\n2023-05-04\n\nadd Foxconn DW5821e (LTE cat 16)\n\n2023-05-02\n\nusb reset button in the WebApp\n\n2023-05-01\n\ncached list of the modems in the main screen of the WebApp\n\n2023-04-13\n\nsupport of pure old Stick(PPP) modems like Huawei E173, E156; ZTE MF110, MF193\nadd E3276 and E303 in Stick(NCM) mode\n\n2023-04-05\n\nability to hide old and new IP’s from the output of IP rotation links.\nauto reboot modems under some circumstances (check conf.txt for configuration)\n\n2023-03-29\n\nadded RequestsPerSecond graphs in modem status\nadded Pings graphs in modem status\n\n2023-03-28\n\nZyxel NR5103E support\n\n2023-03-20\n\n3proxy 0.9.x support\nability to block direct requests to IP’s (e.g. when clients resolve hostnames on their side and bypass Hostnames blocklist) and process only requests containing hostnames (Forces DNS resolution on proxy server side)\n\n2023-03-19\n\nautofix mass storage on Huawei modems\n\n2023-03-11\n\nRepeat IP rotation in case of failure (went online>offline, or the same IP received)\n\n2023-03-09\n\nfixed issue of DNS cache on modems\nBlack 4g modem – MSM8916 with /json WEB API\n\n2023-03-06\n\nability to reboot Quectel if no SIM\nNew UI\nshow graphs in modems statuses\n\n2023-03-03\n\ntimers fix (allows arbitrary nonstandard minutes like 31,61,1222)\nAlcatel IK41 in “stick” mode\n\n2023-03-02\n\nChinese MSM8916 modems support\nbug fix: JS was cached forever\nproxysmart.log - grandparent pid is logged\nproxysmart.log - timestamps preceed every line\nztemf79 : reconnect data\nNum of current connections is reported properly\nBrovi Huawei E3372-325 added support\nQuectel EC25-AFX added\nAPI for status query fixed\n\n2023-02-07\n\nMF667 modem support\n\n2023-02-06\n\nQuectel EM12G\n\n2023-02-05\n\nability to set a custom URL in secure rotation links\nability to set a custom HOST in proxy credentials ports\n\n2023-02-04\n\nQuectel EC25 can send USSD\nIK40 support + USSD\n\n2023-02-02\n\nE3276 can send USSD\nMF823 is properly shown\n\n2023-02-01\n\n3proxy logs contains IMEI and Nickname\n\n2023-01-29\n\nAPI for getting bandwidth stats within arbitrary time interval\n\n2023-01-28\n\nHuawei 3372h-325 “BROVI”\n\n2023-01-27\n\nmongodb cache bug fixed\n\n2023-01-22\n\nvpn: default UDP\n\n2023-01-19\n\nallow only DEMO version on VirtualBox\n\n2023-01-17\n\nshow anonymous link in main screen of the WebApp\n\n2023-01-15\n\nAdded new modem, Xproxy XH22 Wireless\nadded table of content in README\nProxies “Extra users”: added individual speed limits\n\n2023-01-14\n\nQuectel RM520 support (5g)\n\n2023-01-12\n\nFibocom L860, Ipv6 support\n\n2023-01-11\n\npurging sms (for Huawei in HiLink mode only)\n\n2023-01-10\n\nVodafone K5161z supported\n\n2023-01-08\n\nDocumentation: Dirty ip reset\n\n2023-01-07\n\nandroid phones integration (USB tethering mode & remote mobile tunnel mode).\n\n2023-01-04\n\nSecure IP reset links are now shown as full URL\n\n2023-01-03\n\nStatic VPN IP’s based on Index from Openvpn PKI\n\n2022-12-30\n\nWeb: editable field for Pho.Number.\nLink for downloading Openvpn profiles for modems\n\n2022-12-25\n\nQUIC support (UDP, HTTP/3.0) for SOCKS5 proxies , check README\n\n2022-12-22\n\nredefine variables in /etc/proxysmart/conf.d/*.inc\n\n2022-12-21\n\ndetect when CellOp redirects to their Billing Page\n\n2022-12-11\n\nSet minimum time between IP resets\na function for re-add a device: proxysmart.sh add_dev $DEV\nUDEV plug-n-play (hook scripts)\n\n2022-12-02\n\nHuawei dongles in NCM mode (WWAN+AT)\n\n2022-11-27\n\nFiboCom L860-gl : basic work + sms send\nHuawei ME906s : basic work + sms send\n\n2022-11-20\n\nSierraWireless EM7455\n\n2022-11-18\n\nCLR900A\nFranklinT10\n\n2022-11-12\n\nBW quota with PMACCTD, block outgoing access\nignore extra user when it matches with existing user (main user or another extra user)\nAlcatel MW40 - proper operation now\n\n2022-11-09\n\nVerizone Jetpack AC791L\nmore fast status for unknown modems models\n\n2022-11-08\n\nalcatel IK41: reboot, sms list, helper\n\n2022-11-01\n\nsolve issue when run_cache xxx times out and prints nothing, thus is executed over and over again\n\n2022-10-29\n\nDocumentation: secure rotation links\nget_ConnectionStatus_n response must contain OK\ncell op 425 02 ⇒ must be converted too\nmodel_shown returns “” sometimes ⇒ won’t fix, it happens when MAX_PARALLEL_WORKERS_STATUS is too large\n\n2022-10-27\n\nNovatel MIFI\nFranklin T9 (R717)\ncustom DNS servers for proxies\n\n2022-10-25\n\nNM disable modem*\n\n2022-10-19\n\nxproxy.io modems support\nbug fixed: Configuration file /etc/systemd/system/proxysmart.service is marked executable.\nwhen main proxy user == one of extra users, use extra user password\n\n2022-10-16\n\nvpn: blocklist of domains. make sniproxy enablable in conf.txt\nlicense revoking status checked online\n\n2022-10-15\n\nrework of denied domains list, *.domains are added automatically\n\n2022-10-12\n\nUF906 (KuWfi, Anydata, TianJie) modems integration\n\n2022-10-10\n\ndirty ip rotation support\nsecure ip rotation links with auto expiration\n\n2022-10-06\n\nOS spoofing for VPN users\nvpn: mongodb integration\n\n2022-10-04\n\nzte mf688T proxidize can send SMS\nd-link dwm222 basic support (beta)\nsignal reported in main table of the WebApp\n\n2022-09-30\n\nadded OS TCP spoofing with p0f3 signatures (WOW!!!), including Mac OS X, iOS, Android, Windows. Total rework of OSfooler-ng!\nmodem WEB server warm-up (when 1st request is ignored and 2nd and subsequent are processed)\n\n2022-09-22\n\nmodems helpers reorg, make them more fast & cached\nadded hourly IP rotation\n\n2022-09-19\n\nget by mongodb - get 1st value\n\n2022-09-16\n\nzte mf79VIVO support\nextra delay after IP rotation\n\n2022-09-12\n\nreport APN\nzte mf79 - sms send\n\n2022-09-08\n\nimproved support of ZTE MF927\n\n2022-09-03\n\nreport LTE band in full status\n\n2022-09-02\n\nsupport of 4g LAN routers like Huawei Bxxx\n\n2022-08-27\n\nreport IP rotation history as table\n\n2022-08-24\n\nreport IP rotation history\nWebApp can edit modems\nminor fixes: [ ignoring cell fwding in altnetworking2, dns in vpn, etc ]\nshow_model - more correct modems model showing\n\n2022-08-09\n\nopenvpn support (residential VPN!)\n\n2022-08-04\n\nipv6 bug fixed (ipv6 not assigned)\nchange mongodb uri in conf.txt\n\n2022-08-02\n\nnagios plugin exit code fixed ; nagios plugin moved to the main codebase\n\n2022-07-30\n\nfix: del symlink of altnetworking on installation\n\n2022-07-28\n\nhaproxy check >= 2.2\nDocumentation: – periodic IP rotation – ipv6 support – VPS integration\n\n2022-07-22\n\napply_settings = > if absent in DB, assign random creds.\n\n2022-07-20\n\nfixed bug when license stopped working because of floating (??) disk size and RAM size.\n\n2022-07-01\n\nconvert numeric cellop MCCMNC to Letters\ndel old show_status\n\n2022-06-23\n\nipv6, haproxy integration, systemd-slices (altnetworking2).\n\n2022-06-22\n\nmongodb: timeout handling\n\n2022-06-18\n\nmodem_names OR static udev map ⇒ make configurable\nis syslog-ng needed for Haproxy ?? revert back to rsyslog.\n\n2022-06-11\n\ndouble get_external_ip when doing IP reset via API\n\n2022-06-07\n\nPeriodic automatic IP rotation , set AUTO_IP_ROTATION per modem or globally\n\n2022-05-30\n\nadd usb_reset ( usb_reset_individual_DEV ) API, by nick\n\n2022-05-22\n\nadd proxy live counters based on RRD\n\n2022-05-07\n\nwait UDEV_xx after usb reset  reboot\nadd reboot API (CLI|WEB)\n\n2022-05-03\n\nreboot doesn’t wait till modem is added to the system; use reset_gently for that\n\n2022-05-01\n\nbug fixed when a modem has the same LAN as EthLAN, so the modem took priority over LAN\n\n2022-04-26\n\nzte MF971\nadd Quectel modems support\nreport ICCID\n\n2022-04-23\n\nbw_quota bug - quota restrictions are applied on next proxy request only\nread bw_quota, bandlimin, bandlimout, DENIED_SITES_ENABLE, DENIED_SITES_LIST, mtu, extra_users : from Mongodb\n\n2022-04-18\n\ncx /usr/lib/nagios/plugins/proxysmart-nagios-helper.sh\nDNS in Jinja2\n\n2022-04-08\n\nFIX: error 500 when modem not found\nlicense in web\nImei not unique ⇒ show in dash why\nIgnore reset if IMEI is in IGNORED_IMEIS\nDEV not added ⇒ why? show in status\nshow_status_brief, header must be 1st row\nNagios plugin doesn’t work with encrypted source\nZTE MF93: reboot call\n\n2022-03-31\n\nhuawei K5150: reset_ip , list_sms, reboot, send_sms\n\n2022-03-29\n\nmore UWSGI workers\ndemo license\ndeb package building\nmongodb template\n\n2022-03-28\n\nzte mf79: sms list, reboot, IP reset\n\n2022-03-22\n\nindividual blocklists( DENIED_SITES_ENABLE, DENIED_SITES_LIST )\n\n2022-03-21 (PRE)\n\nSms read\nDefault Modem admin pw in config.\nForwarder type in config ( WAN / CELL )\nusb reset yin config ( kris )\nworkmode: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.74 hipi.pl admin123 | grep -i workmode | cut -d“’” -f4\ncell_operator name: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.59 hlcli NetworkInfo | grep ‘ShortName’ | cut -d“ -f4\nadd_indiv.. and reset_gently\nN by modemN\nRandom port and nickname assignment if absent in the db\nlocks\nmake reset_gently WAIT till reset_complete is done\nJSON: report_bandwidth\nJSON function: apply settings for a modem\nstop redirector BEFORE ip rotation to prevent stalled SSH connection on remote SSH server\nleave checking global lock but never acquire it.\nJSON for list modems\nTABLEd status\nshow_status: model & cell_operator & workmode:\nreset_complete: del all Cgroups & ip rules\nJSON function: reset IP\nHipi scripts accept GW & AdminPW args\nmake screenshort w. CutyCapt\nVnstat autorestart\nflush ipv6 on modems\nmake show_status default\norder: curl, hipi w. pass, hipi w/o pass, hlcli, ETC\nOptional ttl fix: Y\nsms sort\nre-connect DATA on modems, if ext_ip empty ⇒ <NAME_EMAIL>\nre-connect DATA on modems, at reset_complete ⇒ <NAME_EMAIL>\nmtu. Report via Valdik\nmtu. Set\nACL : optional: allow admin only to browse HiLink webUI\nstart with no modems reset_complete doesn’t touch ⇒ adding a modem ⇒ reset_gently doesn’t work\ndel ipv6 leak from modems\nACL : optional: deny some sites\nProper logging , with ssh connection, stderr, as one SHOT\nno LAN test\ndefault login/pw for autogenerated proxies\nreport rotation time\nsafe way to read vars from included files\ndeploy script\nN from modemN May not work when iflink is not like modemXXX\ntrim CELLOP\nmanual: tell what http and socks5 ports to use\nmanual: how to use proxy, from internet, from lan\nallow ::1 in ip6tables\ndef.gw via 1 available modem – add to reset_complete\nrun_cached , to prevent multiple hipi runs\nfix EVAL() of commented lines like ‘v=v #ddd’\nregenerate ssh keys for FWD, if absent\nget rid of sourcing files: constants\nrun cached: log hit or miss\nmanual- check if your vps has firewall. disable it. both inside and in hoster panel.\nshow_status: If CurrNet is ’’, use workmode\ncheck faked route on reset_gnelty\nshow_status_json : redirector statuses\nupdate Manual: api calls & sample output\nspeedtest , up & down\ninclude redirector status in status. enabled, failed/running, uptime\nshow_status: list all proxies std format. depends on FWD_ENABLE, list $VPS:\nreplace XARGS+source to just xargs\nlog TimeTook for /var/log/proxysmart.log\ncrypted source autoGen\nbw report change\ncheck /var/run/proxysmart/reset_complete_done ⇒ show_status_json & show_status BREAK\nignored IMEI\nignored devices\nencode with shc.\nanti-p0f\nintegrate ZTE_MF6xx api ( goform/goprocess)\ncheck whether modem WebUI is available, for get_imei(), show_status_json(), get_model()\nadded Anydata UF906, Alcatel IK41 & MW40\nis_locked to JSON status\nglobal RESET LOCK\nzte MF823 ip change\nUSSD\nSMS\nreport phone uptime\nreport USB uptime\nIgnore FRESHLY inserted modems in reset_gently. Let them init\ndon’t rely on bash RANDOM:\nnagios plugin\nzte MF93D\nindividual JSON status for a modem\nWebapp: +GW, +Local_IP, +current hostname, send sms , send ussd\nDHCP more attempts\nWeb: brief status\nbrief status\nreconnect data - additional command, separate from reset_gently\nIP-based ACL\nspeed limits for proxies\nopen proxy (y)\ncheck modem WEB ⇒ change check_tcp to curl.\nDEB=mktemp /tmp/reset_modem_by_imei.XXXXXX – purge it\ndhcp → migrate to dhcpcd from Nmap\nQUICK_STATUS=0\\1, show only few details in show_status..\nreconnect data must give up when reset_complete not finished\nfix: add iptables -w to altnetowrking and Source\nget model ⇒ depend on manufacturer (from lsusb) for ZTE\nWEB API: report bandwidth\nCollectd + Web gui\nttl for _run_cached\nignore Ethernet-USB adapters\nmtu common and individual per modem\nhuman readable bytes counters in bandwidth reports\nmongodb: cached responses\nmake all functions accept IMEI|NICK\nmonthly Quota per user 2022-03-17:\nWEB sometimes show_status_json returns ”“ ⇒ Main WEB screen shows “Expecting value: line 1 column 1 (char 0)” and stucks and doesn’t do further refreshes\nExtra usersin addition to main uper proxy\nv1/changelog.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v1:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v1:readme", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv1:readme\nTable of Contents\n1. Proxysmart manual.\n1. Brief details\n2. Adding modems\n2.1 Adding a new modem (USB)\n2.2. Adding a LAN modem.\n2.3. Adding a virtual modem (backend proxy).\n3. Proxy credentials for new modems\n4. Where is WebApp\n5. How to use proxies\n6. Get list of all modems & their external IPs\n7. Reconfigure all modems & proxies.\n8. How to change proxy credentials for a modem. How to rename a modem.\n9. Reset (change) IP on a modem.\n10. How many modems can I run on a single computer?\n11. How to set TTL and why?\n12. How to set MTU and why?\n13. How to set extra settings for a modem.\n14. How can I access the web interface admin panel of each modem?\n14.1. How can I prevent access to modems web interface via proxy?\n15. How to set monthly traffic quota per modem?\n16. How to make my proxes Open (i.e. not requiring authentication )\n17. Get monthly/daily proxy usage.\n18. How to get current number of connections for a modem?\n19. How to read SMS from a modem.\n20. How to change WebApp password\n21. OS Spoofing\n22. Performance tuning\n23. How to lock network mode per modem\n24. What if a modem connected via 3G or 2G, and I want 4G?\n25. I want to add extra users to a proxy\n26. Is IPV6 supported?\n27. Nagios integration.\n28. Secure (anonymous) IP rotation links.\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n29. “Dirty” IP reset.\n30. Exclude some modems\n31. Use custom Speedtest server.\n32. Minimum time between IP rotations\n33. How to block domains\n33.a. How to allow only whitelisted domains.\n34. How to re-rotate IP when IP doesn’t change?\n34.1 Prevent non-unique IP’s after IP rotation.\n35. How to forward proxy ports using HAproxy?\n36. How to use newer 3proxy version 0.9 ?\n37. Where are proxy logs.\n37.1. No logs policy\n38. My proxies are slow.\n39. My proxies are slower than the same SIM card in a Phone.\n40. How to forward proxy ports via each modem individually?\n41. Auto-rebooting modems.\n42. My proxy is offline and showing Red in the WebApp.\n43. Parallel processing of modems.\n44. IP's are rotated on their own\n45. Install logging of all requests in single place\n46. PPP modems\n2. Project description\n1. project architecture (clients, servers, websites),\n2. Online services are used:\n3. CLI API\n1. show status\n2. full reconfiguration\n3. apply setting for a modem by IMEI\n4. reset IP on a modem\n5. reboot a modem\n6.1. Reset a modem via USB\n6. Run speedtest on all modems at once\n7. report bandwitdh\n8. reset bandwidth counter on a modem\n9. list sms on a modem\n10. send sms\n11. purge SMS\n12. send ussd\n13. get bandwidth counters from a modem\n14. Get IP rotations log for a modem\n15. Get Top hosts from a modem\n16. Report IP uniqueness\n4. WEB API\n1. Web API description.\n2. List all modems ( full status, slow)\n3. List all modems ( brief status, fast )\n4. Single modem status\n5. Reset (change) IP on a modem.\n6. Reboot a modem\n7. Send SMS\n8. Send USSD and read response\n9. Read SMS from a modem\n10. Read bandwidth stats from a modem\n11. del\n12. Reset bandwidth stats for a modem\n13. Reset a modem via USB\n14. Get IP rotations log for a modem\n15. Apply settings for a modem\n16. Purge SMS from a modem\n17. Get Top hosts from a modem\n18. Report IP uniquness\n19. Store a modem object in Mongodb\n20. Export backup\n5. Mongodb integration\n5.1. Mongodb schema\n5.2. Moving Mongodb to other server\n6. Installation\n1. Initial installation\nDevelopment version installation\n2. Upgrade\n3. Post Installation\n4. Cloud VPS integration.\nDo I need a VPS?\nVPS setup steps.\nCloud VPS IP change\n5. Forwarding ports through your own LAN router.\n7. License\n1. Demo license\n2. Requesting a License\n2. License installation\n3. Restoring Demo license.\n8. Mobile (4G/5G) VPN\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n8.1.2. Installation with TCP protocol (through LAN router)\n8.1.3. Installation with UDP protocol (through VPS)\n8.1.4. Installation with UDP protocol (through LAN router)\n8.2. Extra profiles for a modem\n8.3. Mobile VPN, how to connect\n8.4. Many users with the same profile\n8.5. Mobile VPN logs\n9. Bugs and Limitations\nLTE modules\nLAN routers\nOpenvpn profiles\n1. Proxysmart manual.\n1. Brief details\n\nI have developed a software that allows you to run your own 4g proxy farm. It runs on a Linux box (PC) with USB hub and the modems.\n\nFunctions:\n\nIP resets on modems\nWebApp for checking status of each modem\nWEBAPI for actions like querying status, IP rotation, getting used bandwidth for the day, running speedtests\nsetting bandwidth quota per modem per month\nbandwidth throttling per modem\nexposing proxy ports, so they are available from world wide\nreadingSMS and USSD\nOS spoofing, to simulate TCP fingerprints of: MacOS  iOS  Windows  Android\ncustom MTU per modem\nproxy ACLs (what to allow/deny to proxy users)\nBasic configuration.\n\nVariables are set /etc/proxysmart/conf.txt.\n\nEach variable has brief description in place.\n\n2. Adding modems\n2.1 Adding a new modem (USB)\nremove PIN from the modem’s SIM card and plug in the modem into USB port or USB hub.\nCheck whether your modem Web App (e.g. Huawei’s E8372 / E5xxx or ZTE MF79 or Alcatel MW4x ) requires authentication, and if it does, set its admin password to admin123. Basically to the value of $DEFAULT_HILINK_ADMIN_PASSWORD variable in /etc/proxysmart/conf.txt. Otherwise many functions will not work, and its IMEI will be detected similarly to 2-1.1.2\nPlug in the modem\nwait ~5 minutes or run sudo proxysmart.sh reset_gently\nthe modem will appear in the WebApp, click EDIT on it, assign some unique Nickname, HTTP & SOCKS5 ports, Login and Password, then click APPLY\nrefresh the WebApp\ndone!\n2.2. Adding a LAN modem.\n\nMake sure LAN_MODEMS_ENABLE=1 is in /etc/proxysmart/conf.txt.\n\nConfigure the server with 2 LAN cards\n\nAssume you have 2 LAN cards, enp6s0 main LAN, enp2s0 is dedicated for LAN modems:\n\nnmcli con\n\nNAME                UUID                                  TYPE      DEVICE \nWired connection 1  bbbee134-51c3-3830-801f-9636470e0708  ethernet  enp6s0\nWired connection 2  000ed912-2d99-3f37-882b-d79ad13102e7  ethernet  enp2s0 \nRename Wired connection 2 → HUBS\nnmcli con modify Wired\\ connection\\ 2 con-name HUBS\nDisable DHCP and IPV6 on HUBS and assign static IPv4 address\nnmcli con modify HUBS ipv4.method manual \\\n    ipv4.addresses *************0/24 ipv6.method disabled ipv4.route-metric 300 \n\nSo you will add the LAN modems to ************/24 network as ************, ************ etc.\n\nsystemctl restart NetworkManager\n\nDelete old route\n\nip ro del default via ************\n\nConfirm you have only 1 default route via main LAN:\n\nip ro\n\nOutput\n\ndefault via *********** dev enp6s0 proto static metric 100 \n\nAdd the modem\n\nChange the modem’s web admin password to something stored in /etc/proxysmart/conf.txt in DEFAULT_HILINK_ADMIN_PASSWORD variable.\nChange the modem’s IP to something unique e.g. *************\nPut the modem into Ethernet switch routed to the Proxysmart server.\nOn the Proxysmart server make sure you can ping the new modem by its IP you set in previous step.\n\nOn the server, edit the /etc/proxysmart/lan_modems.yaml file, add a line\n\n- { gw: *************, dev: lanmodem10 }\n\nThe line contains its unique IP and the word lanmodem10 ( constructed from a word lanmodem plus a unique number ).\n\nThen either wait 5 minutes or run the command proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n2.3. Adding a virtual modem (backend proxy).\n\nA virtual modem is a in fact a redirect to a 3rd party proxy (HTTP or SOCKS5) so you can build own proxies based on that and resell them.\n\nThey even can be rotated if the backend proxy supports it.\n\nHow to add?\n\nMake sure BACKEND_PROXIES_ENABLE=1 is in /etc/proxysmart/conf.txt.\n\nEdit /etc/proxysmart/backend_proxies.yaml , post lines like these:\n\n- id: bproxy1\n  creds: ***************************\n  ip_reset: 'http://x.x.x.x:8083/api/changeIp?cool'\n\n- id: bproxy2\n  creds: https://lll:<EMAIL>:3129\n\nWhere:\n\nid has to be in the form 'bproxy' + a number\ncreds is a line with credentials of the backend proxy\nip_reset is an optional parameter , the URL for triggering IP rotation of the backend proxy\n\nThen either wait 5 minutes or run the command proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n3. Proxy credentials for new modems\n\nWhen adding new modems, please use\n\nunique HTTP ports from 8001 to 8999,\nunique SOCKS ports from 5001 to 5999.\n\nIf you want different ports ranges, update firewall.conf accordingly.\n\nplease use unique nicknames like dongleXXX or whatever else. Don’t use nicknames like randomXXX, that are assigned automatically.\n4. Where is WebApp\n\nOne of\n\nhttp://localhost:8080/\nhttp://LAN_IP:8080/\nhttp://VPS_IP:8080/\n\nBy default login/password are proxy / proxy.\n\n5. How to use proxies\nIf proxy ports are forwarded via remote cloud VPS: then the proxies can be used from all over the Internet, by that VPS IP and proxy port numbers.\nFrom the same LAN where multimodem server is located: by the server’s LAN IP and proxy port numbers.\n6. Get list of all modems & their external IPs\n\nRun: proxysmart.sh show_status for table-alike output.\n\n7. Reconfigure all modems & proxies.\n\nRun: proxysmart.sh reset_complete\n\nIt is done after reboot automatically by a Cron job.\n\n8. How to change proxy credentials for a modem. How to rename a modem.\n\nWebApp method\n\nclick EDIT on a modem, set new port or password or nickname for a modem\nclick APPLY\n9. Reset (change) IP on a modem.\n\nThe options are below.\n\nFrom Web App\n\nClick Reset Ip button.\n\nFrom command line.\n\nRun: proxysmart.sh reset_quick_nick dongle1\n\nWhere dongle1 is a Dongle “nickname” that is seen from output of proxysmart.sh show_status\n\nFrom Web API.\n\ncheck WEB API section of this manual.\n\nHow to rotate a modem periodically?\n\nWebApp method\n\nUpdate modem’s settings in the WebApp and click APPLY.\n\nFor global setting, edit /etc/proxysmart/conf.txt and set AUTO_IP_ROTATION=5 in order to rotate each modem every 5th minute. If set to 0, automatic IP rotation is not done. You can also set hourly rotation, set 120 for every 2h rotation.\n\nCron method\n\nInstall a Cron job. Edit a file /etc/cron.d/proxysmart, add a line ( or uncomment a commented line.. )\n\n*/10 * * * * root run-one /usr/local/bin/proxysmart.sh reset_quick_nick dongle3\n\nso that a modem with the Nickname dongle3 is rotated every 10 min.\n\nRepeat for each modem you want to rotate periodically.\n\n10. How many modems can I run on a single computer?\n\nHi , technically it depends on how powerful this PC is, and how intensively proxies are used.\n\nRaspberry PI - 4 proxies (roughly)\na miniPC (Intel NUC or similar) - up to 10\na Laptop like Core i5 - up to 30.\n\nAlso it depends on what Plan you buy.\n\nAlso it depends on USB configuration, for maximum number of modems:\n\ndisable USB3.0 in BIOS\nuse USB2.0 hubs\n11. How to set TTL and why?\n\nIn some cases custom TTL must be set in order to have Cell Operator think we are not using the modem in hotsport  tethering mode. I.e. we don’t share its data. By default Linux OS has ttl = 64. To change Cell Operator perception of the situation, we want to set it +1 i.e. 65.\n\nEdit /etc/proxysmart/conf.txt and set CUSTOM_TTL_SET=1 and CUSTOM_TTL_VALUE=65 and regenerate settings.\n\n12. How to set MTU and why?\n\nIn some cases different MTU values connect with different types of ISP’s. You may want to change it.\n\nMtu can be only lowered. E.g. if you have MTU 1390, you can set 1340. Not opposite.\n\n- Edit /etc/proxysmart/conf.txt and set CUSTOM_MTU_SET=1 . - Set MTU in the WebApp for each modem.\n\n13. How to set extra settings for a modem.\n\nThose are optional and are set in the WebApp\n\nWHITELIST - allowed customers IP’s who are not required to type in proxy password (IP-based auth).\nbandwidth (speed) limit. Values are in bits per second. E.g. 2/2 mbps will be 2000000/2000000.\nDENIED_SITES_ENABLE (1 or 0) and DENIED_SITES_LIST (list of blocked sites patterns).\nBandwidth Quota, (in Megabytes)\nMTU\n14. How can I access the web interface admin panel of each modem?\n\nOpen WebApp. Locate the modem. Configure a proxy on your desktop browser.\n\nUse proxy login & password as desribed below (14.1 chapter).\n\nVisit modem IP via that proxy.\n\n14.1. How can I prevent access to modems web interface via proxy?\n\nSince 2023-09-10 it is enabled by default.\n\nEdit /etc/proxysmart/conf.txt and set\n\nPROXY_ADMIN_ENABLE=1\nPROXY_ADMIN_LOGIN=SuperAdmin\nPROXY_ADMIN_PASS=Hqmz81mmZr\n\nAnd regenerate configs. So only admin user is allowed to use modems web interfaces, and normal proxy users are not.\n\n15. How to set monthly traffic quota per modem?\n\nIn the WebApp, set monthly traffic quota. Click EDIT & APPLY.\n\n16. How to make my proxes Open (i.e. not requiring authentication )\n\nSet OPEN_PROXIES=1 in /etc/proxysmart/conf.txt and regenerate all configs.\n\nNote, when proxy ports are forrwarded via a VPS, the proxies are available to any internet user. Use it with caution.\n\n17. Get monthly/daily proxy usage.\n\nClick bandwitdh stats in the WebApp, or run proxysmart.sh bandwidth_report_json dongleXXX, you will see these columns:\n\n“bandwidth_bytes_day_in”\n“bandwidth_bytes_day_out”\n“bandwidth_bytes_month_in”\n“bandwidth_bytes_month_out”\n“bandwidth_bytes_yesterday_in”\n“bandwidth_bytes_yesterday_out”\n18. How to get current number of connections for a modem?\n\nRun a command\n\nss -o state established | grep -c :8038\n\nBut change 8038 with HTTP port of a desired proxy\n\n19. How to read SMS from a modem.\n\nYou have these options.\n\nBrowse to the modem IP ( it is shown as GW in proxysmart.sh show_status ) through the proxy. Click SMS button.\nrun proxysmart.sh list_sms_for_a_modem_by_imei_json 999999999999999 i.e. IMEI of required modem.\nClick SMS in the WebApp\n20. How to change WebApp password\n\nBy default it is set to proxy / proxy. The password sits on the server’s folder /etc/nginx/. It Can be updated from the Terminal , with the command as follows:\n\n sudo htpasswd -b /etc/nginx/htpasswd proxy NewAweSomePassword999999\n\nThen it will ask for password for current Ubuntu user.\n\nIf you want to change username as well, just delete the file and then assign new password\n\nsudo rm /etc/nginx/htpasswd\nsudo htpasswd -b /etc/nginx/htpasswd MyNewUsername NewAweSomePassword999999\n\nHow to change WEB port\n\nedit /etc/nginx/sites-enabled/proxysmart.nginx and set other port and restart Nginx.\n\n21. OS Spoofing\n\nOs Spoofing is used to simulate other OS TCP fingerprints, MacOS  iOS  Windows  Android\n\nHow to enable OS Spoofing?\n\nIn the WebApp set destination OS per each modem.\n\nHow to test OS Spoofing ?\n\nVisit one of these websites (IP checkers) through a proxy. Find something like “OS TCP fingerprints”.\n\nhttp://witch.valdikss.org.ru/\nhttps://thesafety.us/\nhttps://Whoer.net , extended results\nhttps://browserleaks.com/ip\n\nWhat OS can I spoof?\n\nMacOS  iOS  Windows  Android\n\nCan I dump OS TCP fingerprint from a real device and use it?\n\nYes, contact me.\n\nI enabled OS TCP spoofing, but it is not working!\n\nThe reason may be that the operator passes all traffic through its internal proxy, or in other way modifies TCP signatures. Then local OS TCP modifications are overwritten. Is it bad? No! Because still traffic looks natural as it was coming from this operator network.\n\nTry other operator.\n\n22. Performance tuning\n\nWhen >10 modems are added, and when modem list is generated slowly, play with MAX_PARALLEL_WORKERS_STATUS variable, e.g. set it to 2 or 4. On faster CPU’s it can be set to 8.\n\nAlso try to disable OS TCP reporting, i.e. set ENABLE_VALDIK=0 in /etc/proxysmart/conf.txt. It will also make modem list generation faster.\n\nAlso you can disable detailed status, set QUICK_STATUS=1 in /etc/proxysmart/conf.txt & refresh the WebApp.\n\n23. How to lock network mode per modem\n\nSet TARGET_MODE in its settings in the Proxysmart WebApp. Allowed values:\n\nauto\n3g\n4g\n24. What if a modem connected via 3G or 2G, and I want 4G?\n\nRotate its IP.\n\n25. I want to add extra users to a proxy\n\nIn the WebApp, click EDIT on a modem, add some extra users, click APPLY.\n\n26. Is IPV6 supported?\n\nYes but it’s off by default.\n\nOn modems , edit APN and set APN type for both IPv4 and IPv6 , e.g. Ip4Ip6 or Ip4+ip6, there is a dropdown list for that.\n\nOn Proxysmart box: Update /etc/proxysmart/conf.txt with\n\nALTNETWORKING_VERSION=2\nIPV6_SUPPORT=1\n\nand reset configuration proxysmart.sh reset_complete ; or even better do a reboot.\n\n27. Nagios integration.\n\nThere is a plugin embedded, run it as root,\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh IMEI\n\nor\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh NICKNAME\n\nso it will return OK/WARN/CRIT/UNKNOWN and corresponding exit code.\n\n28. Secure (anonymous) IP rotation links.\n\nThese links\n\nCan be safely passed to your customers. They don’t reveal real dongle parameters like IMEI or Nickname.\nThey don’t require HTTP basic authentication\nThey have limited lifetime , it is set in /etc/proxysmart/conf.txt as RESET_LINK_VALIDITY variable, (default value : 5 years).\nThey depend on proxy password. So, when you change proxy password - old IP rotation links will stop working.\n\nA link can be retrieved this way: Open dongle status (click on its IMEI!) in the WebApp, take RESET_SECURE_LINK→URL value.\n\nIf you realized you gave a link to a customer, and want to revoke it, just set new password for the proxy.\n\nIf you want to invalidate all links of all modems, set a new secret: set RESET_LINK_SECRET in /etc/proxysmart/conf.txt .\n\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n\nIt is needed for proper work of HTTP/3.0 which uses UDP.\n\nQUIC (UDP over socks5) will work either in your LAN or via a VPS. Steps are below.\n\nSteps on VPS :\n\nRun:\n\ninstall logrotate rule so Gost logs won’t fill up the disk space.\n\necho '\n/var/log/gost/*.log {\n    missingok\n    compress\n    notifempty\n    hourly\n    rotate 48\n    copytruncate\n}\n' > /etc/logrotate.d/gost\n\necho '35 * * * * root /usr/sbin/logrotate -v /etc/logrotate.d/gost' > /etc/cron.d/gost-logrotate \n\n\nInstall sudoers so proxysmart server can run commands with sudo on the VPS:\n\necho 'fwd  ALL=NOPASSWD:  ALL' >  /etc/sudoers.d/proxysmart \nchmod 400 /etc/sudoers.d/proxysmart\nusermod -s /bin/bash fwd\n\nInstall Gost v2\n\nARCH=linux-amd64\nVER=2.11.3\ncurl -L -o /tmp/gost.gz https://github.com/ginuerzh/gost/releases/download/v$VER/gost-$ARCH-$VER.gz\ngunzip -dc /tmp/gost.gz  > /usr/local/bin/gost.new\nchmod 755 /usr/local/bin/gost.new\nmv  /usr/local/bin/gost.new /usr/local/bin/gost\ngost -V\n\nInstall Gost v3\n\nVER=3.0.0-rc8\nARCH=linux_amd64\nURL=\"https://github.com/go-gost/gost/releases/download/v$VER/gost_${VER}_$ARCH.tar.gz\";\nD=`mktemp -d`;\n( cd $D;\n  curl -L -o /tmp/gost3.tgz \"$URL\";\n  tar xf /tmp/gost3.tgz gost;\n  mv gost /usr/local/bin/gost3.new \n  );\nrm -rf $D;\nchmod 755 /usr/local/bin/gost3.new;\nmv /usr/local/bin/gost3.new /usr/local/bin/gost3;\ngost3 -V\n\nif Haproxy is not installed, do nothing.\n\nif Haproxy installed: free up SOCKS ports (5xxx) from Haproxy: edit /etc/haproxy/haproxy.cfg and delete section frontend fe_SOCKS5 and restart it systemctl restart haproxy.service\n\nSteps on Proxysmart server :\n\nset in /etc/proxysmart/conf.txt :\n\nQUIC_SUPPORT=1\nGOST_VER=gost\n\nand run proxysmart.sh reset_complete.\n\nNote: make sure the VPS has enough RAM, each proxy needs 50MB of RAM. Also add swap if needed.\n\n29. “Dirty” IP reset.\n\nIt may be needed when you need even faster IP reset. In this case, post-checks are not made, so it is not sure if the modem really went online after IP reset. It can be activated by DIRTY_IP_ROTATION=1 in /etc/proxysmart/conf.txt.\n\n30. Exclude some modems\n\nIn /etc/proxysmart/conf.txt\n\nby Device name, populate this array IGNORED_DEV=( modem132 modem0000000002) – array of Network Interfaces that are not processed\nby IMEI, populate this array IGNORED_IMEI=( 9999999999999999 8888888888888888 ) – array of IMEI that are not processed\n31. Use custom Speedtest server.\n\nIt is useful when for some reason you want to run speed tests towards a custom server, instead of Ookla servers. So set up a Apache web server with a large file (500MB) and get 2 URL’s, one will test download and 2nd will test upload. The latter must accept large POST data.\n\nThe commands to setup a server part\n\napt install apache2\ndd if=/dev/urandom  of=/var/www/html/file.bin bs=1M count=500\n\nUpdate /etc/proxysmart/conf.txt with IP of the WEB server:\n\nSPEEDTEST_CUSTOM=1  \nDL_URL=http://$VPS/file.bin\nUL_URL=http://$VPS/i.php\n32. Minimum time between IP rotations\n\nIf you want to avoid too frequent IP rotations triggered by your users – set MINIMUM_TIME_BETWEEN_ROTATIONS=120 e.g. for 120 seconds minimum delay in /etc/proxysmart/conf.txt .\n\n33. How to block domains\nCheck (enable) DENIED_SITES_ENABLE in the WebApp\nDENIED_SITES_LIST is a list of domains that will be blocked, both HTTP and HTTPS, plus their subdomains. E.g. if you list porn.com, then also www1.porn.com,www.porn.com,porn.com are blocked.\n\nNote for Socks5 proxies\n\nWhen a domain blacklist is imposed, then by default users still can access blocked sites by their IP’s.\n\nIn order to prevent it, set DENY_IP_REQUESTS=1 and VERSION_3PROXY=0.9 in /etc/proxysmart/conf.txt and run proxysmart.sh reset_complete for resetting all configuration.\n\n33.a. How to allow only whitelisted domains.\nCheck (enable) WHITELIST_SITES_ENABLE in the WebApp\nWHITELIST_SITES_LIST is a list of domains that are allowed, while other are blocked. Both HTTP and HTTPS, plus their subdomains. E.g. if you list bbc.com, then also www.bbc.com,www1.bbc.com are listed.\n34. How to re-rotate IP when IP doesn’t change?\n\nIn /etc/proxysmart/conf.txt set RETRY_IP_ROTATIONS=1 .\n\nSo when Old_IP == New_IP, then IP rotation is retried. Up to MAX_RETRY_IP_ROTATIONS attempts which is by default 3.\n\n34.1 Prevent non-unique IP’s after IP rotation.\n\nFor example to prevent using IP’s that were in use 1 time (or more) within last 24h: set in /etc/proxysmart/conf.txt :\n\nRETRY_IP_ROTATIONS=1                 # enables Re-rotation\nNON_UNIQUE_IP_OCCURS=\"1\"             # how many times an IP must occur to be considered NonUnique. E.g. 1\nNON_UNIQUE_IP_PERIOD=\"24hour\"        # during which period an IP must occur to be considered NonUnique. E.g. 1day or 1hour\n35. How to forward proxy ports using HAproxy?\n\nWhy? In order to enable client IP whitelisting, i.e. 3proxy on proxysmart server will see original client IP and will be able to use whitelising.\n\nSteps:\n\n1. On Proxysmart server\n\nset PROXY_PORTS_FORWARDER_SOFTWARE=ssh+haproxy in /etc/proxysmart/conf.txt\nrun proxysmart.sh reset_complete for resetting all configuration.\n\n2. On the VPS\n\nRun apt install haproxy rsyslog\n\n3. Copy Haproxy and Syslog conf files from the Proxysmart server files to the VPS\n\nscp them from the Proxysmart server to the VPS. $VPS variable is sourced from the conf.txt\n\nsource /etc/proxysmart/conf.txt\ncd /usr/share/doc/proxysmart/examples/haproxy_integration/\nscp etc/haproxy/haproxy.* $VPS:/etc/haproxy/\nscp etc/rsyslog.d/49-haproxy.conf $VPS:/etc/rsyslog.d/\n\n4. On the VPS\n\nRun\n\ntouch /var/log/haproxy.log\nchown syslog:syslog /var/log/haproxy.log \n\nsystemctl restart rsyslog.service \nsystemctl restart haproxy.service \nsystemctl status haproxy.service\n\nMust be green and show active(running).\n\n5. Post check\n\nTest a proxy via VPS IP and you will original client IP in 3proxy logs.\n\n36. How to use newer 3proxy version 0.9 ?\n\nEdit /etc/proxysmart/conf.txt , set VERSION_3PROXY=0.9 , run proxysmart.sh reset_complete.\n\n37. Where are proxy logs.\n\nOn the Proxysmart server in a folder /var/log/3proxy/ , each filename is named for HTTP proxy port.\n\nLogs are rotated daily and 90 copies are saved, details are in /etc/logrotate.d/3proxy.\n\nLogs of IP rotations are in a folder /var/log/proxysmart/dongle_rotations/.\n\n37.1. No logs policy\n\nIf you want to run NoLogs policy, create a cron script that deletes the logs, i.e. the files\n\n/var/log/gost/*\n/var/log/3proxy/*\n/var/log/sniproxy*\n/var/log/haproxy*\n38. My proxies are slow.\n\nAssume a chain UsbModem→PC→VPS→ProxyUser. Final Proxy speed is limited by:\n\nDownload speed of the modem.\n\nIt can be measured on the side of the PC e.g. in the Proxysmart WebApp by clicking the Speedtest button.\n\nHow to improve it?\n\ntry other carriers\ntry other modems\ntry better location with better signal\nUpload speed from PC to VPS.\n\nNormally it correlates with quality of home internet (Fiber/xDSL) and can be measured by running speedtest on the PC in browser or in Terminal (speedtest-cli). Upload value has to be high.\n\nHow to improve it?\n\nget a better home internet with better upload\nswitch from WiFi to Ethernet\nDownload speed from VPS to the ProxyUser\n\nIt can be measured by downloading a file from VPS to the Proxyuser.\n\nHow to improve it?\n\nChange location of the VPS to a Cloud Hoster that has better reachability to the clients from all over the world\n39. My proxies are slower than the same SIM card in a Phone.\n\nReason 1: Compare LTE category of the modem and the phone. Phone has higher LTE cat e.g. 12..20, while modem has LTE cat 4..6 (depends).\n\nReason 2: when the speed is really bad (about 1mbps) then it is Operator's throttling. Perhaps you bought a plan that allows only phones/tablets and doesn't allow modems.\n\n40. How to forward proxy ports via each modem individually?\n\nWhy is it needed? When home base internet is unstable or its upload speed <15mbps.\n\nA VPS is needed in order to expose the ports this way ( see VPS integration chapter ).\n\nHow it works\n\nEach proxy forwards its port through its modem, not using base internet.\n\nPRO's :\n\nHome base internet speed & stability is not important\n\nCON's :\n\neach modem is working in bidirectional mode\nproxy speed is limited to 4G Upload speed which is slow\n\nSteps: on Proxysmart server\n\nset PROXY_PORTS_FORWARDER_TYPE=cell in /etc/proxysmart/conf.txt\nrun proxysmart.sh reset_complete for resetting all configuration.\n41. Auto-rebooting modems.\n\nSometimes only a reboot can fix a modem. In order to enable, set AUTOREBOOT_DONGLES=1 in /etc/proxysmart/conf.txt. How it works:\n\nif a situation occurs , “reboot score” of a modem is increased by the value, according to the situation:\nSCORE_IP_ROTATION_FAIL=10                   # score increments when IP rotation failed\nSCORE_IP_NOT_DETECTED=2                     # score increments when IP not detected\nSCORE_IP_RECONNECT_FAIL=10                  # score increments when IP not auto-reconnected\nSCORE_WWAN_DATA_FAIL=10                     # score increments when WWAN device can't establish Data connection\nSCORE_WEBAPP_FAIL=20                        # score increments when the modem's WebApp is stuck\nwhen the modem’s reboot score reaches MAX_REBOOT_SCORE then the modem is rebooted.\nspecial case, do USB reset instead of a reboot, when AUTO_USB_RESET_DONGLES is 1, it is useful when modems’ WEB APP is not available.\n42. My proxy is offline and showing Red in the WebApp.\n\nCheck if the modem has good signal.\n\nCheck if the modem has correct APN (set in its Web Dashboard).\n\nCheck if its SIM card is active (not blocked on Operator side) and is topped up.\n\nCheck the modem on another PC (e.g. your own desktop).\n\n43. Parallel processing of modems.\n\nEdit /etc/proxysmart/conf.txt , set PARALLEL_STARTUP=1 .\n\nSo the modems are processed in parallel, in the number of threads defined in MAX_PARALLEL_WORKERS_STATUS variable (default 8).\n\n44. IP's are rotated on their own\n\nIf you don't rotate IP's and they are detected each time as a new IP - it is natural behaviour of mobile provider, when it routes its clients through random different gateways every 1 minute or so. T-Mobile USA is known of doing so.\n\n45. Install logging of all requests in single place\n\n*the Goal*\n\nGet single log of all requests from Proxies (HTTP/Socks5) clients and VPN clients.\n\nInstallation On Proxysmart server\n\nEdit /etc/proxysmart/conf.txt , set SNIFFER_ENABLED=1 .\n\nrun proxysmart.sh reset_complete\n\nWatch the log /var/log/proxy_log.log on Proxysmart server.\n\nIt is rotated and 365 daily copies are stored on disk.\n\nIt can also be installed on a VPS if the VPS is working as proxies frontend.\n\nInstallation On VPS\n\nRequired files (copy from Proxysmart server to the VPS):\n\n/usr/local/bin/proxy_log.sh\n/etc/systemd/system/proxy_log.service\n/etc/logrotate.d/proxy_log\n\nrun :\n\napt update && apt install tshark\nsystemctl enable proxy_log --now \n\nWatch the log /var/log/proxy_log.log on VPS.\n\nLog format\n\nFile: /var/log/proxy_log.log\n\n    _ws.col.Time  frame.interface_name   ip.src  tcp.srcport   ip.dst   tcp.dstport  \n    #   1          2                        3       4           5           6\n    \n    socks.remote_name    socks.dst    socks.port   socks.dstport \n    # 7                         8         9         10\n    \n     http.request.method    http.host  \n    #   11                  12        \n\n     tls.handshake.extensions_server_name  x509ce.dNSName\n    #   13                                  14\n46. PPP modems\n\nThese are very old 3g modems like Huawei E303, E173, E156; ZTE MF110, MF193, MF190. In order to make them work with proxysmart,\n\nedit /etc/proxysmart/conf.txt and set PPP_MODEMS_ENABLE=1 .\n\nMake Quectel LTE modules work in PPP mode\n\nWhy? sometimes they fail working in QMI mode. So:\n\nedit /etc/proxysmart/conf.txt and set PPP_MODEMS_ENABLE=1\nplace a file /etc/udev/rules.d/21-wwan.rules\n# ignore QMI_WWAN endpoints on Quectel, to make it work in PPP mode.\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"2c7c\" , ATTRS{idProduct}==\"0125\",  ENV{.LOCAL_ifNum}==\"04\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\nre-plug Quectel modems or reboot Proxysmart server\n2. Project description\n1. project architecture (clients, servers, websites),\nonsite: box with Ubuntu, USB hub and modems\nremote: VPS with proxy ports (optional)\n2. Online services are used:\nhttp://ip.tanatos.org/ip.php which is simple PHP script that returns visitor’s IP. It is used to detect whether a modem is really online. Can be replaced with one of https://ifconfig.co or similar, but I was not happy with their reliabiality, they are down sometimes. The URL is defined in /etc/proxysmart/conf.txt.\nhttp://witch.valdikss.org.ru/ : used for detecting p0f and MTU\n3. CLI API\n1. show status\n\nShow full status of all modems, table (slower).\n\n# proxysmart.sh  show_status \n\nOutput:\n\nShow brief status of all modems, table, (faster)\n\nRun\n\n# proxysmart.sh  show_status_brief\n\nOutput:\n\nShow full status of all modems , json\n\n# proxysmart.sh  show_status_json \n\nOutput:\n\nShow status for a single modem, JSON\n\n# proxysmart.sh  show_single_status_json dongle111 \n\nOutput:\n\n2. full reconfiguration\n\nRun\n\n# proxysmart.sh reset_complete  \n\nOutput:\n\n3. apply setting for a modem by IMEI\n\nJSON output\n\n# proxysmart.sh   apply_settings_for_a_modem_by_imei  868723023562406 \n\nOutput:\n\nPlain text output.\n\n proxysmart.sh  apply_settings_for_a_modem_by_imei_raw    359999999999999 \n\noutput:\n\n4. reset IP on a modem\n\nArgs: IMEI or NICKNAME.\n\nJSON output:\n\n# proxysmart.sh   reset_modem_by_imei    899999999999999 \n# proxysmart.sh   reset_modem_by_imei    Dongle222\n\nOutput:\n\nPlain text output:\n\n# proxysmart.sh  reset_quick_nick  899999999999999\n# proxysmart.sh  reset_quick_nick  Dongle222\n\nOutput:\n\n5. reboot a modem\n\nArgs: Nickname or IMEI.\n\nTEXT Output\n\nJSON Output\n\n6.1. Reset a modem via USB\n\nCan accept DEV name, IMEI or Nickname. So\n\nFor Text output:\n\nFor Json output.\n\n6. Run speedtest on all modems at once\n# proxysmart.sh  speedtest all\n\nResponse:\n\n7. report bandwitdh\n\nOn a single modem\n\nWith arbitrary time interval.\n\nOn all modems:\n\n8. reset bandwidth counter on a modem\n\nJSON output\n\n9. list sms on a modem\n\nJSON output\n\n10. send sms\n\nPlain output:\n\nJSON output:\n\n11. purge SMS\n\nPurges SMS from all folders.\n\nCall by IMEI or nickname,\n\njson output:\n\n12. send ussd\n\nPlain output\n\nJSON output:\n\n13. get bandwidth counters from a modem\n\n..use bandwidth stats..\n\n14. Get IP rotations log for a modem\n\nBy Nickname or IMEI\n\n15. Get Top hosts from a modem\n\nBy Nickname or IMEI\n\n16. Report IP uniqueness\n\nJSON output.\n\nTEXT output.\n\n4. WEB API\n1. Web API description.\n\nWEB API endpoint is the URL that Proxysmart WebApp available at.\n\nIt can be - LAN_IP:8080 when you call it from the same LAN - VPS_IP:8080 when you forwardded ports to the Cloud VPS - STATIC_IP:8080 when you forwarded ports via your LAN router and your ISP gave you STATIC_IP\n\nAlso attach proper username:password (the -u parameter).\n\nWhenever below you are seeing localhost:8080, replace it with the actual WEB API endpoint.\n\n2. List all modems ( full status, slow)\n\nRequest:\n\ncurl 'http://localhost:8080/apix/show_status_json' -u proxy:proxy \n\nResponse:\n\n3. List all modems ( brief status, fast )\n\nRequest:\n\ncurl localhost:8080/apix/show_status_brief_json -u proxy:proxy\n\nResponse:\n\n4. Single modem status\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/show_single_status_json?arg=dongle111    -u proxy:proxy\ncurl http://localhost:8080/apix/show_single_status_json?arg=899999999999999    -u proxy:proxy\n\nResponse:\n\n5. Reset (change) IP on a modem.\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reset_modem_by_imei?IMEI=899999999999999 -u proxy:proxy\ncurl http://localhost:8080/apix/reset_modem_by_nick?NICK=dongle22 -u proxy:proxy\n\nResponse:\n\n6. Reboot a modem\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reboot_modem_by_imei -d IMEI=860493043888886 -u proxy:proxy\ncurl http://localhost:8080/apix/reboot_modem_by_nick -d NICK=dongle2 -u proxy:proxy\n\nResponse:\n\nETA: ~ 1.5 minute\n\n7. Send SMS\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-sms' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' \\\n    --data-urlencode 'phone=+11111111111' \\\n    --data-urlencode \"sms=txt txt fff\"\n\nResponse:\n\n8. Send USSD and read response\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-ussd' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' --data-urlencode 'ussd=*100#'\n\nResponse:\n\n9. Read SMS from a modem\n\nRequest:\n\ncurl 'http://localhost:8080/modem/sms/862329888888888?json=1' -u proxy:proxy\n\nResponse:\n\n10. Read bandwidth stats from a modem\n\nRequest:\n\ncurl localhost:8080/apix/bandwidth_report_json?IMEI=899999999999999   -u proxy:proxy\n\nResponse:\n\nWith arbitrary time interval:\n\nRequest:\n\ncurl -G http://localhost:8080/apix/get_counters_imei -X GET -d IMEI=868888888888888 --data-urlencode 'START=2023-01-28 18:10' --data-urlencode 'END=2023-01-28 19:20:01' -u proxy:proxy \n\nResponse:\n\n11. del\n12. Reset bandwidth stats for a modem\n\nRequest (by IMEI or nickname):\n\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=dongle111    -u proxy:proxy\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=2727233671671676    -u proxy:proxy\n\nResponse:\n\n{\"result\":\"success\",\"debug\":null}\n13. Reset a modem via USB\n\nRequest either - by network interface e.g. modem77 - by Nickname - by IMEI\n\ncurl localhost:8080/apix/usb_reset_modem_json?arg=modem77      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=dongle22      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n14. Get IP rotations log for a modem\n\nRequest - by Nickname - by IMEI\n\ncurl localhost:8080/apix/get_rotation_log?arg=899999999999999  -u proxy:proxy \ncurl localhost:8080/apix/get_rotation_log?arg=dongle2          -u proxy:proxy \n\nResponse:\n\n15. Apply settings for a modem\n\nRequest:\n\ncurl http://localhost:8080/modem/settings -d imei=862329099999999 -u proxy:proxy\n\nResponse:\n\n16. Purge SMS from a modem\n\nRequest either - by Nickname - by IMEI\n\ncurl localhost:8080/apix/purge_sms_json?arg=Nick77      -u proxy:proxy\ncurl localhost:8080/apix/purge_sms_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n{ \"result\": \"success\", \"msg\": \"\" }\n17. Get Top hosts from a modem\n\nRequest:\n\n18. Report IP uniquness\n\nRequest:\n\n19. Store a modem object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a modem”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n20. Export backup\n\nDestination format: v1\n\nSo it can be later imported in V1 version of Proxysmart.\n\nRequest:\n\nDestination format: v2\n\nSo it can be later imported in V2 version of Proxysmart.\n\nRequest:\n\n5. Mongodb integration\n\nMongodb contains a collection modems with elements, 1 element = 1 modem.\n\nMandatory fields are\n\nIMEI\nname\nhttp_port\nsocks_port\nproxy_login\nproxy_password\n\nOther fields are optional.\n\nAfter editing a record in Mongodb, apply settings for the edited modem ( /modem/settings WEB API call).\n\n5.1. Mongodb schema\n\nSample file modems.json with 2 modems. 1st modem: only mandatory fields. 2nd modem: also arbitrary fields.\n\nschema\n\nNotes:\n\nbw_quota : bandwidth quota in MB\nPROXY_VALID_BEFORE: expiry of a port\nCONNLIM: number of allowed new connnections during 1 minute\nbandlimin: upload speed (from proxy user perspective), bits/second\nbandlimout: download speed (from proxy user perspective), bits/second\nTARGET_MODE - the mode (3g/4g/auto/default) the mode will work in.\nOS - spoofed destination OS, can be\n(empty or absent field) No spoofing\n“android:1” Android, p0f compliant but slow\n“android:3” real Android, almost like Linux\n“macosx:3” macosx:3\n“macosx:4” real MacOSX 12.6 / iPhone 13 Pro Max\n“ios:1” ios:1, p0f compliant\n“ios:2” ios:2, real Iphone\n“windows:1” real Windows 10\n5.2. Moving Mongodb to other server\n\nSometimes you want to move Mongodb to a cloud server.\n\nIn order to do so\n\nkeep collection name modems\nif your new mongodb is Mongodb 5+ and doesn’t have backward compatibility with the older clients, upgrade Mongodb Client to 5th version. Run on the Proxysmart box:\napt purge mongo\\* -y\n. /etc/os-release \nrm -f /etc/apt/sources.list.d/mongodb*\ncurl -L https://www.mongodb.org/static/pgp/server-5.0.asc | gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/mongodb-5.0.gpg\necho \"deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu $VERSION_CODENAME/mongodb-org/5.0 multiverse\" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list \napt-get update\napt install mongodb-mongosh mongodb-database-tools -y\nln -sf /usr/bin/mongosh /usr/local/bin/mongo\nupdate MONGODB_URI to new Mongodb URI in /etc/proxysmart/conf.txt\nif your new mongodb URI has +srv extension , install a PIP module: /var/www/proxysmart/venv/bin/pip install \"pymongo[srv]\"\ntest new Mongodb URI (I assume you updated MONGODB_URI variable in conf.txt above):\n    . /etc/proxysmart/conf.txt;\n    mongoexport --quiet --uri=\"$MONGODB_URI\" -c modems --forceTableScan\n\nit should return array of all elements in the modems collection\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n6. Installation\n1. Initial installation\n\nInstall a fresh OS.\n\nSupported OS and architectures:\n\nUbuntu 22.04, 20.04 on amd64, arm64.\nDebian 11 or Raspberry PI OS (ex-Raspbian) on amd64, arm64, armhf ( see Raspberry PI OS Notes below).\nRaspberry PI : https://ubuntu.com/download/raspberry-pi , choose Ubuntu Server 22.04 64bit\nNormal PC/laptop: Choose Server or Desktop, https://ubuntu.com/download, choose Ubuntu 22.04\n\nArmhf (arm 32 bit) doesn’t have Mongodb support!\n\nThose steps will take 5..10 minutes.\n\nUnplug any 4g modems.\n\nAdd an APT repo.\n\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | \\\n    gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\n\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\n\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo $VERSION_CODENAME main\" \\\n    | sudo tee /etc/apt/sources.list.d/proxysmart.list\n\nsudo apt update\nsudo apt install proxysmart\n\nThen follow instructions: It will tell what to do next ( run 2 files ).\n\nsudo /usr/lib/proxysmart/install_pkgs.sh\nsudo /usr/lib/proxysmart/install_webapp.sh\n\nReboot or run sudo proxysmart.sh reset_complete.\n\nAfter that either enjoy the Demo version at http://localhost:8080 or check License section.\n\nRockpi Notes\n\nIf LOGRAM is enabled ( a folder /var/log.hdd exists). Disable logging:\n\nmongodb, edit /etc/mongodb.conf, comment logpath directive.\n\nRaspberry PI OS (ex-Raspbian) Notes\n\nits kernel doesn't have xt_cgroup module , so you have to rebuild its kernel and include this module. It is recommended to switch to Ubuntu instead.\n\nDevelopment version installation\n\nWhy? To unlock new features that are not yet in the Main version.\n\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | \\\n    gpg --dearmor | sudo dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\n\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\n\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.dev $VERSION_CODENAME main\" \\\n    | sudo tee /etc/apt/sources.list.d/proxysmart.list\n\nsudo apt update \nsudo apt install proxysmart\nsudo /usr/lib/proxysmart/install_pkgs.sh\nsudo /usr/lib/proxysmart/install_webapp.sh\n\nReboot or run sudo proxysmart.sh reset_complete.\n\n2. Upgrade\n\nRun these commands:\n\nNOTE when dpkg will ask whether to replace old config file with new one, answer N (No) or just press Enter.\n\nSo old config file is saved.\n\nsudo apt update \nsudo apt install proxysmart\nsudo /usr/lib/proxysmart/install_pkgs.sh\nsudo /usr/lib/proxysmart/install_webapp.sh\n\nReboot or run sudo proxysmart.sh reset_complete.\n\n3. Post Installation\n\nPlug in all 4g modems you have, wait ~20 sec to let them initialize.\n\nNow test if ip li shows you any modem* interfaces, otherwise reboot to apply UDEV rules.\n\nIf it does, continue next below. (Otherwise reboot to apply UDEV rules.)\n\nNow you can start all the modems:\n\nYou have to run proxysmart.sh reset_complete or reboot the multi-modem server.\n\nCommand proxysmart.sh show_status will return a table with proxy port, external IP’s.\n\nNavigate to the WebApp http://localhost:8080 proxy/proxy and assign login/password/nicknames/ports to the modems.\n\nTest reboot, reboot the box, wait 1 minute, make sure the WebApp shows the modems.\n\nWebApp\n\nVisit http://your_box_lan_IP_address:8080/ or http://localhost:8080/\n\nDefault user:password pair is proxy:proxy\n\n4. Cloud VPS integration.\n\nWhy? The VPS is needed to forward proxy ports from a cloud VPS IP back to the multi modem server, so proxy ports are available for all users around the world.\n\nDo I need a VPS?\n\nA VPS is NOT needed when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nThe VPS server can be a cheap 1GB DigitalOcean / Linode / Vultr VPS or similar.\n\nIt has to be located as close as possible to the 4g farm server ( for lowest ping ).\n\nVPS setup steps.\nOn multi modem server\n\nCopy content from the file /root/.ssh/fwd.pub [1]\n\nOn VPS\n\nCheck if your VPS has no firewall. Disable it if it has – Both inside Linux OS and in hoster panel.\n\nCreate a user fwd , run :\n\nuseradd -s /bin/true -m fwd\nusermod -p '*' fwd\nmkdir -p /home/<USER>/.ssh/\ntouch /home/<USER>/.ssh/authorized_keys\nchown -R fwd: /home/<USER>/\nchmod 700 /home/<USER>/.ssh/\nchmod 600 /home/<USER>/.ssh/authorized_keys\n\nAdjust SSH server configuration, run :\n\nmkdir -p /etc/ssh/sshd_config.d\necho '\nGatewayPorts clientspecified\nClientAliveInterval 3\nClientAliveCountMax 3\nMaxStartups 100:30:1000\nLoginGraceTime 10\n' > /etc/ssh/sshd_config.d/proxysmart.conf\n\nservice ssh restart\n\nedit the file and paste the content [1] you copied in the step above. It is public part of fwd.ssh key that is used for communication from Proxysmart to VPS.\n\nnano /home/<USER>/.ssh/authorized_keys\n\nSave the file (press Control O) and exit the editor (Control x)\n\nOn multi modem server\n\nin /etc/proxysmart/conf.txt :\n\nset VPS variable to VPS IP\nset PROXY_PORTS_FORWARDER_ENABLE=1\nrun proxysmart.sh reset_complete\nedit /etc/systemd/system/fwdssh-vps.service , change CONNECT_HOST to VPS IP\nPick a free port for SSH_REMOTE_PORT, in most cases 6902 is fine.\nPick a free port for WEB_REMOTE_PORT, in most cases 8080 is fine.\n\nRun:\n\nsystemctl daemon-reload\nsystemctl start fwdssh-vps\nsystemctl enable fwdssh-vps\nsystemctl status fwdssh-vps\n\nMake sure it is green.\n\nOn VPS\n\nissue the command ss -tnlp and you will see proxy ports are bound with sshd daemon. That means the ports are forwarded.\n\nOn your private desktop or any other PC\nvisit http://vps_ip:8080 for the WebApp , default login:password is proxy:proxy\nyou can ssh to VPS IP and port 6902, and that goes to the multi-modem-server:22.\nCloud VPS IP change\n\nIf CLoud VPS IP is changed, update it on multi-modem-server side by defining new VPS variable in the /etc/proxysmart/conf.txt file, and rerun proxysmart.sh reset_complete there.\n\nAlso change VPS IP in /etc/systemd/system/fwdssh-vps.service on multi-modem-server and run these:\n\nsystemctl daemon-reload\nsystemctl restart fwdssh-vps\nsystemctl status fwdssh-vps\n\nMake sure it is green.\n\n5. Forwarding ports through your own LAN router.\n\nWhy? It is needed to forward proxy ports from a your ISP IP address back to the multi modem server, so proxy ports are available for all users around the world.\n\nIt is suitable when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nSteps\n\nConsult with documentation of your LAN router. Forward these ports from ISP IP address to the LAN IP of proxysmart server:\n\nTCP 8001-8999 for HTTP proxies\nTCP 5001-5999 for SOCKS5 pproxies\nTCP 8080 for the WebApp\nTCP 1194 for Openvpn (if it is working in TCP mode)\nUDP 1194 for Openvpn (if it is working in UDP mode)\n\nNotes\n\nAlso edit /etc/proxysmart/conf.txt . Replace myrouter.com with your actual Hostname or IP addresss.\n\nSo proxy credentials & links will be shown with your actual Hostname or IP addresss.\n\nPROXY_PORTS_FORWARDER_ENABLE=0\nREWRITE_WEBAPP_URL=1\nREWRITE_WEBAPP_TO=\"http://myrouter.com:8080\"\nREWRITE_HOST_IN_PROXY_CREDS=1\nREWRITE_HOST_IN_PROXY_CREDS_TO=\"myrouter.com\"\n\nrun\n\nsystemctl disable --now gost_forward_vpn\nsystemctl disable --now fwdssh-vps\n\n.. so forwarding system ports to a VPS is disabled.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete .\n\n7. License\n1. Demo license\n\nInstallation is shipped with default demo license.\n\nIt allows you to run proxy on 1 modem.\n\nIn order to run more modems, buy a License.\n\n2. Requesting a License\n2.1. Get the machine data\n\nMethod1. From the WebApp:\n\nOpen the proxysmart WebApp at http://localhost:8080 or http://LAN_IP:8080\nExpand License section\nCopy machine_data value\n\nMethod2. From the CLI:\n\nOpen terminal\nRun sudo proxysmart.sh license_status\nCopy machine_data value\n2.2. Contact Sales Team\n\nSend the copied value to proxysmart.org\n\n2. License installation\n\nYou will be given the license and license signature. Both are sequences of numbers and characters. Then submit both either via WebApp or CLI:\n\nsubmitting via WebApp\n\nOpen the WebApp , http://localhost:8080 , expand License section and type in the keys & submit both.\n\nsubmitting via CLI\n\nrun commands\n\nproxysmart.sh submit_license LICENSE\nproxysmart.sh submit_license_signature LICENSE_SIGNATURE\n3. Restoring Demo license.\n\nIf your paid license expired or broken, restore DEMO license, run:\n\nsudo cp -v /usr/share/doc/proxysmart/examples/license.txt* /etc/proxysmart/\n\n8. Mobile (4G/5G) VPN\n\nTogether with building proxies, it is possible to build Residential VPN.\n\nAssumption is, your proxies are already available via Cloud VPS.\n\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n\nIf ports forwarded through a VPS\n\nSteps on VPS\n\nAssume the VPS is already “integrated” - see VPS integration topic.\n\nPick a free TCP port on the VPS, run ss -tnlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501. We will call it OPENVPN_REMOTE_PORT.\n\nSteps on Proxysmart server\n\nedit /etc/systemd/system/fwdssh-vps.service\nuncomment and set Environment=OPENVPN_LOCAL_PORT=1194\nuncomment and set Environment=OPENVPN_REMOTE_PORT=1501 , to the OPENVPN_REMOTE_PORT from the step above.\nedit /etc/proxysmart/conf.txt and set OPENVPN_SERVER_PORT=1501 , to the OPENVPN_REMOTE_PORT from the step above.\nset OPENVPN_INTEGRATION=1\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $VPS_IP:$OPENVPN_REMOTE_PORT/TCP )\n\nsystemctl daemon-reload\nsystemctl restart fwdssh-vps\n\nThis just enabled port forwarding of TCP port OPENVPN_REMOTE_PORT to localhost:OPENVPN_LOCAL_PORT.\n\nThen run /usr/lib/proxysmart/install_openvpn.sh , it will do the installation of Openvpn server.\n\nCheck if /etc/openvpn/server.conf has proto tcp otherwise set it there.\n\nCheck if /etc/openvpn/client.ovpn.template has proto tcp and proper remote (with VPS IP and OPENVPN_SERVER_PORT) otherwise set it there.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete . For each modem it will generate a VPN profile.\n\nRestart proxysmart WebApp so it shows a web link for downloading the profiles systemctl restart proxysmart .\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.2. Installation with TCP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded TCP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nedit /etc/proxysmart/conf.txt and set OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_INTEGRATION=1\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/TCP )\n\nThen run /usr/lib/proxysmart/install_openvpn.sh , it will do the installation of Openvpn server.\n\nCheck if /etc/openvpn/server.conf has proto tcp otherwise set it there.\n\nCheck if /etc/openvpn/client.ovpn.template has proto tcp and proper remote (with $EXT_IP and OPENVPN_SERVER_PORT ) otherwise set it there.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete . For each modem it will generate a VPN profile.\n\nRestart proxysmart WebApp so it shows a web link for downloading the profiles systemctl restart proxysmart .\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.3. Installation with UDP protocol (through VPS)\n\nExpand\n\n8.1.4. Installation with UDP protocol (through LAN router)\n\nExpand\n\n8.2. Extra profiles for a modem\n\nIf you need 2 extra VPN profiles for a dongle dongle1 , run openvpn_create_user dongle1@a or openvpn_create_user dongle1@b .\n\n8.3. Mobile VPN, how to connect\n\nSo download the VPN profiles and connect using any VPN client software.\n\nDownload and install software:\n\nWindows: https://openvpn.net/community-downloads/ or https://openvpn.net/client-connect-vpn-for-windows/\n\nMacOS: https://tunnelblick.net/\n\nAndroid: https://play.google.com/store/apps/details?id=de.blinkt.openvpn or https://f-droid.org/en/packages/de.blinkt.openvpn/\n\nIOS: https://apps.apple.com/us/app/openvpn-connect/id590379981\n\nImport downloaded OpenVPN profile, tap Connect.\nuse Login and Password from the corresponding proxy.\n8.4. Many users with the same profile\n\nBy default only 1 device (PC, mobile, tablet) can use 1 OpenVPN profile. If you want multiple devices use 1 profile, edit /etc/openvpn/server.conf , comment out ;duplicate-cn line by removing the ; character, and run proxysmart.sh reset_complete.\n\n8.5. Mobile VPN logs\n\nLogs of openvpn sessions - /var/log/openvpn/sessions.log. Format:\n\n'$time','$type','$local_port','$proto','$duration','$bytes_in','$bytes_out','$Real_IP','$Real_PORT','$Ovpn_CERT','$Ovpn_IP','$IMEI','$proxy_login','$auth_reject_why'\ntype - session_start / session_stop / auth_reject\nlocal_port - local port of Openvpn server\nproto - tcp-server or udp\nduration - when type is session_stop, how many the session lasted\nReal_IP, Real_PORT - of a client\nauth_reject_why - when type is session_stop, the reason why auth was rejected\n9. Bugs and Limitations\nLTE modules\nIPV6 is not fully supported\nLAN routers\nIPV6 is not fully supported\nOpenvpn profiles\nBandwidth quotas don’t apply to Openvpn users\nv1/readme.txt · Last modified: 2024/05/09 14:03 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:changelog.v2dev ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog.v2dev", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog.v2dev\nChangelog\n\n2024-07-11\n\nIPv6 on LAN 4G/5G modems(routers)\n\n2024-07-10\n\nfix for Openvpn client - TCP looped connections. Affected only servers with real IP's\n\n2024-07-05\n\nAdded Android Xiaomi Mi A2 Lite (Redmi6 PRO) in USB+ADB mode.\nFix TTL for Lan modems for Os TCP spoofing.\n\n2024-06-28\n\nimproved reboot of stuck Proxidize modems.\n\n2024-06-27\n\nSierraWireless on USA Verizon - improved ( WWAN_MODEMS_MSS_FIX setting)\n\n2024-06-26\n\nadded LAN 4G router modem, Tenda 5G03\n\n2024-06-23\n\nfix - p0f spoofing stopped working after IP rotation.\n\n2024-06-22\n\nQuectel EG91.\n\n2024-06-21\n\nAlcatel (TCL) MW43.\n\n2024-06-17\n\nImproved sniffer proxy logging (memory & disk usage).\n\n2024-06-16\n\nQuectel RM520N-GL - improved SMS reading.\n\n2024-06-13\n\nimproved IP leak prevention.\n\n2024-06-07\n\nZTE MF79 - support TARGET_MODE during IP rotation.\n\n2024-06-06\n\nWebApp - Ports bandwidth stats - show previous month usage.\nWebApp - System status - show overall TCP connections count.\n\n2024-06-05\n\nImproved IP-rotation on IPv4-only modems in IPv6-enabled system.\n\n2024-06-03\n\nBug fixed of Openvpn connections stuck on Ubuntu 20.04.\n\n2024-06-02\n\nImproved Debian 11 and 12 support.\n\n2024-05-28\n\nWebApp>SystemStatus - show main server external IPv4 and IPv6\n\n2024-05-24\n\nGlobal IP uniqueness check (if enabled, checking IP uniqueness across all present modems)\n\n2024-05-24\n\nHuawei stick mode (E303, E3272, E3276), improved APN detection.\n\n2024-05-23\n\nUbuntu 18.04, Debian 10 support dropped\n\n2024-05-21\n\nModems Autoreboot - bug fixed.\n\n2024-05-18\n\nWebApp - show ping latency and packets loss.\n\n2024-05-17\n\nWebApp - a button to download the license (works if the license is issued after 2024-05-17)\n\n2024-05-09\n\nnew p0f signatures - android 14, IPhone 12 max pro, MacOSX 13.5, Windows 11 х64 Desktop\n\n2024-05-06\n\nAndroid phones in raw ADB mode (no extra apps), in particular Motorola Stylus 5G on Android 12\n\n2024-04-29\n\nadded LAN 5G/4G router modem, ZTE MC7010CA\nadded LAN 4G router modem, ZTE MF289\n\n2024-04-27\n\nAnsible playbok for setting up a VPS\n\n2024-04-25\n\nWebApp - new Desktop/Mobile layout\n\n2024-04-19\n\nadded LAN 4G router modem, Tenda 4G03Pro\n\n2024-04-18\n\nadded LAN 5G/4G router modem, ZTE MC801A and ZTE MC8010CA\n\n2024-04-16\n\nadded p0f signature for “Windows 10 64bit Desktop”\n\n2024-04-12\n\nadded LAN 4G router modem, Cudy LT500\n\n2024-04-06\n\nWebApp - expired and overquota ports are shown in pink.\nImproved Ubuntu 24.04 support\n\n2024-04-01\n\nadded new modem - Huawei K5160\n\n2024-03-28\n\nan option to download only last month proxy logs\n\n2024-03-26\n\nLong running tasks are now queued up - Modem Rebooting, USB resetting a modem, downloading proxy logs.\n\n2024-03-25\n\nimproved Tshark memory & disk consumption\n\n2024-03-21\n\nSierraWireless EM7455 now can work in PPP mode\n\n2024-03-02\n\nV2.1 release\nWebApp - improved speed\nWebApp - show next IP rotation time\nWebApp - cached statuses of modems + background statuses updater\nWebApp - choose Openvpn profiles format, default or OpenvpnConnnect3.4+\nWebApp - choose how proxy credentials are shown ( host:port:login:password or proto://login:password@host:port )\nWebApp - ApplySettings replaced with Re-Add Device - which is useful when a device is stuck and settings can't be applied\nWebApp - Link to IP info\noption to use xt-tls iptables module for more reliable domains blocking\ndaily/monthly quotas are automatically unblocked on the next day/month\nImproved support of Socks5 UDP behind DMZ in LAN's\nRefactored Ping graphs (single daemon instead of multiple per-modem processes)\n\n2024-02-27\n\nHuawei K5150 can work in Stick (NCM) mode\n\n2024-02-19\n\nlog Caller of IP rotation (schedule or manual or WebAppClick or ByLink)\n\n2024-02-17\n\nprevent IP rotation by link if proxy is expired or over quota\n\n2024-02-15\n\nSupport of SierraWireless EM7565, EM7511\n\n2024-02-07\n\nshow live Reboot scores in the WebApp\n\n2024-02-05\n\nUSB devices tree and USB errors log, are shown in the WebApp\n\n2024-01-31\n\nImproved Fibocom L860 ip rotation\n\n2024-01-29\n\nMax connections test\n\n2024-01-24\n\nHuawei B535 support\n\n2024-01-23\n\nMTU fix (slow speed fix) on USA Verizone on LTE chips\n\n2024-01-19\n\nWebApp - dynamic search in the table\nWebApp - show failed modems on top\n\n2024-01-18\n\nReplace product name & company name in the WebApp (WhiteLabel'ing)\n\n2024-01-17\n\nAlcatel IK41, improved IP rotation\n\n2024-01-10\n\nCompacted layout of HTML tables\n\n2024-01-08\n\nSierra Wireless EM7455: force set APN even before checking LTE status\n\n2024-01-04\n\nTelegram alerts for expiring proxies\nAlerts to a WebHook\nAlerts are saved in a local log file.\n\n2024-01-01\n\ngraphs for VPN bandwidth\n\n2023-12-30\n\nglobal sites blocklist can be edited in the WebApp\nWebApp improved, true\\false settings are shown as radio buttons\n\n2023-12-27\n\nAlerts to Telegram\n\n2023-12-27\n\nWebApp: adding modems Notes.\n\n2023-12-25\n\nWebApp: mobile signal is shown as colored bars\n\n2023-12-22\n\nsupport of Huawei 5G CPE H112-370 (ip rotation, sms read)\n\n2023-12-21\n\nadded 'modems states' : adding queued→adding→added→deleting , failed_to_add.\n\n2023-12-20\n\nnew WebApp (condensed layout)\n\n2023-12-19\n\nreduced IP rotation time on Huawei (E3372, E8372, E55*) by approx. 4 seconds\n\n2023-12-12\n\nimproved support of Huawei E3372s modems.\nexport/import of LAN modems & other local settings during the Backup/Restore\n\n2023-12-07\n\nbumped version to 2.0.\nAndroid VPN can send UDP.\n\n2023-12-06\n\nAndroid proxies - cover reconnects.\nReboot call in the WebApp opens in new tab.\n\n2023-12-05\n\nAndroid App can read SMS.\n\n2023-12-04\n\nfix Fibcom L860 on long USB id's.\n\n2023-11-30\n\nSIMA7630C (ip rotation, SMS read, reboot)\n\n2023-11-27\n\nAndroid VPN - added.\nAndroid VPN - proper native DNS from the Mobile Carrier\n\n2023-11-25\n\nopenvpn sessions log in a separate file ( $OPENVPN_SESSIONS_LOG )\nussd on Quectel EP06\n\n2023-11-23\n\nsniffer script to log all proxies requests and original clients IP's. Can be installed either on VPS or on Proxysmart server, or both. It generates easy to read & analyze single log of who,when,what.\n\n2023-11-21\n\nsupport of LTE module - T77W595 / LT4120\n\n2023-11-12\n\nbuild for Ubuntu 24.04 Noble\nwarning about license due to expire\n\n2023-11-08\n\nimport & export mongodb – a button in the WebApp\nexport to Proxysmart v2\n\n2023-11-07\n\nmongodb uri can be set in /etc/proxysmart/conf.d/*.inc\n\n2023-11-05\n\nParallel initialization of proxies during reset_complete & reset_gently (enablable with PARALLEL_STARTUP variable)\nfix IP rotation counter logging\nReboot button in the main WebApp for rebooting the server\nAndroid proxies - automatic ports probing and detection.\n\n2023-11-03\n\nOlax U90 - support of a new modem\nnew WEB API call for storing a modem object\nQuectel modems now obey with TARGET_MODE during IP rotation\n\n2023-11-01\n\nAlcatel HH71 autoreconnect\nLAN modems: improved detection; ability to reboot\nfix for adding modems when IMEI is not properly detected\n\n2023-10-25\n\nQuectel EC20 support; Imvproved Chinese letters in SMS\nimproved stability of IK41 (stick mode)\n\n2023-10-23\n\nindividual OS TCP spoofing (per proxy)\n\n2023-10-18\n\nimproved speed on Android proxies over 27mbps\n\n2023-10-13\n\nimproved SMS on IK41 (stick mode)\n\n2023-10-09\n\nPossibility to set Bandwitdh Quota direction, one of (in, inout, out).\n\n2023-10-04\n\nimproved Huawei E3272 support.\n\n2023-10-03\n\nTarget mode of a modem during IP rotation: can be set in the WebApp\nDeleted TARGET_MODE from conf.txt (global variable)\n\n2023-09-28\n\nfixed installation in India where github is blocked\nandroid: VPN integration - done\n\n2023-09-26\n\nAdd Huawei 5g CPE H122-373\n\n2023-09-25\n\nimproved ME-906s modems IP rotation\nPrevent getting non-unique IP’s after rotation\n\n2023-09-13\n\nallow more than 1 device to use 1 VPN profile\nVodafone K5161h supported\n\n2023-09-10\n\nWebApp: show total online modems count\nWebApp: show VPN profile link in the main screen of the webapp\nFibocom L860 - custom APN fix & obeying custom Net Mode (TARGET_MODE)\ndeal with dongles Nicknames conflicts\ndeal with HTTP and SOCKS port conflicts\n\n2023-09-09\n\nAndroid proxies - improved Ping graphs; improved Carrier detection\n\n2023-08-30\n\nadded auto reconnect for DW5811e\n\n2023-08-28\n\nimproved QUIC support: add switch 3proxy/gost for Socks software in LAN ; add switch gost/gost3 as Socks software\n\n2023-08-25\n\nqmicli , added timeouts\n\n2023-08-23\n\nzteMF79NV support\n\n2023-08-21\n\nAlcatel modems (MW40,MW42,HH71) obeys $DEFAULT_HILINK_ADMIN_PASSWORD\nAlcatel MW45 improved support.\n\n2023-08-15\n\nadd Reset Counters button to the Bandwidth report page.\n\n2023-07-17\n\nimprove SMS sending on ZTE MF667, MF927, MF688V3E, MF823, MF833, MF93\n\n2023-07-13\n\nIP uniqueness report\n\n2023-07-10\n\nTop hosts report (WEB API method + WebApp table).\nAuto USB reset when modem’s WEB APP is stuck ( AUTO_USB_RESET_DONGLES=1 )\n\n2023-07-06\n\nAdd proxies built on Proxysmart android App\n\n2023-06-04\n\nSIM7600G proper SMS read\n\n2023-05-26\n\nMain proxy user:added Threads limit\nExtra users: added Threads limit & Dual_Auth IP\n\n2023-05-11\n\nadd SIM7600G (Lte cat4)\n\n2023-05-04\n\nadd Foxconn DW5821e (LTE cat 16)\n\n2023-05-02\n\nusb reset button in the WebApp\n\n2023-05-01\n\ncached list of the modems in the main screen of the WebApp\n\n2023-04-13\n\nsupport of pure old Stick(PPP) modems like Huawei E173, E156; ZTE MF110, MF193\nadd E3276 and E303 in Stick(NCM) mode\n\n2023-04-05\n\nability to hide old and new IP’s from the output of IP rotation links.\nauto reboot modems under some circumstances (check conf.txt for configuration)\n\n2023-03-29\n\nadded RequestsPerSecond graphs in modem status\nadded Pings graphs in modem status\n\n2023-03-28\n\nZyxel NR5103E support\n\n2023-03-20\n\n3proxy 0.9.x support\nability to block direct requests to IP’s (e.g. when clients resolve hostnames on their side and bypass Hostnames blocklist) and process only requests containing hostnames (Forces DNS resolution on proxy server side)\n\n2023-03-19\n\nautofix mass storage on Huawei modems\n\n2023-03-11\n\nRepeat IP rotation in case of failure (went online>offline, or the same IP received)\n\n2023-03-09\n\nfixed issue of DNS cache on modems\nBlack 4g modem – MSM8916 with /json WEB API\n\n2023-03-06\n\nability to reboot Quectel if no SIM\nNew UI\nshow graphs in modems statuses\n\n2023-03-03\n\ntimers fix (allows arbitrary nonstandard minutes like 31,61,1222)\nAlcatel IK41 in “stick” mode\n\n2023-03-02\n\nChinese MSM8916 modems support\nbug fix: JS was cached forever\nproxysmart.log - grandparent pid is logged\nproxysmart.log - timestamps preceed every line\nztemf79 : reconnect data\nNum of current connections is reported properly\nBrovi Huawei E3372-325 added support\nQuectel EC25-AFX added\nAPI for status query fixed\n\n2023-02-07\n\nMF667 modem support\n\n2023-02-06\n\nQuectel EM12G\n\n2023-02-05\n\nability to set a custom URL in secure rotation links\nability to set a custom HOST in proxy credentials ports\n\n2023-02-04\n\nQuectel EC25 can send USSD\nIK40 support + USSD\n\n2023-02-02\n\nE3276 can send USSD\nMF823 is properly shown\n\n2023-02-01\n\n3proxy logs contains IMEI and Nickname\n\n2023-01-29\n\nAPI for getting bandwidth stats within arbitrary time interval\n\n2023-01-28\n\nHuawei 3372h-325 “BROVI”\n\n2023-01-27\n\nmongodb cache bug fixed\n\n2023-01-22\n\nvpn: default UDP\n\n2023-01-19\n\nallow only DEMO version on VirtualBox\n\n2023-01-17\n\nshow anonymous link in main screen of the WebApp\n\n2023-01-15\n\nAdded new modem, Xproxy XH22 Wireless\nadded table of content in README\nProxies “Extra users”: added individual speed limits\n\n2023-01-14\n\nQuectel RM520 support (5g)\n\n2023-01-12\n\nFibocom L860, Ipv6 support\n\n2023-01-11\n\npurging sms (for Huawei in HiLink mode only)\n\n2023-01-10\n\nVodafone K5161z supported\n\n2023-01-08\n\nDocumentation: Dirty ip reset\n\n2023-01-07\n\nandroid phones integration (USB tethering mode & remote mobile tunnel mode).\n\n2023-01-04\n\nSecure IP reset links are now shown as full URL\n\n2023-01-03\n\nStatic VPN IP’s based on Index from Openvpn PKI\n\n2022-12-30\n\nWeb: editable field for Pho.Number.\nLink for downloading Openvpn profiles for modems\n\n2022-12-25\n\nQUIC support (UDP, HTTP/3.0) for SOCKS5 proxies , check README\n\n2022-12-22\n\nredefine variables in /etc/proxysmart/conf.d/*.inc\n\n2022-12-21\n\ndetect when CellOp redirects to their Billing Page\n\n2022-12-11\n\nSet minimum time between IP resets\na function for re-add a device: proxysmart.sh add_dev $DEV\nUDEV plug-n-play (hook scripts)\n\n2022-12-02\n\nHuawei dongles in NCM mode (WWAN+AT)\n\n2022-11-27\n\nFiboCom L860-gl : basic work + sms send\nHuawei ME906s : basic work + sms send\n\n2022-11-20\n\nSierraWireless EM7455\n\n2022-11-18\n\nCLR900A\nFranklinT10\n\n2022-11-12\n\nBW quota with PMACCTD, block outgoing access\nignore extra user when it matches with existing user (main user or another extra user)\nAlcatel MW40 - proper operation now\n\n2022-11-09\n\nVerizone Jetpack AC791L\nmore fast status for unknown modems models\n\n2022-11-08\n\nalcatel IK41: reboot, sms list, helper\n\n2022-11-01\n\nsolve issue when run_cache xxx times out and prints nothing, thus is executed over and over again\n\n2022-10-29\n\nDocumentation: secure rotation links\nget_ConnectionStatus_n response must contain OK\ncell op 425 02 ⇒ must be converted too\nmodel_shown returns “” sometimes ⇒ won’t fix, it happens when MAX_PARALLEL_WORKERS_STATUS is too large\n\n2022-10-27\n\nNovatel MIFI\nFranklin T9 (R717)\ncustom DNS servers for proxies\n\n2022-10-25\n\nNM disable modem*\n\n2022-10-19\n\nxproxy.io modems support\nbug fixed: Configuration file /etc/systemd/system/proxysmart.service is marked executable.\nwhen main proxy user == one of extra users, use extra user password\n\n2022-10-16\n\nvpn: blocklist of domains. make sniproxy enablable in conf.txt\nlicense revoking status checked online\n\n2022-10-15\n\nrework of denied domains list, *.domains are added automatically\n\n2022-10-12\n\nUF906 (KuWfi, Anydata, TianJie) modems integration\n\n2022-10-10\n\ndirty ip rotation support\nsecure ip rotation links with auto expiration\n\n2022-10-06\n\nOS spoofing for VPN users\nvpn: mongodb integration\n\n2022-10-04\n\nzte mf688T proxidize can send SMS\nd-link dwm222 basic support (beta)\nsignal reported in main table of the WebApp\n\n2022-09-30\n\nadded OS TCP spoofing with p0f3 signatures (WOW!!!), including Mac OS X, iOS, Android, Windows. Total rework of OSfooler-ng!\nmodem WEB server warm-up (when 1st request is ignored and 2nd and subsequent are processed)\n\n2022-09-22\n\nmodems helpers reorg, make them more fast & cached\nadded hourly IP rotation\n\n2022-09-19\n\nget by mongodb - get 1st value\n\n2022-09-16\n\nzte mf79VIVO support\nextra delay after IP rotation\n\n2022-09-12\n\nreport APN\nzte mf79 - sms send\n\n2022-09-08\n\nimproved support of ZTE MF927\n\n2022-09-03\n\nreport LTE band in full status\n\n2022-09-02\n\nsupport of 4g LAN routers like Huawei Bxxx\n\n2022-08-27\n\nreport IP rotation history as table\n\n2022-08-24\n\nreport IP rotation history\nWebApp can edit modems\nminor fixes: [ ignoring cell fwding in altnetworking2, dns in vpn, etc ]\nshow_model - more correct modems model showing\n\n2022-08-09\n\nopenvpn support (residential VPN!)\n\n2022-08-04\n\nipv6 bug fixed (ipv6 not assigned)\nchange mongodb uri in conf.txt\n\n2022-08-02\n\nnagios plugin exit code fixed ; nagios plugin moved to the main codebase\n\n2022-07-30\n\nfix: del symlink of altnetworking on installation\n\n2022-07-28\n\nhaproxy check >= 2.2\nDocumentation: – periodic IP rotation – ipv6 support – VPS integration\n\n2022-07-22\n\napply_settings = > if absent in DB, assign random creds.\n\n2022-07-20\n\nfixed bug when license stopped working because of floating (??) disk size and RAM size.\n\n2022-07-01\n\nconvert numeric cellop MCCMNC to Letters\ndel old show_status\n\n2022-06-23\n\nipv6, haproxy integration, systemd-slices (altnetworking2).\n\n2022-06-22\n\nmongodb: timeout handling\n\n2022-06-18\n\nmodem_names OR static udev map ⇒ make configurable\nis syslog-ng needed for Haproxy ?? revert back to rsyslog.\n\n2022-06-11\n\ndouble get_external_ip when doing IP reset via API\n\n2022-06-07\n\nPeriodic automatic IP rotation , set AUTO_IP_ROTATION per modem or globally\n\n2022-05-30\n\nadd usb_reset ( usb_reset_individual_DEV ) API, by nick\n\n2022-05-22\n\nadd proxy live counters based on RRD\n\n2022-05-07\n\nwait UDEV_xx after usb reset  reboot\nadd reboot API (CLI|WEB)\n\n2022-05-03\n\nreboot doesn’t wait till modem is added to the system; use reset_gently for that\n\n2022-05-01\n\nbug fixed when a modem has the same LAN as EthLAN, so the modem took priority over LAN\n\n2022-04-26\n\nzte MF971\nadd Quectel modems support\nreport ICCID\n\n2022-04-23\n\nbw_quota bug - quota restrictions are applied on next proxy request only\nread bw_quota, bandlimin, bandlimout, DENIED_SITES_ENABLE, DENIED_SITES_LIST, mtu, extra_users : from Mongodb\n\n2022-04-18\n\ncx /usr/lib/nagios/plugins/proxysmart-nagios-helper.sh\nDNS in Jinja2\n\n2022-04-08\n\nFIX: error 500 when modem not found\nlicense in web\nImei not unique ⇒ show in dash why\nIgnore reset if IMEI is in IGNORED_IMEIS\nDEV not added ⇒ why? show in status\nshow_status_brief, header must be 1st row\nNagios plugin doesn’t work with encrypted source\nZTE MF93: reboot call\n\n2022-03-31\n\nhuawei K5150: reset_ip , list_sms, reboot, send_sms\n\n2022-03-29\n\nmore UWSGI workers\ndemo license\ndeb package building\nmongodb template\n\n2022-03-28\n\nzte mf79: sms list, reboot, IP reset\n\n2022-03-22\n\nindividual blocklists( DENIED_SITES_ENABLE, DENIED_SITES_LIST )\n\n2022-03-21 (PRE)\n\nSms read\nDefault Modem admin pw in config.\nForwarder type in config ( WAN / CELL )\nusb reset yin config ( kris )\nworkmode: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.74 hipi.pl admin123 | grep -i workmode | cut -d“’” -f4\ncell_operator name: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.59 hlcli NetworkInfo | grep ‘ShortName’ | cut -d“ -f4\nadd_indiv.. and reset_gently\nN by modemN\nRandom port and nickname assignment if absent in the db\nlocks\nmake reset_gently WAIT till reset_complete is done\nJSON: report_bandwidth\nJSON function: apply settings for a modem\nstop redirector BEFORE ip rotation to prevent stalled SSH connection on remote SSH server\nleave checking global lock but never acquire it.\nJSON for list modems\nTABLEd status\nshow_status: model & cell_operator & workmode:\nreset_complete: del all Cgroups & ip rules\nJSON function: reset IP\nHipi scripts accept GW & AdminPW args\nmake screenshort w. CutyCapt\nVnstat autorestart\nflush ipv6 on modems\nmake show_status default\norder: curl, hipi w. pass, hipi w/o pass, hlcli, ETC\nOptional ttl fix: Y\nsms sort\nre-connect DATA on modems, if ext_ip empty ⇒ <NAME_EMAIL>\nre-connect DATA on modems, at reset_complete ⇒ <NAME_EMAIL>\nmtu. Report via Valdik\nmtu. Set\nACL : optional: allow admin only to browse HiLink webUI\nstart with no modems reset_complete doesn’t touch ⇒ adding a modem ⇒ reset_gently doesn’t work\ndel ipv6 leak from modems\nACL : optional: deny some sites\nProper logging , with ssh connection, stderr, as one SHOT\nno LAN test\ndefault login/pw for autogenerated proxies\nreport rotation time\nsafe way to read vars from included files\ndeploy script\nN from modemN May not work when iflink is not like modemXXX\ntrim CELLOP\nmanual: tell what http and socks5 ports to use\nmanual: how to use proxy, from internet, from lan\nallow ::1 in ip6tables\ndef.gw via 1 available modem – add to reset_complete\nrun_cached , to prevent multiple hipi runs\nfix EVAL() of commented lines like ‘v=v #ddd’\nregenerate ssh keys for FWD, if absent\nget rid of sourcing files: constants\nrun cached: log hit or miss\nmanual- check if your vps has firewall. disable it. both inside and in hoster panel.\nshow_status: If CurrNet is ’’, use workmode\ncheck faked route on reset_gnelty\nshow_status_json : redirector statuses\nupdate Manual: api calls & sample output\nspeedtest , up & down\ninclude redirector status in status. enabled, failed/running, uptime\nshow_status: list all proxies std format. depends on FWD_ENABLE, list $VPS:\nreplace XARGS+source to just xargs\nlog TimeTook for /var/log/proxysmart.log\ncrypted source autoGen\nbw report change\ncheck /var/run/proxysmart/reset_complete_done ⇒ show_status_json & show_status BREAK\nignored IMEI\nignored devices\nencode with shc.\nanti-p0f\nintegrate ZTE_MF6xx api ( goform/goprocess)\ncheck whether modem WebUI is available, for get_imei(), show_status_json(), get_model()\nadded Anydata UF906, Alcatel IK41 & MW40\nis_locked to JSON status\nglobal RESET LOCK\nzte MF823 ip change\nUSSD\nSMS\nreport phone uptime\nreport USB uptime\nIgnore FRESHLY inserted modems in reset_gently. Let them init\ndon’t rely on bash RANDOM:\nnagios plugin\nzte MF93D\nindividual JSON status for a modem\nWebapp: +GW, +Local_IP, +current hostname, send sms , send ussd\nDHCP more attempts\nWeb: brief status\nbrief status\nreconnect data - additional command, separate from reset_gently\nIP-based ACL\nspeed limits for proxies\nopen proxy (y)\ncheck modem WEB ⇒ change check_tcp to curl.\nDEB=mktemp /tmp/reset_modem_by_imei.XXXXXX – purge it\ndhcp → migrate to dhcpcd from Nmap\nQUICK_STATUS=0\\1, show only few details in show_status..\nreconnect data must give up when reset_complete not finished\nfix: add iptables -w to altnetowrking and Source\nget model ⇒ depend on manufacturer (from lsusb) for ZTE\nWEB API: report bandwidth\nCollectd + Web gui\nttl for _run_cached\nignore Ethernet-USB adapters\nmtu common and individual per modem\nhuman readable bytes counters in bandwidth reports\nmongodb: cached responses\nmake all functions accept IMEI|NICK\nmonthly Quota per user\nWebApp fix : Main WEB screen shows “Expecting value: line 1 column 1 (char 0)” and stucks and doesn’t do further refreshes\nExtra users in addition to main proxy users\nv2/changelog.v2dev.txt · Last modified: 2024/07/11 15:08 (external edit)\nPage Tools\n    "}, {"title": "v2:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nTable of Contents\n1. Proxysmart manual [v2].\n1. Brief details\n2. Adding modems\n2.1 Adding a new modem (USB)\n2.2. Adding a LAN modem.\n2.3. Adding an Android phone\n2.4. Adding a virtual modem (backend proxy).\n3. Proxy credentials for new modems\n4. Where is WebApp\n5. How to use proxies\n6. del\n7. Reconfigure all modems & proxies.\n8. How to change proxy credentials for a modem. How to rename a modem.\n9. Reset (change) IP on a modem.\n10. How many modems can I run on a single computer?\n11. How to set TTL and why?\n12. How to set MTU and why?\n13. How to set extra settings for a proxy port.\n14. How can I access the web interface admin panel of each modem?\n14.1. How can I prevent access to modems web interface via proxy?\n15. How to set monthly traffic quota per modem?\n16. How to make my proxes Open (i.e. not requiring authentication )\n17. Get monthly/daily proxy usage.\n18. How to get current number of connections for a modem?\n19. How to read SMS from a modem.\n20. How to change WebApp password\n21. OS Spoofing\n22. Performance tuning\n23. How to lock network mode per modem\n24. What if a modem connected via 3G or 2G, and I want 4G?\n25. I want to add extra users on a modem\n26. Is IPV6 supported?\n27. Nagios integration.\n28. IP rotation links.\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n30. “Dirty” IP reset.\n31. Exclude some modems\n32. Use custom Speedtest server.\n33. Minimum time between IP rotations\n34. How to block domains\n35. How to allow only whitelisted domains.\n36. How to re-rotate IP when IP doesn’t change?\n37. Prevent non-unique IP’s after IP rotation.\n38. How to forward proxy ports using HAproxy?\n39. Custom DNS server for the proxies\n40. Where are proxy logs.\n41. No logs policy\n42. My proxies are slow.\n43. My proxies are slower than the same SIM card in a Phone.\n44. How to forward proxy ports via each modem individually?\n45. Auto-rebooting modems.\n46. My proxy is offline and showing Red in the WebApp.\n47. del\n48. IP's are rotated on their own\n49. Install logging of all requests in single place\n50. PPP modems\n51. Alerts to Telegram\n2. Project description\n1. architecture\n2. Online services are used:\n3. CLI API\n1. show status\n2. full reconfiguration\n3. apply setting for a modem by IMEI\n4. reset IP on a modem\n5. reboot a modem\n6.1. Reset a modem via USB\n6. Run speedtest\n7. report bandwitdh\n8. reset bandwidth counter on a port\n9. list sms on a modem\n10. send sms\n11. purge SMS\n12. send ussd\n13. get bandwidth counters from a modem\n14. Get IP rotations log for a modem\n15. Get Top hosts from a modem\n16. Report IP uniqueness\n4. WEB API\n1. Web API description.\n2. List all modems ( full status, slow)\n3. List all modems ( brief status, fast )\n3.1. List all active ports\n4. Single modem status\n5. Reset (change) IP on a modem.\n6. Reboot a modem\n7. Send SMS\n8. Send USSD and read response\n9. Read SMS from a modem\n10. Read bandwidth stats from a port\n11. del\n12. Reset bandwidth stats for a port\n13. Reset a modem via USB\n14. Get IP rotations log for a modem\n15. Apply settings for a modem\n15.1. Apply settings for a port\n15.2. Purge a port\n16. Purge SMS from a modem\n17. Get Top hosts from a modem\n18. Report IP uniquness\n19. Store a modem object in Mongodb\n20. Store a port object in Mongodb\n21. Export backup\n5. Mongodb integration\n5.1. Schema\n5.1.1. Modems\n5.1.2. Ports\n5.2 Workflow\n5.3 Configuration\n5.4 Moving Mongodb to other server\n6. Installation\n1. Initial installation\nDevelopment version installation\n2. Upgrade\n2.1. Upgrade from older V2\n2.2 Upgrade from V1\n3. Post Installation\n4. Cloud VPS integration.\nDo I need a VPS?\nVPS setup steps.\nCloud VPS IP change\n5. Forwarding ports through your own LAN router.\n7. License\n1. Demo license\n2. Requesting a License\n2. License installation\n3. Restoring Demo license.\n8. Mobile (4G/5G) VPN\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n8.1.2. Installation with TCP protocol (through LAN router)\n8.1.3. Installation with UDP protocol (through VPS)\n8.1.4. Installation with UDP protocol (through LAN router)\n8.2. Many users with the same profile\n8.3. Mobile VPN, how to connect\n8.4. Mobile VPN, FAQ\n8.4.1. Switch Openvpn protocol\n8.5. Mobile VPN logs\n9. Bugs and Limitations\nLTE modules\nAndroid phones\nVPN users\nPort ranges\nOS TCP Fingerprint spoofing\n1. Proxysmart manual [v2].\n1. Brief details\n\nThe software allows running your own 4g proxy farm. It runs on a Linux box (PC) with USB hub and the modems.\n\nFunctions:\n\nIP resets on modems (+ automatic rotation + checking IP uniqueness)\nWebApp for checking statuses of the modems, for creating users and ports, IP rotations\nWEB API for all actions\nBandwidth quotas and Speed limits per proxy\nExposing proxy ports, so they are available from world wide\nReading,sending SMS and USSD codes\nOS spoofing, to simulate OS TCP fingerprints of: MacOS, iOS, Windows,  Android (+any other OS)\nProxy ACLs (what to allow/deny to proxy users) - blacklists\nCreating mobile VPN together with proxies\nSocks5 supports UDP and QUIC (HTTP/3.0)\nNo leaks\nNative DNS from mobile carriers\nLarge set of supported USB modems, LAN routers, LTE modules, Android phones.\nBasic configuration.\n\nVariables are set in the WebApp→Global_settings and in /etc/proxysmart/conf.txt.\n\nEach variable has brief description in place.\n\n2. Adding modems\n2.1 Adding a new modem (USB)\nremove PIN from the modem’s SIM card and plug in the modem into USB port or USB hub.\nCheck whether your modem Web App (e.g. Huawei’s E8372 / E5xxx or ZTE MF79 or Alcatel MW4x ) requires authentication, and if it does, set its admin password to admin123. Basically to the value of DEFAULT_HILINK_ADMIN_PASSWORD variable in WebApp→GlobalSettings . Otherwise many functions will not work, and its IMEI will be detected similarly to 2-1.1.2\nPlug in the modem\nwait ~5 minutes or run sudo proxysmart.sh reset_gently\nthe modem will appear in the WebApp, click EDIT on it, assign some unique Nickname, HTTP & SOCKS5 ports, Login and Password, then click APPLY\nrefresh the WebApp\ndone!\n2.2. Adding a LAN modem.\n\nConfigure the server with 2 LAN cards\n\nAssume you have 2 LAN cards, enp6s0 main LAN, enp2s0 is dedicated for LAN modems:\n\nnmcli con\n\nNAME                UUID                                  TYPE      DEVICE \nWired connection 1  bbbee134-51c3-3830-801f-9636470e0708  ethernet  enp6s0\nWired connection 2  000ed912-2d99-3f37-882b-d79ad13102e7  ethernet  enp2s0 \nRename Wired connection 2 → HUBS\nnmcli con modify Wired\\ connection\\ 2 con-name HUBS\nDisable DHCP and IPV6 on HUBS and assign static IPv4 address\nnmcli con modify HUBS ipv4.method manual ipv4.addresses *************0/24 ipv6.method link-only ipv4.route-metric 300 \n\nSo you will add the LAN modems to ************/24 network as ************, ************ etc.\n\nsystemctl restart NetworkManager\n\nDelete old route\n\nip ro del default via ************\n\nConfirm you have only 1 default route via main LAN:\n\nip ro\n\nOutput\n\ndefault via *********** dev enp6s0 proto static metric 100 \n\nAdd the modem\n\nChange the modem’s web admin password to something stored in WebApp→GlobalSettings as DEFAULT_HILINK_ADMIN_PASSWORD variable.\nChange the modem’s IP to something unique e.g. *************\nPut the modem's LAN outlet into Ethernet switch together with the Proxysmart server.\nOn the Proxysmart server make sure you can ping the new modem by its IP you set in previous step.\nMake sure LAN_MODEMS_ENABLE=1 is in WebApp→GlobalSettings.\nAdd Lan modem in the Webapp→Edit_modems , scroll to the bottom, and add as lanmodem10 , ************* .\n\nThen either wait 5 minutes or run the command sudo proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n2.3. Adding an Android phone\n\nMain guide dedicated to adding Android Phones: Android phones guide\n\n2.4. Adding a virtual modem (backend proxy).\n\nA virtual modem is a in fact a redirect to a 3rd party proxy (HTTP or SOCKS5) so you can build own proxies based on that and resell them.\n\nThey even can be rotated if the backend proxy supports it.\n\nHow to add?\n\nMake sure BACKEND_PROXIES_ENABLE=1 is in WebApp→Global_settings .\n\nAdd them the Webapp→Edit_modems→Virtual modems\n\n, scroll to the bottom, and add each with the following fields\n\nid has to be in the form 'bproxy' + a number e.g. bproxy1 or bproxy2\ncreds is a line with credentials of the backend proxy e.g. ************************************* or socks5://Mylogin:Mypassword@Server:1080\nip_reset is an optional parameter , the URL for triggering IP rotation of the backend proxy\n\nClick SAVE\n\nThen either wait 5 minutes or run the command sudo proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n3. Proxy credentials for new modems\n\nWhen adding new modems, please use\n\nunique HTTP ports from 8001 to 8999,\nunique SOCKS ports from 5001 to 5999.\nunique nicknames like dongleXXX or whatever else. Don’t use nicknames like randomXXX, that are assigned automatically.\n4. Where is WebApp\n\nOne of\n\nhttp://localhost:8080/\nhttp://LAN_IP:8080/\nhttp://VPS_IP:8080/\n\nBy default login/password are proxy / proxy.\n\n5. How to use proxies\nIf proxy ports are forwarded via remote cloud VPS: then the proxies can be used from all over the Internet, by that VPS IP and proxy port numbers.\nFrom the same LAN where multimodem server is located: by the server’s LAN IP and proxy port numbers.\n6. del\n\ndel\n\n7. Reconfigure all modems & proxies.\n\nMethod1. Click the button “Reset Complete” on the main screen of the WebApp in the bottom.\n\nMethod2. In linux console, run: proxysmart.sh reset_complete\n\nAlso it is done after reboot automatically by a Cron job.\n\n8. How to change proxy credentials for a modem. How to rename a modem.\n\nWebApp method\n\nclick EDIT on a modem, set new port or password or nickname for a modem\nclick APPLY\n9. Reset (change) IP on a modem.\n\nThe options are below.\n\nFrom Web App\n\nClick Reset Ip button.\n\nFrom command line.\n\nRun: proxysmart.sh reset_quick_nick dongle1\n\nWhere dongle1 is a Dongle “nickname” that is seen from output of proxysmart.sh show_status\n\nFrom Web API.\n\ncheck WEB API section of this manual.\n\nHow to rotate a modem periodically?\n\nWebApp method\n\nUpdate modem’s settings in the WebApp and click APPLY.\n\nCron method\n\nInstall a Cron job. Edit a file /etc/cron.d/proxysmart, add a line ( or uncomment a commented line.. )\n\n*/10 * * * * root run-one /usr/local/bin/proxysmart.sh reset_quick_nick dongle3\n\nso that a modem with the Nickname dongle3 is rotated every 10 min.\n\nRepeat for each modem you want to rotate periodically.\n\n10. How many modems can I run on a single computer?\n\nHi , technically it depends on how powerful this PC is, and how intensively proxies are used.\n\nRaspberry PI - 4 proxies (roughly)\na miniPC (Intel NUC or similar) - up to 10\na Laptop like Core i5 - up to 30.\n\nAlso it depends on what Plan you buy.\n\nAlso it depends on USB configuration, for maximum number of modems:\n\ndisable USB3.0 in BIOS\nuse USB2.0 hubs\n11. How to set TTL and why?\n\nIn some cases custom TTL must be set in order to have Cell Operator think we are not using the modem in hotsport  tethering mode. I.e. we don’t share its data. By default Linux OS has ttl = 64. To change Cell Operator perception of the situation, we want to set it +1 i.e. 65.\n\nEdit WebApp→GlobalSettings and set CUSTOM_TTL_SET=1 and CUSTOM_TTL_VALUE=65 and regenerate settings.\n\n12. How to set MTU and why?\n\nIn some cases different MTU values connect with different types of ISP’s. You may want to change it.\n\nMtu can be only lowered. E.g. if you have MTU 1390, you can set 1340. Not opposite.\n\n- Edit /etc/proxysmart/conf.txt and set CUSTOM_MTU_SET=1 . - Set MTU in the WebApp for each modem.\n\n13. How to set extra settings for a proxy port.\n\nThose are optional and are set in the WebApp\n\nWHITELIST - allowed customers IP’s who are not required to type in proxy password (IP-based auth).\nbandwidth (speed) limit. Values are in mbps (megabits per second).\nDENIED_SITES_ENABLE (on/off) and DENIED_SITES_LIST (list of blocked sites patterns).\nBandwidth Quota (Megabytes) and Bandwidth Quota Type (daily/monthly/lifetime)\n14. How can I access the web interface admin panel of each modem?\n\nOpen WebApp. Locate the modem. Configure a proxy on your desktop browser.\n\nUse proxy login & password as desribed below (14.1 chapter).\n\nVisit modem IP via that proxy.\n\n14.1. How can I prevent access to modems web interface via proxy?\n\nSince 2023-09-10 it is enabled by default.\n\nEdit WebApp→GlobalSettings and set\n\nPROXY_ADMIN_ENABLE=1\nPROXY_ADMIN_LOGIN=SuperAdmin\nPROXY_ADMIN_PASS=Hqmz81mmZr\n\nAnd regenerate configs. So only admin user is allowed to use modems web interfaces, and normal proxy users are not.\n\n15. How to set monthly traffic quota per modem?\n\nIn the WebApp, set monthly traffic quota. Click EDIT & APPLY.\n\n16. How to make my proxes Open (i.e. not requiring authentication )\n\nSet OPEN_PROXIES=1 in WebApp→GlobalSettings and regenerate all configs.\n\nNote, when proxy ports are forrwarded via a VPS, the proxies are available to any internet user. Use it with caution.\n\n17. Get monthly/daily proxy usage.\n\nClick bandwitdh stats in the WebApp, or run proxysmart.sh bandwidth_report_json portIDXXX, you will see these columns:\n\n“bandwidth_bytes_day_in”\n“bandwidth_bytes_day_out”\n“bandwidth_bytes_month_in”\n“bandwidth_bytes_month_out”\n“bandwidth_bytes_yesterday_in”\n“bandwidth_bytes_yesterday_out”\n18. How to get current number of connections for a modem?\n\nRun a command\n\nss -o state established | grep -c :8038\n\nBut change 8038 with HTTP port of a desired proxy\n\n19. How to read SMS from a modem.\n\nYou have these options.\n\nClick Read SMS in the WebApp\nrun proxysmart.sh list_sms_for_a_modem_by_imei_json 999999999999999 i.e. IMEI of required modem.\nBrowse to the modem IP ( it is shown as GW in proxysmart.sh show_status ) through the proxy. Click SMS button.\n20. How to change WebApp password\n\nBy default it is set to proxy / proxy.\n\nIn the WebApp→GlobalSettings scroll to the bottom, set new WebApp password. NOTE: login remains proxy.\n\nCommand line method.\n\nsudo htpasswd -b /etc/nginx/htpasswd proxy NewAweSomePassword999999\n\nIf you want to change username as well, just delete the file and then assign new password\n\nsudo rm /etc/nginx/htpasswd\nsudo htpasswd -b -c /etc/nginx/htpasswd MyNewUsername NewAweSomePassword999999\n21. OS Spoofing\n\nOs Spoofing is used to simulate other OS TCP fingerprints.\n\nWhat OS can I spoof?\n\nMacOSX, iOS,  Windows,  Android.\n\nHow to enable OS Spoofing?\n\nIn the WebApp set the needed OS per each proxy port (click EDIT PORT).\n\nHow to test OS Spoofing ?\n\nVisit one of these websites (IP checkers) through a proxy. Find something like “OS TCP fingerprints”.\n\nhttp://witch.valdikss.org.ru/\nhttps://thesafety.us/\nhttps://whoer.net → extended results\nhttps://browserleaks.com/ip\n\nCan I dump OS TCP fingerprint from a real device and use it?\n\nYes, contact me.\n\nI enabled OS TCP spoofing, but it is not working!\n\nThe reason may be that the operator passes all traffic through its internal proxy, or in other way modifies TCP signatures. Then local OS TCP modifications are overwritten. Is it bad? No! Because still traffic looks natural as it was coming from this operator network.\n\nTry other operator.\n\n22. Performance tuning\n\nWhen >10 modems are added, and when modem list is generated slowly, play with MAX_PARALLEL_WORKERS_STATUS variable, on faster CPU’s it can be set to 8 or 16.\n\n23. How to lock network mode per modem\n\nSet TARGET_MODE in its settings in the Proxysmart WebApp. Allowed values:\n\nauto\n3g\n4g\n24. What if a modem connected via 3G or 2G, and I want 4G?\n\nRotate its IP.\n\n25. I want to add extra users on a modem\n\nIn the WebApp, create more ports on the modem, each port means a dedicated proxy.\n\n26. Is IPV6 supported?\n\nYes but it’s off by default.\n\nOn modems , edit APN and set APN type for both IPv4 and IPv6 , e.g. Ip4Ip6 or Ip4+ip6, there is a dropdown list for that.\n\nOn Proxysmart box: Update WebApp→GlobalSettings → IPV6_SUPPORT On\n\nand reset configuration proxysmart.sh reset_complete ; or even better do a reboot.\n\n27. Nagios integration.\n\nThere is a plugin embedded, run it as root,\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh IMEI\n\nor\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh NICKNAME\n\nso it will return OK/WARN/CRIT/UNKNOWN and corresponding exit code.\n\n28. IP rotation links.\n\nThese links\n\nCan be safely passed to your customers. They don’t reveal real dongle parameters like IMEI or Nickname.\nThey don’t require HTTP basic authentication\nThey have limited lifetime , it is set in WebApp→GlobalSettings as RESET_LINK_VALIDITY variable, (default value : 5years).\nThey depend on the proxy password. So, when you change the proxy password - old IP rotation links, associated with that proxy, will stop working.\n\nA link can be copied from the WebApp→Ports list. Each Port has its own IP rotation link. If one port rotates IP, then other ports of the same modem affected too.\n\nIf you realized you gave a link to a customer, and want to revoke it, just set new password for the proxy.\n\nIf you want to invalidate all links of all modems, set a new secret: set RESET_LINK_SECRET in WebApp→GlobalSettings .\n\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n\nIt is needed for proper work of HTTP/3.0 which uses UDP.\n\nQUIC (UDP over socks5) will work either in your LAN or via a VPS. Steps are below.\n\nSteps on VPS :\n\nMake sure you finished the Cloud VPS setup part, with Ansible\n\ncd /root/proxysmart-vps/\nnano vars.txt\n\n- set vps_socks5_udp: 1\n\nSave the file (press Control O) and exit the editor (Control x)\n\nRun Ansible again\n\nansible-playbook ./proxysmart-vps.yml\nSteps on Proxysmart server :\n\nset in WebApp->GlobalSettings → QUIC_SUPPORT : On.\n\nand reboot or reconfigure all proxies (run proxysmart.sh reset_complete ).\n\nNote: make sure the VPS has enough RAM, each proxy needs 50MB of RAM. Also add swap if needed.\n\n30. “Dirty” IP reset.\n\nIt may be needed when you need even faster IP reset. In this case, post-checks are not made, so it is not sure if the modem really went online after IP reset. It can be activated by DIRTY_IP_ROTATION=1 in WebApp→GlobalSettings\n\n31. Exclude some modems\n\nIn /etc/proxysmart/conf.txt\n\nby Device name, populate this array IGNORED_DEV=( modem132 modem0000000002) – array of Network Interfaces that are not processed\nby IMEI, populate this array IGNORED_IMEI=( 9999999999999999 8888888888888888 ) – array of IMEI that are not processed\n32. Use custom Speedtest server.\n\nIt is useful when for some reason you want to run speed tests towards a custom server, instead of Ookla servers. So set up a Apache web server with a large file (500MB) and get 2 URL’s, one will test download and 2nd will test upload. The latter must accept large POST data.\n\nThe commands to setup a server part\n\napt install apache2\ndd if=/dev/urandom  of=/var/www/html/file.bin bs=1M count=500\n\nUpdate WebApp→Global_settings with IP of the WEB server:\n\nSPEEDTEST_CUSTOM=1  \nDL_URL=http://v.v.v.v/file.bin\nUL_URL=http://v.v.v.v/i.php\n\nWhere v.v.v.v is VPS IP.\n\nDL_URL can be an URL of a large enough file (~100Mb+). And UL_URL is an URL that accepts large enough POST request.\n\n33. Minimum time between IP rotations\n\nIf you want to avoid too frequent IP rotations triggered by your users – set MINIMUM_TIME_BETWEEN_ROTATIONS=120 e.g. for 120 seconds minimum delay in WebApp→Global_settings .\n\n34. How to block domains\n\nIndividual (per proxy) block lists\n\nCheck (enable) DENIED_SITES_ENABLE in the WebApp\nDENIED_SITES_LIST is a list of domains that will be blocked, both HTTP and HTTPS, plus their subdomains. E.g. if you list porn.com, then also www1.porn.com,www.porn.com,porn.com are blocked.\n\nGlobal block list - for all proxies\n\nin WebApp→Global_settings set DENIED_SITES_ENABLE and paste domains or IP's into DENIED_SITES_LIST , click SAVE and re-apply all modems settings.\n\nNote for Socks5 proxies\n\nWhen a domain blacklist is imposed, then by default users still can access blocked sites by their IP’s.\n\nIn order to prevent it, set DENY_IP_REQUESTS=1 in WebApp→Global_settings and run proxysmart.sh reset_complete for resetting all configuration (or reboot).\n\n35. How to allow only whitelisted domains.\n\nThe feaure it not ready.\n\n36. How to re-rotate IP when IP doesn’t change?\n\nIn WebApp→Global_settings set RETRY_IP_ROTATIONS=1 .\n\nSo when Old_IP == New_IP, then IP rotation is retried. Up to MAX_RETRY_IP_ROTATIONS attempts which is by default 3.\n\n37. Prevent non-unique IP’s after IP rotation.\n\nFor example to prevent using IP’s that were in use 1 time (or more) within last 24h: set in WebApp→Global_settings :\n\nRETRY_IP_ROTATIONS=1                 # enables Re-rotation\nNON_UNIQUE_IP_OCCURS=\"1\"             # how many times an IP must occur to be considered NonUnique. E.g. 1\nNON_UNIQUE_IP_PERIOD=\"24hour\"        # during which period an IP must occur to be considered NonUnique. E.g. 1day or 1hour\n38. How to forward proxy ports using HAproxy?\n\nWhy? In order to enable client IP whitelisting, i.e. 3proxy on proxysmart server will see original client IP and will be able to use whitelising.\n\nSteps:\n\n1. On Proxysmart server\n\nset PROXY_PORTS_FORWARDER_SOFTWARE=ssh+haproxy in WebApp→Global_settings\nrun proxysmart.sh reset_complete for resetting all configuration.\n\n2. On the VPS\n\ncd /root/proxysmart-vps/\nnano vars.txt\n\n- set haproxy_enabled: 1\n\nSave the file (press Control O) and exit the editor (Control x)\n\nRun Ansible again\n\nansible-playbook ./proxysmart-vps.yml\n\n3. Post check\n\nTest a proxy via VPS IP and you will original client IP in 3proxy logs.\n\n39. Custom DNS server for the proxies\n\nEdit /etc/proxysmart/conf.txt and set DNS_SERVER_PROXIES=“*******” where `*******` is a custom DNS server, it must be publicly available.\n\nClick the button “Reset Complete” on the main screen of the WebApp in the bottom or in the console, run: sudo proxysmart.sh reset_complete or reboot the server.\n\n40. Where are proxy logs.\n\nOn the Proxysmart server in a folder /var/log/3proxy/ , each filename is named for HTTP proxy port.\n\nLogs are rotated daily and 90 copies are saved, details are in /etc/logrotate.d/3proxy.\n\nLogs of IP rotations are in a folder /var/log/proxysmart/dongle_rotations/.\n\n41. No logs policy\n\nIf you want to run NoLogs policy, create a cron script that deletes the logs, i.e. the files\n\n/var/log/gost/*\n/var/log/3proxy/*\n/var/log/sniproxy*\n/var/log/haproxy*\n42. My proxies are slow.\n\nAssume a chain UsbModem→PC→VPS→ProxyUser. Final Proxy speed is limited by:\n\nDownload speed of the modem\nUpload speed from PC to VPS\nDownload speed from VPS to the ProxyUser\nDownload speed of the modem.\n\nIt can be measured on the side of the PC e.g. in the Proxysmart WebApp by clicking the Speedtest button.\n\nHow to improve it?\n\ntry other carriers\ntry other modems\ntry better location with better signal (i.e. not your Home)\nUpload speed from PC to VPS.\n\nNormally it correlates with quality of home internet (Fiber/xDSL) and can be measured by running speedtest on the PC in browser or in Terminal (speedtest-cli). Upload value has to be high.\n\nWith different types of port forwardings:\n\nwan (Home Internet is used for ports forwarding) : remote proxy user's DownloadSpeed is limited to minimum of (ModemDownloadSpeed, HomeInternetUploadSpeed )\n\ncell (each modem forwards its proxies through its internet) : remote proxy user's DownloadSpeed is limited to minimum of (ModemDownloadSpeed, ModemUploadSpeed )\n\nHow to improve it?\n\nget a better home internet with better upload\nswitch from WiFi to Ethernet\nDownload speed from VPS to the ProxyUser\n\nIt can be measured by downloading a file from VPS to the Proxyuser.\n\nHow to improve it?\n\nChange location of the VPS to a Cloud Hoster that has better reachability to the clients from all over the world\n43. My proxies are slower than the same SIM card in a Phone.\n\nReason 1: Compare LTE category of the modem and the phone. Phone has higher LTE cat e.g. 12..20, while modem has LTE cat 4..6 (depends).\n\nReason 2: when the speed is really bad (about 1mbps) then it is Operator's throttling. Perhaps you bought a plan that allows only phones/tablets and doesn't allow modems.\n\n44. How to forward proxy ports via each modem individually?\n\nWhy is it needed? When home base internet is unstable or its upload speed <15mbps.\n\nA VPS is needed in order to expose the ports this way ( see VPS integration chapter ).\n\nHow it works\n\nEach proxy forwards its port through its modem, not using base internet.\n\nPRO's :\n\nHome base internet speed & stability is not important\n\nCON's :\n\neach modem is working in bidirectional mode\nproxy speed is limited to 4G Upload speed which is slow\n\nSteps: on Proxysmart server\n\nset PROXY_PORTS_FORWARDER_TYPE=cell in WebApp→Global_settings\nrun proxysmart.sh reset_complete for resetting all configuration.\n45. Auto-rebooting modems.\n\nSometimes only a reboot can fix a modem. In order to enable, set AUTOREBOOT_DONGLES=1 in WebApp→Global_settings. How it works:\n\nif a situation occurs , “reboot score” of a modem is increased by the value, according to the situation:\nSCORE_IP_ROTATION_FAIL=10                   # score increments when IP rotation failed\nSCORE_IP_NOT_DETECTED=2                     # score increments when IP not detected\nSCORE_IP_RECONNECT_FAIL=10                  # score increments when IP not auto-reconnected\nSCORE_WWAN_DATA_FAIL=10                     # score increments when WWAN device can't establish Data connection\nSCORE_WEBAPP_FAIL=20                        # score increments when the modem's WebApp is stuck\nwhen the modem’s reboot score reaches MAX_REBOOT_SCORE then the modem is rebooted.\nspecial case, do USB reset instead of a reboot, when AUTO_USB_RESET_DONGLES is 1, it is useful when modems’ WEB APP is not available.\n46. My proxy is offline and showing Red in the WebApp.\nCheck if the modem has good signal.\nCheck if the modem has correct APN (set in its Web Dashboard).\nCheck if its SIM card is active (not blocked on Operator side) and is topped up.\nCheck the modem on another PC (e.g. your own desktop).\n47. del\n48. IP's are rotated on their own\n\nIf you don't rotate IP's and they are detected each time as a new IP - it is natural behaviour of mobile provider, when it routes its clients through random different gateways every 1 minute or so. T-Mobile USA is known of doing so.\n\n49. Install logging of all requests in single place\n\nWhy? Get single log of all requests from Proxies (HTTP/Socks5) clients and VPN clients.\n\nInstallation On Proxysmart server\n\nIn the WebApp→GlobalSettings set SNIFFER_ENABLED=1 and click Apply.\n\nrun proxysmart.sh reset_complete\n\nWatch the log /var/log/proxy_log.log on Proxysmart server.\n\nIt is rotated and 365 daily copies are stored on disk.\n\nThen it is bound to a button “Download Proxy Logs”.\n\nIt can also be installed on a VPS if the VPS is working as proxies frontend.\n\nInstallation On VPS\n\nnot supported yet.\n\nLog format\n\nFile: /var/log/proxy_log.log\n\n    _ws.col.Time  frame.interface_name   ip.src  tcp.srcport   ip.dst   tcp.dstport  \n    #   1          2                        3       4           5           6\n    \n    socks.remote_name    socks.dst    socks.port   socks.dstport \n    # 7                         8         9         10\n    \n     http.request.method    http.host  \n    #   11                  12        \n\n     tls.handshake.extensions_server_name  x509ce.dNSName\n    #   13                                  14\n50. PPP modems\n\nThese are very old 3g modems like Huawei E303, E173, E156; ZTE MF110, MF193, MF190. In order to make them work with proxysmart,\n\nedit WebApp→GlobalSettings and set PPP_MODEMS_ENABLE=1 .\n\nMake Quectel / Sierra Wireless LTE modules work in PPP mode\n\nWhy? sometimes they fail working in QMI mode. So:\n\nedit WebApp→GlobalSettings and set PPP_MODEMS_ENABLE=1\nplace a file /etc/udev/rules.d/21-wwan.rules\n# ignore QMI_WWAN endpoints on Quectel, to make it work in PPP mode.\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"2c7c\" , ATTRS{idProduct}==\"0125\",  ENV{.LOCAL_ifNum}==\"04\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\n# ignore QMI_WWAN endpoints on SierraWireless  , to make it work in PPP mode. Save to 21-wwan.rules:\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"413c\" , ATTRS{idProduct}==\"81b6\",  ENV{.LOCAL_ifNum}==\"08\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\nre-plug modems or reboot Proxysmart server\n51. Alerts to Telegram\n\nIn Telegram start a chat with a bot https://t.me/userinfobot and get your Telegram numeric ID.\n\nIn Proxysmart WebApp→GlobalSettings , set TG_ALERTS_ENABLE ; and set TG_ALERTS_RECEIVER to your Telegram numeric ID.\n\nIn Telegram start a chat with Proxysmart bot https://t.me/nagios737bot and send 'hi'.\n\nAfter that the bot will send you alerts.\n\n2. Project description\n1. architecture\nonsite: box with Ubuntu, USB hub and modems\nremote: VPS with proxy ports (optional)\n2. Online services are used:\nhttp://ip.tanatos.org/ip.php which is simple PHP script that returns visitor’s IP. It is used to detect whether a modem is really online. Can be replaced with one of https://ifconfig.co or similar, but I was not happy with their reliabiality, they are down sometimes. The URL is defined in WebApp→Global_settings\nhttp://witch.valdikss.org.ru/ : used for detecting p0f and MTU\n3. CLI API\n1. show status\n\nShow full status of all modems, table (slower).\n\n# proxysmart.sh  show_status \n\nOutput:\n\nShow brief status of all modems, table, (faster)\n\nRun\n\n# proxysmart.sh  show_status_brief\n\nOutput:\n\nShow full status of all modems , json\n\n# proxysmart.sh  show_status_json \n\nOutput:\n\nShow status for a single modem, JSON\n\nArguements - NICK or IMEI.\n\n# proxysmart.sh  show_single_status_json dongle111 \n\nOutput:\n\n2. full reconfiguration\n\nRun\n\n# proxysmart.sh reset_complete  \n\nOutput:\n\n3. apply setting for a modem by IMEI\n\nJSON output\n\n# proxysmart.sh   apply_settings_for_a_modem_by_imei  868723029999406 \n\nOutput:\n\nPlain text output:\n\n proxysmart.sh  apply_settings_for_a_modem_by_imei_raw    359999999999999 \n\noutput:\n\n4. reset IP on a modem\n\nArgs: IMEI or NICKNAME.\n\nJSON output:\n\n# proxysmart.sh   reset_modem_by_imei    899999999999999 \n# proxysmart.sh   reset_modem_by_imei    Dongle222\n\nOutput:\n\nPlain text output:\n\n# proxysmart.sh  reset_quick_nick  899999999999999\n# proxysmart.sh  reset_quick_nick  Dongle222\n\nOutput:\n\n5. reboot a modem\n\nArgs: Nickname or IMEI.\n\nTEXT Output\n\nJSON Output\n\n6.1. Reset a modem via USB\n\nCan accept DEV name, IMEI or Nickname. So\n\nFor Text output:\n\nFor Json output.\n\n6. Run speedtest\n\nOn a single modem:\n\nArgs: NICKNAME or IMEI.\n\n# proxysmart.sh  speedtest 353990074160000\n# proxysmart.sh  speedtest sierra\n\nResponse:\n\n7. report bandwitdh\n\nOn a single port\n\nWith arbitrary time interval.\n\n8. reset bandwidth counter on a port\n\nARGS: portID\n\nJSON output\n\n9. list sms on a modem\n\nJSON output\n\n10. send sms\n\nPlain output:\n\nJSON output:\n\n11. purge SMS\n\nPurges SMS from all folders.\n\nCall by IMEI or nickname,\n\njson output:\n\n12. send ussd\n\nPlain output\n\nJSON output:\n\n13. get bandwidth counters from a modem\n\n..use bandwidth stats..\n\n14. Get IP rotations log for a modem\n\nBy Nickname or IMEI\n\n15. Get Top hosts from a modem\n\nBy Nickname or IMEI\n\n16. Report IP uniqueness\n\nJSON output.\n\nTEXT output.\n\n4. WEB API\n1. Web API description.\n\nWEB API endpoint is the URL that Proxysmart WebApp available at.\n\nIt can be\n\nLAN_IP:8080 when you call it from the same LAN\nVPS_IP:8080 when you forwardded ports to the Cloud VPS\nSTATIC_IP:8080 when you forwarded ports via your LAN router and your ISP gave you STATIC_IP\n\nAlso attach proper username:password (the -u parameter).\n\nWhenever below you are seeing localhost:8080, replace it with the actual WEB API endpoint.\n\n2. List all modems ( full status, slow)\n\nRequest:\n\ncurl 'http://localhost:8080/apix/show_status_json' -u proxy:proxy \n\nResponse:\n\n3. List all modems ( brief status, fast )\n\nRequest:\n\ncurl localhost:8080/apix/show_status_brief_json -u proxy:proxy\n\nResponse:\n\n3.1. List all active ports\n\nRequest:\n\ncurl http://localhost:8080/apix/list_ports_json -u proxy:proxy\n\nResponse:\n\n4. Single modem status\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/show_single_status_json?arg=dongle111    -u proxy:proxy\ncurl http://localhost:8080/apix/show_single_status_json?arg=899999999999999    -u proxy:proxy\n\nResponse:\n\n5. Reset (change) IP on a modem.\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reset_modem_by_imei?IMEI=899999999999999 -u proxy:proxy\ncurl http://localhost:8080/apix/reset_modem_by_nick?NICK=dongle22 -u proxy:proxy\n\nResponse:\n\n6. Reboot a modem\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reboot_modem_by_imei -d IMEI=860493043888886 -u proxy:proxy\ncurl http://localhost:8080/apix/reboot_modem_by_nick -d NICK=dongle2 -u proxy:proxy\n\nResponse:\n\nETA: ~ 1.5 minute\n\n7. Send SMS\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-sms' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' \\\n    --data-urlencode 'phone=+11111111111' \\\n    --data-urlencode \"sms=txt txt fff\"\n\nResponse:\n\n8. Send USSD and read response\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-ussd' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' --data-urlencode 'ussd=*100#'\n\nResponse:\n\n9. Read SMS from a modem\n\nRequest:\n\ncurl 'http://localhost:8080/modem/sms/862329888888888?json=1' -u proxy:proxy\n\nResponse:\n\n10. Read bandwidth stats from a port\n\nArgs: porID\n\nRequest:\n\ncurl localhost:8080/apix/bandwidth_report_json?arg=portJFJHFHJ -u proxy:proxy\n\nResponse:\n\nWith arbitrary time interval:\n\nARGS: portID, start time, end time.\n\nRequest:\n\ncurl -G http://localhost:8080/apix/get_counters_port -X GET -d PORTID=portKFJKJKDD --data-urlencode 'START=2023-01-28 18:10' --data-urlencode 'END=2023-01-28 19:20:01' -u proxy:proxy \n\nResponse:\n\n11. del\n\ndel\n\n12. Reset bandwidth stats for a port\n\nRequest (by portID ):\n\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=portJKJKDHJ83  -u proxy:proxy\n\nResponse:\n\n{\"result\":\"success\",\"debug\":null}\n13. Reset a modem via USB\n\nRequest either - by network interface e.g. modem77 - by Nickname - by IMEI\n\ncurl localhost:8080/apix/usb_reset_modem_json?arg=modem77      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=dongle22      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n14. Get IP rotations log for a modem\n\nRequest - by Nickname - by IMEI\n\ncurl localhost:8080/apix/get_rotation_log?arg=899999999999999  -u proxy:proxy \ncurl localhost:8080/apix/get_rotation_log?arg=dongle2          -u proxy:proxy \n\nResponse:\n\n15. Apply settings for a modem\n\nRequest:\n\ncurl http://localhost:8080/modem/settings -d imei=862329099999999 -u proxy:proxy\n\nResponse:\n\n15.1. Apply settings for a port\n\nArgs: portID\n\nRequest:\n\ncurl http://localhost:8080/apix/apply_port?arg=port029348 -u proxy:proxy\n\nResponse:\n\n15.2. Purge a port\nit deletes the port object from the DB\nit stops its proxies\n\nArgs: portID\n\nRequest:\n\ncurl http://localhost:8080/apix/purge_port?arg=port029348 -u proxy:proxy\n\nResponse:\n\n16. Purge SMS from a modem\n\nRequest either - by Nickname - by IMEI\n\ncurl localhost:8080/apix/purge_sms_json?arg=Nick77      -u proxy:proxy\ncurl localhost:8080/apix/purge_sms_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n{ \"result\": \"success\", \"msg\": \"\" }\n17. Get Top hosts from a modem\n\nRequest:\n\n18. Report IP uniquness\n\nRequest:\n\n19. Store a modem object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a modem”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n20. Store a port object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a port”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n21. Export backup\n\nDestination format: v2\n\nSo it can be later imported in V2 version of Proxysmart.\n\nRequest:\n\n5. Mongodb integration\n5.1. Schema\n\nMongodb contains 2 collections: modems and ports.\n\n5.1.1. Modems\n\nIt contains real modems.\n\nArray of elements, 1 element = 1 modem.\n\n1st element contains only mandatory keys\n2nd element contains all possible keys\n\nExample\n\nNotes:\n\nTARGET_MODE - the mode (3g/4g/auto/default) the mode will work in.\n\n5.1.2. Ports\n\nIt contains proxy ports given to the users. Each port is connected to a modem by the IMEI key. So you can attach 1 or more ports to a modem.\n\nArray of elements, 1 element = 1 port.\n\n1st element contains only mandatory keys\n2nd element contains all possible keys\n\nExample\n\nNotes:\n\nbw_quota : bandwidth quota in MB\nQUOTA_TYPE can be daily/monthly/lifetime. Latter means you allocate the quota forever till it expires.\nIP_MODE: can be :\n4 : ipv4 only\n6 : ipv6 only\n46 : prefer ipv4 but also allow ipv6\n64 : prefer ipv6 but also allow ipv4\nnull : leave default\nPROXY_VALID_BEFORE: expiry of a port\nbandlimin: download speed (megabits per second)\nbandlimout: upload speed (megabits per second)\nOS - spoofed destination OS, can be\n(empty or absent field) No spoofing\n“android:1” Android, p0f compliant but slow\n“android:3” real Android, almost like Linux\n“macosx:3” macosx:3\n“macosx:4” real MacOSX 12.6 / iPhone 13 Pro Max\n“ios:1” ios:1, p0f compliant\n“ios:2” ios:2, real Iphone\n“windows:1” real Windows 10\n5.2 Workflow\n\nquick start:\n\nPopulate Modems collection with modems\nPopulate Ports collection with ports\nfor each added modem call 'Apply settings for a modem' WEB API call. It will configure each modem and its ports\n\nif you edited a modem\n\ncall 'Apply settings for a modem' WEB API call for the modem.\n\nif you edited a port\n\ncall 'Apply settings for a port' WEB API call for the port (faster)\n\nor\n\ncall 'Apply settings for a modem' WEB API call for the modem. (slower, affects all modem's ports)\n\nif you deleted a port\n\ncall 'Purge port' WEB API call for the port (faster)\n\nor\n\ncall 'Apply settings for a modem' WEB API call for the modem. (slower, affects all modem's ports))\n\n5.3 Configuration\n\nMongoDB URI is defined in /etc/proxysmart/conf.txt :\n\nMONGODB_URI=\"********************************************************************************************\"\n\nIf you want to use other Mongodb collection names instead of modems and ports , define them in /etc/proxysmart/conf.txt :\n\nMONGODB_MODEMS_COLLECTION=modemsNEW\nMONGODB_PORTS_COLLECTION=portsNEW\n\nafter changes:\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n5.4 Moving Mongodb to other server\n\nSometimes you want to move Mongodb to a cloud server.\n\nIn order to do so\n\nkeep collection name modems\nif your new mongodb is Mongodb 5+ and doesn’t have backward compatibility with the older clients, upgrade Mongodb Client to 5th version. Run on the Proxysmart box:\nsudo -i\napt purge mongo\\* -y\n. /etc/os-release \nrm -f /etc/apt/sources.list.d/mongodb*\ncurl -L https://www.mongodb.org/static/pgp/server-5.0.asc | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/mongodb-5.0.gpg\necho \"deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu $VERSION_CODENAME/mongodb-org/5.0 multiverse\" | tee /etc/apt/sources.list.d/mongodb-org-5.0.list \napt-get update\napt install mongodb-mongosh mongodb-database-tools -y\nln -sf /usr/bin/mongosh /usr/local/bin/mongo\nupdate MONGODB_URI to new Mongodb URI in /etc/proxysmart/conf.txt\nif your new mongodb URI has +srv extension , install a PIP module: /var/www/proxysmart/venv/bin/pip install \"pymongo[srv]\"\ntest new Mongodb URI (I assume you updated MONGODB_URI variable in conf.txt above):\n    . /etc/proxysmart/conf.txt;\n    mongoexport --quiet --uri=\"$MONGODB_URI\" -c modems --forceTableScan\n\nit should return array of all elements in the modems collection\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n6. Installation\n1. Initial installation\n\nInstall a fresh OS.\n\nSupported OS and architectures:\n\nUbuntu 22.04, 20.04 on amd64, arm64.\nDebian 11 or Raspberry PI OS (ex-Raspbian) on amd64, arm64, armhf ( see Raspberry PI OS Notes below).\nRaspberry PI : https://ubuntu.com/download/raspberry-pi , choose Ubuntu Server 22.04 64bit\nNormal PC/laptop: Choose Server or Desktop, https://ubuntu.com/download, choose Ubuntu 22.04\n\nArmhf (arm 32 bit) doesn’t have Mongodb support!\n\nThose steps will take 5..10 minutes.\n\nUnplug any 4g modems.\n\nAdd an APT repo.\n\nsudo -i\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update\napt install proxysmart\n\nThen follow instructions: It will tell what to do next ( run these ).\n\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\nAfter that either enjoy the Demo version at http://localhost:8080 or check License section.\n\nRockpi Notes\n\nIf LOGRAM is enabled ( a folder /var/log.hdd exists). Disable logging:\n\nmongodb, edit /etc/mongodb.conf, comment logpath directive.\n\nRaspberry PI OS (ex-Raspbian) Notes\n\nits kernel doesn't have xt_cgroup module , so you have to rebuild its kernel and include this module. It is recommended to switch to Ubuntu instead.\n\nDevelopment version installation\n\nWhy? To unlock new features that are not yet in the Main version.\n\nsudo -i\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2.dev $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update \napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\n2. Upgrade\n2.1. Upgrade from older V2\n\nI.e. minor upgrade.\n\nRun these commands:\n\nNOTE when dpkg will ask whether to replace old config file with new one, answer N (No) or just press Enter.\n\nSo old config file is saved.\n\nsudo -i\napt update \napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\n2.2 Upgrade from V1\n\nI.e. major upgrade V1>V2.\n\nIn V1, go to WebApp → “Edit modems” and and download Backup file (Export backup for V2).\nThen run\nsudo -i\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update\napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\nOpen the webapp, import the file you downloaded\nReboot or run proxysmart.sh reset_complete.\nin the webapp→Global settings, revisit all settings and set them per your needs. It is replacement for older conf.txt.\n3. Post Installation\n\nPlug in all 4g modems you have, wait ~20 sec to let them initialize.\n\nNow test if ip li shows you any modem* interfaces, otherwise reboot to apply UDEV rules.\n\nIf it does, continue next below. (Otherwise reboot to apply UDEV rules.)\n\nNow you can start all the modems:\n\nYou have to run proxysmart.sh reset_complete or reboot the multi-modem server.\n\nCommand proxysmart.sh show_status will return a table with proxy port, external IP’s.\n\nNavigate to the WebApp ( http://localhost:8080 proxy/proxy) and assign login/password/nicknames/ports to the modems.\n\nTest reboot, reboot the box, wait 1 minute, make sure the WebApp shows the modems.\n\nWebApp\n\nVisit http://your_box_lan_IP_address:8080/ or http://localhost:8080/\n\nDefault user:password pair is proxy:proxy\n\n4. Cloud VPS integration.\n\nWhy? The VPS is needed to forward proxy ports from a cloud VPS IP back to the Proxysmart multi modem server, so proxy ports are available for all users around the world.\n\nDo I need a VPS?\n\nA VPS is NOT needed when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nThe VPS server can be a cheap 1GB DigitalOcean / Linode / Vultr VPS or similar.\n\nIt has to be located as close as possible to the 4g farm server ( for lowest ping ).\n\nVPS setup steps.\nOn Proxysmart multi modem server\n\nGo to the WebApp , copy content of the SSH public key from the bottom of the page. We will refer to it as PUBKEY below.\n\nAlso it is stored on disk as /root/.ssh/fwd.pub\n\nOn VPS\n\nCheck if your VPS has no firewall. Disable it if it has – Both inside Linux OS and in hoster panel.\n\nInstall & run Ansible.\n\napt update && apt install git ansible -y\ncd ~/\ngit clone https://github.com/ezbik/proxysmart-vps.git\ncd proxysmart-vps\n\nedit the file vars.txt\n\nnano vars.txt\n\nInsert the PUBKEY inside square brackets for the ssh_pub_keys list. Save the file (press Control O) and exit the editor (Control x)\n\nRun Ansible:\n\nansible-playbook proxysmart-vps.yml\n\ndone.\n\nOn Proxysmart multi modem server\n\nin WebApp→Global_Settings:\n\nset VPS variable to VPS IP\nset PROXY_PORTS_FORWARDER_ENABLE=1\nPick a port for SSH_REMOTE_PORT, in most cases 6902 is fine. The port (TCP) has to be free on the VPS\nPick a port for WEB_REMOTE_PORT, in most cases 8080 is fine. The port (TCP) has to be free on the VPS\n\nRun proxysmart.sh reset_complete\n\nOn VPS\n\nissue the command ss -tnlp and you will see proxy ports are bound with sshd daemon. That means the ports are forwarded.\n\nOn your private desktop or any other PC\nvisit http://vps_ip:8080 for the WebApp , default login:password is proxy:proxy\nyou can ssh to VPS IP and port 6902, and that goes to the multi-modem-server:22.\nCloud VPS IP change\n\nIf CLoud VPS IP is changed, update it on multi-modem-server side by defining new VPS variable in WebApp→Global_settings and rerun proxysmart.sh reset_complete there (or reboot).\n\n5. Forwarding ports through your own LAN router.\n\nWhy? It is needed to forward proxy ports from a your ISP IP address back to the Proxysmart multi modem server, so proxy ports are available for all users around the world.\n\nIt is suitable when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nSteps\n\nConsult with documentation of your LAN router. Forward these ports from ISP IP address to the LAN IP of proxysmart server:\n\nTCP 8001-8999 for HTTP proxies\nTCP 5001-5999 for SOCKS5 pproxies\nTCP 8080 for the WebApp\nTCP 1194 for Openvpn (if it is working in TCP mode)\nUDP 1194 for Openvpn (if it is working in UDP mode)\n\nNotes\n\nAlso edit settings WebApp→GlobalSettings, replace myrouter.com with your actual Hostname or IP addresss.\n\nSo proxy credentials & links will be shown with your actual Hostname or IP addresss.\n\nPROXY_PORTS_FORWARDER_ENABLE : Off\nREWRITE_WEBAPP_URL : On\nREWRITE_WEBAPP_TO : http://myrouter.com:8080\nREWRITE_HOST_IN_PROXY_CREDS : On\nREWRITE_HOST_IN_PROXY_CREDS_TO : myrouter.com\n\nclick SAVE.\n\n.. so forwarding system ports to a VPS is disabled.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete .\n\n7. License\n1. Demo license\n\nInstallation is shipped with default demo license.\n\nIt allows you to run proxy on 1 modem.\n\nIn order to run more modems, buy a License.\n\n2. Requesting a License\n2.1. Get the machine data\n\nMethod1. From the WebApp:\n\nOpen the proxysmart WebApp at http://localhost:8080 or http://LAN_IP:8080\nScroll down to the Machine Data text.\nCopy MachineData value to the Clipboard.\n\nMethod2. From the CLI:\n\nOpen terminal\nRun sudo proxysmart.sh license_status\nCopy machine_data value\n2.2. Contact Sales Team\n\nSend the copied value to proxysmart.org\n\n2. License installation\n\nYou will be given the license and license signature. Both are sequences of numbers and characters. Then submit both either via WebApp or CLI:\n\nsubmitting via WebApp\n\nOpen the WebApp , http://localhost:8080 , expand License section and type in the keys & submit both.\n\nsubmitting via CLI\n\nrun commands\n\nproxysmart.sh submit_license LICENSE\nproxysmart.sh submit_license_signature LICENSE_SIGNATURE\n3. Restoring Demo license.\n\nIf your paid license expired or broken, restore DEMO license, run:\n\nsudo cp -v /usr/share/doc/proxysmart/examples/license.txt* /etc/proxysmart/\n\n8. Mobile (4G/5G) VPN\n\nTogether with building proxies, it is possible to build Residential VPN.\n\nAssumption is, your proxies are already available via Cloud VPS.\n\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n\nIf ports forwarded through a VPS\n\nSteps on VPS\n\nAssume the VPS is already “integrated” - see VPS integration topic.\n\nPick a free TCP port on the VPS, run ss -tnlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501. We will call it OPENVPN_REMOTE_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1501 , to the free TCP port on Cloud VPS.\nset OPENVPN_INTEGRATION=1 so that Proxysmart will understand Openvpn is in use.\nset OPENVPN_LOCAL_PORT=1194\nClick SAVE\n\nSo VPN client certificates will be generated with these values and VPN clients will connect there ( *******:1501 )\n\nGo to the WebApp main screen and download OpenVPN profiles for each port.\n\n8.1.2. Installation with TCP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded TCP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_SERVER_HOST to $EXT_IP\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/TCP )\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.3. Installation with UDP protocol (through VPS)\n\nSteps on VPS\n\nMake sure you finished the Cloud VPS setup part, with Ansible, so VPS part is done.\n\nSteps on Proxysmart server\n\nPick a free UDP port on the VPS, run ss -unlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501.\n\nWebApp→Global_Settings\nset OPENVPN_SERVER_PORT=1501 , to the free UDP port on Cloud VPS.\nset OPENVPN_INTEGRATION=1 so that Proxysmart will understand Openvpn is in use.\nset VPS_SOCKS5_SERVER to scheme with authentication on VPS e.g. socks5://px:g739Az8JYK@*******:2323\nClick SAVE\n\nSo VPN client certificates will be generated with these values and VPN clients will connect there ( *******:1501 )\n\nGo to the WebApp main screen and download OpenVPN profiles for each port.\n\n8.1.4. Installation with UDP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded UDP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_SERVER_HOST to $EXT_IP\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/UDP )\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.2. Many users with the same profile\n\nBy default only 1 device (PC, mobile, tablet) can use 1 OpenVPN profile. If you want multiple devices use 1 profile, edit /etc/openvpn/server.conf , comment out ;duplicate-cn line by removing the ; character, and run proxysmart.sh reset_complete.\n\n8.3. Mobile VPN, how to connect\n\nSo download the VPN profiles and connect using any VPN client software.\n\nDownload and install software:\n\nWindows: https://openvpn.net/community-downloads/ or https://openvpn.net/client-connect-vpn-for-windows/\n\nMacOS: https://tunnelblick.net/\n\nAndroid: https://play.google.com/store/apps/details?id=de.blinkt.openvpn or https://f-droid.org/en/packages/de.blinkt.openvpn/\n\nIOS: https://apps.apple.com/us/app/openvpn-connect/id590379981\n\nImport downloaded OpenVPN profile, tap Connect.\nuse Login and Password from the corresponding proxy.\n8.4. Mobile VPN, FAQ\n8.4.1. Switch Openvpn protocol\n\nIn WebApp→GlobalSettings set OPENVPN_PROTOCOL to tcp or udp and run proxysmart.sh reset_complete\n\nOn Clients, either download profiles again, or change protocol in client settings.\n\n8.5. Mobile VPN logs\n\nLogs of openvpn sessions - /var/log/openvpn/sessions.log. Format:\n\n'$time','$type','$local_port','$proto','$duration','$bytes_in','$bytes_out','$Real_IP','$Real_PORT','$Ovpn_CERT','$Ovpn_IP','$IMEI','$proxy_login','$auth_reject_why'\ntype - session_start / session_stop / auth_reject\nlocal_port - local port of Openvpn server\nproto - tcp-server or udp\nduration - when type is session_stop, how many the session lasted\nReal_IP, Real_PORT - of a client\nauth_reject_why - when type is session_stop, the reason why auth was rejected\n9. Bugs and Limitations\nLTE modules\nIPV6 is not supported\nAndroid phones\nNo P0f-spoofing with Proxysmart APK app.\nVPN users\nIPV6 is not supported\nPort ranges\nProxy ports range: HTTP 8001..8999, SOCKS5 5001..5999\nOS TCP Fingerprint spoofing\nNot supported on Ipv6\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:changelog ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:changelog", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:changelog\nChangelog\n\n2024-06-28\n\nimproved reboot of stuck Proxidize modems.\n\n2024-06-27\n\nSierraWireless on USA Verizon - improved ( WWAN_MODEMS_MSS_FIX setting)\n\n2024-06-26\n\nadded LAN 4G router modem, Tenda 5G03\n\n2024-06-23\n\nfix - p0f spoofing stopped working after IP rotation.\n\n2024-06-22\n\nQuectel EG91.\n\n2024-06-21\n\nAlcatel (TCL) MW43.\n\n2024-06-17\n\nImproved sniffer proxy logging (memory & disk usage).\n\n2024-06-16\n\nQuectel RM520N-GL - improved SMS reading.\n\n2024-06-13\n\nimproved IP leak prevention.\n\n2024-06-07\n\nZTE MF79 - support TARGET_MODE during IP rotation.\n\n2024-06-06\n\nWebApp - Ports bandwidth stats - show previous month usage.\nWebApp - System status - show overall TCP connections count.\n\n2024-06-05\n\nImproved IP-rotation on IPv4-only modems in IPv6-enabled system.\n\n2024-06-03\n\nBug fixed of Openvpn connections stuck on Ubuntu 20.04.\n\n2024-06-02\n\nImproved Debian 11 and 12 support.\n\n2024-05-28\n\nWebApp>SystemStatus - show main server external IPv4 and IPv6\n\n2024-05-24\n\nGlobal IP uniqueness check (if enabled, checking IP uniqueness across all present modems)\n\n2024-05-24\n\nHuawei stick mode (E303, E3272, E3276), improved APN detection.\n\n2024-05-23\n\nUbuntu 18.04, Debian 10 support dropped\n\n2024-05-21\n\nModems Autoreboot - bug fixed.\n\n2024-05-18\n\nWebApp - show ping latency and packets loss.\n\n2024-05-17\n\nWebApp - a button to download the license (works if the license is issued after 2024-05-17)\n\n2024-05-09\n\nnew p0f signatures - android 14, IPhone 12 max pro, MacOSX 13.5, Windows 11 х64 Desktop\n\n2024-05-06\n\nAndroid phones in raw ADB mode (no extra apps), in particular Motorola Stylus 5G on Android 12\n\n2024-04-29\n\nadded LAN 5G/4G router modem, ZTE MC7010CA\nadded LAN 4G router modem, ZTE MF289\n\n2024-04-27\n\nAnsible playbok for setting up a VPS\n\n2024-04-25\n\nWebApp - new Desktop/Mobile layout\n\n2024-04-19\n\nadded LAN 4G router modem, Tenda 4G03Pro\n\n2024-04-18\n\nadded LAN 5G/4G router modem, ZTE MC801A and ZTE MC8010CA\n\n2024-04-16\n\nadded p0f signature for “Windows 10 64bit Desktop”\n\n2024-04-12\n\nadded LAN 4G router modem, Cudy LT500\n\n2024-04-06\n\nWebApp - expired and overquota ports are shown in pink.\nImproved Ubuntu 24.04 support\n\n2024-04-01\n\nadded new modem - Huawei K5160\n\n2024-03-28\n\nan option to download only last month proxy logs\n\n2024-03-26\n\nLong running tasks are now queued up - Modem Rebooting, USB resetting a modem, downloading proxy logs.\n\n2024-03-25\n\nimproved Tshark memory & disk consumption\n\n2024-03-21\n\nSierraWireless EM7455 now can work in PPP mode\n\n2024-03-02\n\nV2.1 release\nWebApp - improved speed\nWebApp - show next IP rotation time\nWebApp - cached statuses of modems + background statuses updater\nWebApp - choose Openvpn profiles format, default or OpenvpnConnnect3.4+\nWebApp - choose how proxy credentials are shown ( host:port:login:password or proto://login:password@host:port )\nWebApp - ApplySettings replaced with Re-Add Device - which is useful when a device is stuck and settings can't be applied\nWebApp - Link to IP info\noption to use xt-tls iptables module for more reliable domains blocking\ndaily/monthly quotas are automatically unblocked on the next day/month\nImproved support of Socks5 UDP behind DMZ in LAN's\nRefactored Ping graphs (single daemon instead of multiple per-modem processes)\n\n2024-02-27\n\nHuawei K5150 can work in Stick (NCM) mode\n\n2024-02-19\n\nlog Caller of IP rotation (schedule or manual or WebAppClick or ByLink)\n\n2024-02-17\n\nprevent IP rotation by link if proxy is expired or over quota\n\n2024-02-15\n\nSupport of SierraWireless EM7565, EM7511\n\n2024-02-07\n\nshow live Reboot scores in the WebApp\n\n2024-02-05\n\nUSB devices tree and USB errors log, are shown in the WebApp\n\n2024-01-31\n\nImproved Fibocom L860 ip rotation\n\n2024-01-29\n\nMax connections test\n\n2024-01-24\n\nHuawei B535 support\n\n2024-01-23\n\nMTU fix (slow speed fix) on USA Verizone on LTE chips\n\n2024-01-19\n\nWebApp - dynamic search in the table\nWebApp - show failed modems on top\n\n2024-01-18\n\nReplace product name & company name in the WebApp (WhiteLabel'ing)\n\n2024-01-17\n\nAlcatel IK41, improved IP rotation\n\n2024-01-10\n\nCompacted layout of HTML tables\n\n2024-01-08\n\nSierra Wireless EM7455: force set APN even before checking LTE status\n\n2024-01-04\n\nTelegram alerts for expiring proxies\nAlerts to a WebHook\nAlerts are saved in a local log file.\n\n2024-01-01\n\ngraphs for VPN bandwidth\n\n2023-12-30\n\nglobal sites blocklist can be edited in the WebApp\nWebApp improved, true\\false settings are shown as radio buttons\n\n2023-12-27\n\nAlerts to Telegram\n\n2023-12-27\n\nWebApp: adding modems Notes.\n\n2023-12-25\n\nWebApp: mobile signal is shown as colored bars\n\n2023-12-22\n\nsupport of Huawei 5G CPE H112-370 (ip rotation, sms read)\n\n2023-12-21\n\nadded 'modems states' : adding queued→adding→added→deleting , failed_to_add.\n\n2023-12-20\n\nnew WebApp (condensed layout)\n\n2023-12-19\n\nreduced IP rotation time on Huawei (E3372, E8372, E55*) by approx. 4 seconds\n\n2023-12-12\n\nimproved support of Huawei E3372s modems.\nexport/import of LAN modems & other local settings during the Backup/Restore\n\n2023-12-07\n\nbumped version to 2.0.\nAndroid VPN can send UDP.\n\n2023-12-06\n\nAndroid proxies - cover reconnects.\nReboot call in the WebApp opens in new tab.\n\n2023-12-05\n\nAndroid App can read SMS.\n\n2023-12-04\n\nfix Fibcom L860 on long USB id's.\n\n2023-11-30\n\nSIMA7630C (ip rotation, SMS read, reboot)\n\n2023-11-27\n\nAndroid VPN - added.\nAndroid VPN - proper native DNS from the Mobile Carrier\n\n2023-11-25\n\nopenvpn sessions log in a separate file ( $OPENVPN_SESSIONS_LOG )\nussd on Quectel EP06\n\n2023-11-23\n\nsniffer script to log all proxies requests and original clients IP's. Can be installed either on VPS or on Proxysmart server, or both. It generates easy to read & analyze single log of who,when,what.\n\n2023-11-21\n\nsupport of LTE module - T77W595 / LT4120\n\n2023-11-12\n\nbuild for Ubuntu 24.04 Noble\nwarning about license due to expire\n\n2023-11-08\n\nimport & export mongodb – a button in the WebApp\nexport to Proxysmart v2\n\n2023-11-07\n\nmongodb uri can be set in /etc/proxysmart/conf.d/*.inc\n\n2023-11-05\n\nParallel initialization of proxies during reset_complete & reset_gently (enablable with PARALLEL_STARTUP variable)\nfix IP rotation counter logging\nReboot button in the main WebApp for rebooting the server\nAndroid proxies - automatic ports probing and detection.\n\n2023-11-03\n\nOlax U90 - support of a new modem\nnew WEB API call for storing a modem object\nQuectel modems now obey with TARGET_MODE during IP rotation\n\n2023-11-01\n\nAlcatel HH71 autoreconnect\nLAN modems: improved detection; ability to reboot\nfix for adding modems when IMEI is not properly detected\n\n2023-10-25\n\nQuectel EC20 support; Imvproved Chinese letters in SMS\nimproved stability of IK41 (stick mode)\n\n2023-10-23\n\nindividual OS TCP spoofing (per proxy)\n\n2023-10-18\n\nimproved speed on Android proxies over 27mbps\n\n2023-10-13\n\nimproved SMS on IK41 (stick mode)\n\n2023-10-09\n\nPossibility to set Bandwitdh Quota direction, one of (in, inout, out).\n\n2023-10-04\n\nimproved Huawei E3272 support.\n\n2023-10-03\n\nTarget mode of a modem during IP rotation: can be set in the WebApp\nDeleted TARGET_MODE from conf.txt (global variable)\n\n2023-09-28\n\nfixed installation in India where github is blocked\nandroid: VPN integration - done\n\n2023-09-26\n\nAdd Huawei 5g CPE H122-373\n\n2023-09-25\n\nimproved ME-906s modems IP rotation\nPrevent getting non-unique IP’s after rotation\n\n2023-09-13\n\nallow more than 1 device to use 1 VPN profile\nVodafone K5161h supported\n\n2023-09-10\n\nWebApp: show total online modems count\nWebApp: show VPN profile link in the main screen of the webapp\nFibocom L860 - custom APN fix & obeying custom Net Mode (TARGET_MODE)\ndeal with dongles Nicknames conflicts\ndeal with HTTP and SOCKS port conflicts\n\n2023-09-09\n\nAndroid proxies - improved Ping graphs; improved Carrier detection\n\n2023-08-30\n\nadded auto reconnect for DW5811e\n\n2023-08-28\n\nimproved QUIC support: add switch 3proxy/gost for Socks software in LAN ; add switch gost/gost3 as Socks software\n\n2023-08-25\n\nqmicli , added timeouts\n\n2023-08-23\n\nzteMF79NV support\n\n2023-08-21\n\nAlcatel modems (MW40,MW42,HH71) obeys $DEFAULT_HILINK_ADMIN_PASSWORD\nAlcatel MW45 improved support.\n\n2023-08-15\n\nadd Reset Counters button to the Bandwidth report page.\n\n2023-07-17\n\nimprove SMS sending on ZTE MF667, MF927, MF688V3E, MF823, MF833, MF93\n\n2023-07-13\n\nIP uniqueness report\n\n2023-07-10\n\nTop hosts report (WEB API method + WebApp table).\nAuto USB reset when modem’s WEB APP is stuck ( AUTO_USB_RESET_DONGLES=1 )\n\n2023-07-06\n\nAdd proxies built on Proxysmart android App\n\n2023-06-04\n\nSIM7600G proper SMS read\n\n2023-05-26\n\nMain proxy user:added Threads limit\nExtra users: added Threads limit & Dual_Auth IP\n\n2023-05-11\n\nadd SIM7600G (Lte cat4)\n\n2023-05-04\n\nadd Foxconn DW5821e (LTE cat 16)\n\n2023-05-02\n\nusb reset button in the WebApp\n\n2023-05-01\n\ncached list of the modems in the main screen of the WebApp\n\n2023-04-13\n\nsupport of pure old Stick(PPP) modems like Huawei E173, E156; ZTE MF110, MF193\nadd E3276 and E303 in Stick(NCM) mode\n\n2023-04-05\n\nability to hide old and new IP’s from the output of IP rotation links.\nauto reboot modems under some circumstances (check conf.txt for configuration)\n\n2023-03-29\n\nadded RequestsPerSecond graphs in modem status\nadded Pings graphs in modem status\n\n2023-03-28\n\nZyxel NR5103E support\n\n2023-03-20\n\n3proxy 0.9.x support\nability to block direct requests to IP’s (e.g. when clients resolve hostnames on their side and bypass Hostnames blocklist) and process only requests containing hostnames (Forces DNS resolution on proxy server side)\n\n2023-03-19\n\nautofix mass storage on Huawei modems\n\n2023-03-11\n\nRepeat IP rotation in case of failure (went online>offline, or the same IP received)\n\n2023-03-09\n\nfixed issue of DNS cache on modems\nBlack 4g modem – MSM8916 with /json WEB API\n\n2023-03-06\n\nability to reboot Quectel if no SIM\nNew UI\nshow graphs in modems statuses\n\n2023-03-03\n\ntimers fix (allows arbitrary nonstandard minutes like 31,61,1222)\nAlcatel IK41 in “stick” mode\n\n2023-03-02\n\nChinese MSM8916 modems support\nbug fix: JS was cached forever\nproxysmart.log - grandparent pid is logged\nproxysmart.log - timestamps preceed every line\nztemf79 : reconnect data\nNum of current connections is reported properly\nBrovi Huawei E3372-325 added support\nQuectel EC25-AFX added\nAPI for status query fixed\n\n2023-02-07\n\nMF667 modem support\n\n2023-02-06\n\nQuectel EM12G\n\n2023-02-05\n\nability to set a custom URL in secure rotation links\nability to set a custom HOST in proxy credentials ports\n\n2023-02-04\n\nQuectel EC25 can send USSD\nIK40 support + USSD\n\n2023-02-02\n\nE3276 can send USSD\nMF823 is properly shown\n\n2023-02-01\n\n3proxy logs contains IMEI and Nickname\n\n2023-01-29\n\nAPI for getting bandwidth stats within arbitrary time interval\n\n2023-01-28\n\nHuawei 3372h-325 “BROVI”\n\n2023-01-27\n\nmongodb cache bug fixed\n\n2023-01-22\n\nvpn: default UDP\n\n2023-01-19\n\nallow only DEMO version on VirtualBox\n\n2023-01-17\n\nshow anonymous link in main screen of the WebApp\n\n2023-01-15\n\nAdded new modem, Xproxy XH22 Wireless\nadded table of content in README\nProxies “Extra users”: added individual speed limits\n\n2023-01-14\n\nQuectel RM520 support (5g)\n\n2023-01-12\n\nFibocom L860, Ipv6 support\n\n2023-01-11\n\npurging sms (for Huawei in HiLink mode only)\n\n2023-01-10\n\nVodafone K5161z supported\n\n2023-01-08\n\nDocumentation: Dirty ip reset\n\n2023-01-07\n\nandroid phones integration (USB tethering mode & remote mobile tunnel mode).\n\n2023-01-04\n\nSecure IP reset links are now shown as full URL\n\n2023-01-03\n\nStatic VPN IP’s based on Index from Openvpn PKI\n\n2022-12-30\n\nWeb: editable field for Pho.Number.\nLink for downloading Openvpn profiles for modems\n\n2022-12-25\n\nQUIC support (UDP, HTTP/3.0) for SOCKS5 proxies , check README\n\n2022-12-22\n\nredefine variables in /etc/proxysmart/conf.d/*.inc\n\n2022-12-21\n\ndetect when CellOp redirects to their Billing Page\n\n2022-12-11\n\nSet minimum time between IP resets\na function for re-add a device: proxysmart.sh add_dev $DEV\nUDEV plug-n-play (hook scripts)\n\n2022-12-02\n\nHuawei dongles in NCM mode (WWAN+AT)\n\n2022-11-27\n\nFiboCom L860-gl : basic work + sms send\nHuawei ME906s : basic work + sms send\n\n2022-11-20\n\nSierraWireless EM7455\n\n2022-11-18\n\nCLR900A\nFranklinT10\n\n2022-11-12\n\nBW quota with PMACCTD, block outgoing access\nignore extra user when it matches with existing user (main user or another extra user)\nAlcatel MW40 - proper operation now\n\n2022-11-09\n\nVerizone Jetpack AC791L\nmore fast status for unknown modems models\n\n2022-11-08\n\nalcatel IK41: reboot, sms list, helper\n\n2022-11-01\n\nsolve issue when run_cache xxx times out and prints nothing, thus is executed over and over again\n\n2022-10-29\n\nDocumentation: secure rotation links\nget_ConnectionStatus_n response must contain OK\ncell op 425 02 ⇒ must be converted too\nmodel_shown returns “” sometimes ⇒ won’t fix, it happens when MAX_PARALLEL_WORKERS_STATUS is too large\n\n2022-10-27\n\nNovatel MIFI\nFranklin T9 (R717)\ncustom DNS servers for proxies\n\n2022-10-25\n\nNM disable modem*\n\n2022-10-19\n\nxproxy.io modems support\nbug fixed: Configuration file /etc/systemd/system/proxysmart.service is marked executable.\nwhen main proxy user == one of extra users, use extra user password\n\n2022-10-16\n\nvpn: blocklist of domains. make sniproxy enablable in conf.txt\nlicense revoking status checked online\n\n2022-10-15\n\nrework of denied domains list, *.domains are added automatically\n\n2022-10-12\n\nUF906 (KuWfi, Anydata, TianJie) modems integration\n\n2022-10-10\n\ndirty ip rotation support\nsecure ip rotation links with auto expiration\n\n2022-10-06\n\nOS spoofing for VPN users\nvpn: mongodb integration\n\n2022-10-04\n\nzte mf688T proxidize can send SMS\nd-link dwm222 basic support (beta)\nsignal reported in main table of the WebApp\n\n2022-09-30\n\nadded OS TCP spoofing with p0f3 signatures (WOW!!!), including Mac OS X, iOS, Android, Windows. Total rework of OSfooler-ng!\nmodem WEB server warm-up (when 1st request is ignored and 2nd and subsequent are processed)\n\n2022-09-22\n\nmodems helpers reorg, make them more fast & cached\nadded hourly IP rotation\n\n2022-09-19\n\nget by mongodb - get 1st value\n\n2022-09-16\n\nzte mf79VIVO support\nextra delay after IP rotation\n\n2022-09-12\n\nreport APN\nzte mf79 - sms send\n\n2022-09-08\n\nimproved support of ZTE MF927\n\n2022-09-03\n\nreport LTE band in full status\n\n2022-09-02\n\nsupport of 4g LAN routers like Huawei Bxxx\n\n2022-08-27\n\nreport IP rotation history as table\n\n2022-08-24\n\nreport IP rotation history\nWebApp can edit modems\nminor fixes: [ ignoring cell fwding in altnetworking2, dns in vpn, etc ]\nshow_model - more correct modems model showing\n\n2022-08-09\n\nopenvpn support (residential VPN!)\n\n2022-08-04\n\nipv6 bug fixed (ipv6 not assigned)\nchange mongodb uri in conf.txt\n\n2022-08-02\n\nnagios plugin exit code fixed ; nagios plugin moved to the main codebase\n\n2022-07-30\n\nfix: del symlink of altnetworking on installation\n\n2022-07-28\n\nhaproxy check >= 2.2\nDocumentation: – periodic IP rotation – ipv6 support – VPS integration\n\n2022-07-22\n\napply_settings = > if absent in DB, assign random creds.\n\n2022-07-20\n\nfixed bug when license stopped working because of floating (??) disk size and RAM size.\n\n2022-07-01\n\nconvert numeric cellop MCCMNC to Letters\ndel old show_status\n\n2022-06-23\n\nipv6, haproxy integration, systemd-slices (altnetworking2).\n\n2022-06-22\n\nmongodb: timeout handling\n\n2022-06-18\n\nmodem_names OR static udev map ⇒ make configurable\nis syslog-ng needed for Haproxy ?? revert back to rsyslog.\n\n2022-06-11\n\ndouble get_external_ip when doing IP reset via API\n\n2022-06-07\n\nPeriodic automatic IP rotation , set AUTO_IP_ROTATION per modem or globally\n\n2022-05-30\n\nadd usb_reset ( usb_reset_individual_DEV ) API, by nick\n\n2022-05-22\n\nadd proxy live counters based on RRD\n\n2022-05-07\n\nwait UDEV_xx after usb reset  reboot\nadd reboot API (CLI|WEB)\n\n2022-05-03\n\nreboot doesn’t wait till modem is added to the system; use reset_gently for that\n\n2022-05-01\n\nbug fixed when a modem has the same LAN as EthLAN, so the modem took priority over LAN\n\n2022-04-26\n\nzte MF971\nadd Quectel modems support\nreport ICCID\n\n2022-04-23\n\nbw_quota bug - quota restrictions are applied on next proxy request only\nread bw_quota, bandlimin, bandlimout, DENIED_SITES_ENABLE, DENIED_SITES_LIST, mtu, extra_users : from Mongodb\n\n2022-04-18\n\ncx /usr/lib/nagios/plugins/proxysmart-nagios-helper.sh\nDNS in Jinja2\n\n2022-04-08\n\nFIX: error 500 when modem not found\nlicense in web\nImei not unique ⇒ show in dash why\nIgnore reset if IMEI is in IGNORED_IMEIS\nDEV not added ⇒ why? show in status\nshow_status_brief, header must be 1st row\nNagios plugin doesn’t work with encrypted source\nZTE MF93: reboot call\n\n2022-03-31\n\nhuawei K5150: reset_ip , list_sms, reboot, send_sms\n\n2022-03-29\n\nmore UWSGI workers\ndemo license\ndeb package building\nmongodb template\n\n2022-03-28\n\nzte mf79: sms list, reboot, IP reset\n\n2022-03-22\n\nindividual blocklists( DENIED_SITES_ENABLE, DENIED_SITES_LIST )\n\n2022-03-21 (PRE)\n\nSms read\nDefault Modem admin pw in config.\nForwarder type in config ( WAN / CELL )\nusb reset yin config ( kris )\nworkmode: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.74 hipi.pl admin123 | grep -i workmode | cut -d“’” -f4\ncell_operator name: /etc/proxysmart/altnetworking.sh /etc/proxysmart/autogen/namespace.59 hlcli NetworkInfo | grep ‘ShortName’ | cut -d“ -f4\nadd_indiv.. and reset_gently\nN by modemN\nRandom port and nickname assignment if absent in the db\nlocks\nmake reset_gently WAIT till reset_complete is done\nJSON: report_bandwidth\nJSON function: apply settings for a modem\nstop redirector BEFORE ip rotation to prevent stalled SSH connection on remote SSH server\nleave checking global lock but never acquire it.\nJSON for list modems\nTABLEd status\nshow_status: model & cell_operator & workmode:\nreset_complete: del all Cgroups & ip rules\nJSON function: reset IP\nHipi scripts accept GW & AdminPW args\nmake screenshort w. CutyCapt\nVnstat autorestart\nflush ipv6 on modems\nmake show_status default\norder: curl, hipi w. pass, hipi w/o pass, hlcli, ETC\nOptional ttl fix: Y\nsms sort\nre-connect DATA on modems, if ext_ip empty ⇒ <NAME_EMAIL>\nre-connect DATA on modems, at reset_complete ⇒ <NAME_EMAIL>\nmtu. Report via Valdik\nmtu. Set\nACL : optional: allow admin only to browse HiLink webUI\nstart with no modems reset_complete doesn’t touch ⇒ adding a modem ⇒ reset_gently doesn’t work\ndel ipv6 leak from modems\nACL : optional: deny some sites\nProper logging , with ssh connection, stderr, as one SHOT\nno LAN test\ndefault login/pw for autogenerated proxies\nreport rotation time\nsafe way to read vars from included files\ndeploy script\nN from modemN May not work when iflink is not like modemXXX\ntrim CELLOP\nmanual: tell what http and socks5 ports to use\nmanual: how to use proxy, from internet, from lan\nallow ::1 in ip6tables\ndef.gw via 1 available modem – add to reset_complete\nrun_cached , to prevent multiple hipi runs\nfix EVAL() of commented lines like ‘v=v #ddd’\nregenerate ssh keys for FWD, if absent\nget rid of sourcing files: constants\nrun cached: log hit or miss\nmanual- check if your vps has firewall. disable it. both inside and in hoster panel.\nshow_status: If CurrNet is ’’, use workmode\ncheck faked route on reset_gnelty\nshow_status_json : redirector statuses\nupdate Manual: api calls & sample output\nspeedtest , up & down\ninclude redirector status in status. enabled, failed/running, uptime\nshow_status: list all proxies std format. depends on FWD_ENABLE, list $VPS:\nreplace XARGS+source to just xargs\nlog TimeTook for /var/log/proxysmart.log\ncrypted source autoGen\nbw report change\ncheck /var/run/proxysmart/reset_complete_done ⇒ show_status_json & show_status BREAK\nignored IMEI\nignored devices\nencode with shc.\nanti-p0f\nintegrate ZTE_MF6xx api ( goform/goprocess)\ncheck whether modem WebUI is available, for get_imei(), show_status_json(), get_model()\nadded Anydata UF906, Alcatel IK41 & MW40\nis_locked to JSON status\nglobal RESET LOCK\nzte MF823 ip change\nUSSD\nSMS\nreport phone uptime\nreport USB uptime\nIgnore FRESHLY inserted modems in reset_gently. Let them init\ndon’t rely on bash RANDOM:\nnagios plugin\nzte MF93D\nindividual JSON status for a modem\nWebapp: +GW, +Local_IP, +current hostname, send sms , send ussd\nDHCP more attempts\nWeb: brief status\nbrief status\nreconnect data - additional command, separate from reset_gently\nIP-based ACL\nspeed limits for proxies\nopen proxy (y)\ncheck modem WEB ⇒ change check_tcp to curl.\nDEB=mktemp /tmp/reset_modem_by_imei.XXXXXX – purge it\ndhcp → migrate to dhcpcd from Nmap\nQUICK_STATUS=0\\1, show only few details in show_status..\nreconnect data must give up when reset_complete not finished\nfix: add iptables -w to altnetowrking and Source\nget model ⇒ depend on manufacturer (from lsusb) for ZTE\nWEB API: report bandwidth\nCollectd + Web gui\nttl for _run_cached\nignore Ethernet-USB adapters\nmtu common and individual per modem\nhuman readable bytes counters in bandwidth reports\nmongodb: cached responses\nmake all functions accept IMEI|NICK\nmonthly Quota per user\nWebApp fix : Main WEB screen shows “Expecting value: line 1 column 1 (char 0)” and stucks and doesn’t do further refreshes\nExtra users in addition to main proxy users\nv2/changelog.txt · Last modified: 2024/07/03 04:22 by 127.0.0.1\nPage Tools\n    "}, {"title": "v2:readme ['Proxysmart Wiki' ]", "url": "https://proxysmart.org/wiki/v2:readme", "html": "Proxysmart Web\n\n\n\nskip to content\n'Proxysmart Wiki'\nUser Tools\nLog In\nSite Tools\nSearch\nSitemap\nv2:readme\nTable of Contents\n1. Proxysmart manual [v2].\n1. Brief details\n2. Adding modems\n2.1 Adding a new modem (USB)\n2.2. Adding a LAN modem.\n2.3. Adding an Android phone\n2.4. Adding a virtual modem (backend proxy).\n3. Proxy credentials for new modems\n4. Where is WebApp\n5. How to use proxies\n6. del\n7. Reconfigure all modems & proxies.\n8. How to change proxy credentials for a modem. How to rename a modem.\n9. Reset (change) IP on a modem.\n10. How many modems can I run on a single computer?\n11. How to set TTL and why?\n12. How to set MTU and why?\n13. How to set extra settings for a proxy port.\n14. How can I access the web interface admin panel of each modem?\n14.1. How can I prevent access to modems web interface via proxy?\n15. How to set monthly traffic quota per modem?\n16. How to make my proxes Open (i.e. not requiring authentication )\n17. Get monthly/daily proxy usage.\n18. How to get current number of connections for a modem?\n19. How to read SMS from a modem.\n20. How to change WebApp password\n21. OS Spoofing\n22. Performance tuning\n23. How to lock network mode per modem\n24. What if a modem connected via 3G or 2G, and I want 4G?\n25. I want to add extra users on a modem\n26. Is IPV6 supported?\n27. Nagios integration.\n28. IP rotation links.\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n30. “Dirty” IP reset.\n31. Exclude some modems\n32. Use custom Speedtest server.\n33. Minimum time between IP rotations\n34. How to block domains\n35. How to allow only whitelisted domains.\n36. How to re-rotate IP when IP doesn’t change?\n37. Prevent non-unique IP’s after IP rotation.\n38. How to forward proxy ports using HAproxy?\n39. Custom DNS server for the proxies\n40. Where are proxy logs.\n41. No logs policy\n42. My proxies are slow.\n43. My proxies are slower than the same SIM card in a Phone.\n44. How to forward proxy ports via each modem individually?\n45. Auto-rebooting modems.\n46. My proxy is offline and showing Red in the WebApp.\n47. del\n48. IP's are rotated on their own\n49. Install logging of all requests in single place\n50. PPP modems\n51. Alerts to Telegram\n2. Project description\n1. architecture\n2. Online services are used:\n3. CLI API\n1. show status\n2. full reconfiguration\n3. apply setting for a modem by IMEI\n4. reset IP on a modem\n5. reboot a modem\n6.1. Reset a modem via USB\n6. Run speedtest\n7. report bandwitdh\n8. reset bandwidth counter on a port\n9. list sms on a modem\n10. send sms\n11. purge SMS\n12. send ussd\n13. get bandwidth counters from a modem\n14. Get IP rotations log for a modem\n15. Get Top hosts from a modem\n16. Report IP uniqueness\n4. WEB API\n1. Web API description.\n2. List all modems ( full status, slow)\n3. List all modems ( brief status, fast )\n3.1. List all active ports\n4. Single modem status\n5. Reset (change) IP on a modem.\n6. Reboot a modem\n7. Send SMS\n8. Send USSD and read response\n9. Read SMS from a modem\n10. Read bandwidth stats from a port\n11. del\n12. Reset bandwidth stats for a port\n13. Reset a modem via USB\n14. Get IP rotations log for a modem\n15. Apply settings for a modem\n15.1. Apply settings for a port\n15.2. Purge a port\n16. Purge SMS from a modem\n17. Get Top hosts from a modem\n18. Report IP uniquness\n19. Store a modem object in Mongodb\n20. Store a port object in Mongodb\n21. Export backup\n5. Mongodb integration\n5.1. Schema\n5.1.1. Modems\n5.1.2. Ports\n5.2 Workflow\n5.3 Configuration\n5.4 Moving Mongodb to other server\n6. Installation\n1. Initial installation\nDevelopment version installation\n2. Upgrade\n2.1. Upgrade from older V2\n2.2 Upgrade from V1\n3. Post Installation\n4. Cloud VPS integration.\nDo I need a VPS?\nVPS setup steps.\nCloud VPS IP change\n5. Forwarding ports through your own LAN router.\n7. License\n1. Demo license\n2. Requesting a License\n2. License installation\n3. Restoring Demo license.\n8. Mobile (4G/5G) VPN\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n8.1.2. Installation with TCP protocol (through LAN router)\n8.1.3. Installation with UDP protocol (through VPS)\n8.1.4. Installation with UDP protocol (through LAN router)\n8.2. Many users with the same profile\n8.3. Mobile VPN, how to connect\n8.4. Mobile VPN, FAQ\n8.4.1. Switch Openvpn protocol\n8.5. Mobile VPN logs\n9. Bugs and Limitations\nLTE modules\nAndroid phones\nVPN users\nPort ranges\nOS TCP Fingerprint spoofing\n1. Proxysmart manual [v2].\n1. Brief details\n\nThe software allows running your own 4g proxy farm. It runs on a Linux box (PC) with USB hub and the modems.\n\nFunctions:\n\nIP resets on modems (+ automatic rotation + checking IP uniqueness)\nWebApp for checking statuses of the modems, for creating users and ports, IP rotations\nWEB API for all actions\nBandwidth quotas and Speed limits per proxy\nExposing proxy ports, so they are available from world wide\nReading,sending SMS and USSD codes\nOS spoofing, to simulate OS TCP fingerprints of: MacOS, iOS, Windows,  Android (+any other OS)\nProxy ACLs (what to allow/deny to proxy users) - blacklists\nCreating mobile VPN together with proxies\nSocks5 supports UDP and QUIC (HTTP/3.0)\nNo leaks\nNative DNS from mobile carriers\nLarge set of supported USB modems, LAN routers, LTE modules, Android phones.\nBasic configuration.\n\nVariables are set in the WebApp→Global_settings and in /etc/proxysmart/conf.txt.\n\nEach variable has brief description in place.\n\n2. Adding modems\n2.1 Adding a new modem (USB)\nremove PIN from the modem’s SIM card and plug in the modem into USB port or USB hub.\nCheck whether your modem Web App (e.g. Huawei’s E8372 / E5xxx or ZTE MF79 or Alcatel MW4x ) requires authentication, and if it does, set its admin password to admin123. Basically to the value of DEFAULT_HILINK_ADMIN_PASSWORD variable in WebApp→GlobalSettings . Otherwise many functions will not work, and its IMEI will be detected similarly to 2-1.1.2\nPlug in the modem\nwait ~5 minutes or run sudo proxysmart.sh reset_gently\nthe modem will appear in the WebApp, click EDIT on it, assign some unique Nickname, HTTP & SOCKS5 ports, Login and Password, then click APPLY\nrefresh the WebApp\ndone!\n2.2. Adding a LAN modem.\n\nConfigure the server with 2 LAN cards\n\nAssume you have 2 LAN cards, enp6s0 main LAN, enp2s0 is dedicated for LAN modems:\n\nnmcli con\n\nNAME                UUID                                  TYPE      DEVICE \nWired connection 1  bbbee134-51c3-3830-801f-9636470e0708  ethernet  enp6s0\nWired connection 2  000ed912-2d99-3f37-882b-d79ad13102e7  ethernet  enp2s0 \nRename Wired connection 2 → HUBS\nnmcli con modify Wired\\ connection\\ 2 con-name HUBS\nDisable DHCP and IPV6 on HUBS and assign static IPv4 address\nnmcli con modify HUBS ipv4.method manual ipv4.addresses *************0/24 ipv6.method link-only ipv4.route-metric 300 \n\nSo you will add the LAN modems to ************/24 network as ************, ************ etc.\n\nsystemctl restart NetworkManager\n\nDelete old route\n\nip ro del default via ************\n\nConfirm you have only 1 default route via main LAN:\n\nip ro\n\nOutput\n\ndefault via *********** dev enp6s0 proto static metric 100 \n\nAdd the modem\n\nChange the modem’s web admin password to something stored in WebApp→GlobalSettings as DEFAULT_HILINK_ADMIN_PASSWORD variable.\nChange the modem’s IP to something unique e.g. *************\nPut the modem's LAN outlet into Ethernet switch together with the Proxysmart server.\nOn the Proxysmart server make sure you can ping the new modem by its IP you set in previous step.\nMake sure LAN_MODEMS_ENABLE=1 is in WebApp→GlobalSettings.\nAdd Lan modem in the Webapp→Edit_modems , scroll to the bottom, and add as lanmodem10 , ************* .\n\nThen either wait 5 minutes or run the command sudo proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n2.3. Adding an Android phone\n\nMain guide dedicated to adding Android Phones: Android phones guide\n\n2.4. Adding a virtual modem (backend proxy).\n\nA virtual modem is a in fact a redirect to a 3rd party proxy (HTTP or SOCKS5) so you can build own proxies based on that and resell them.\n\nThey even can be rotated if the backend proxy supports it.\n\nHow to add?\n\nMake sure BACKEND_PROXIES_ENABLE=1 is in WebApp→Global_settings .\n\nAdd them the Webapp→Edit_modems→Virtual modems\n\n, scroll to the bottom, and add each with the following fields\n\nid has to be in the form 'bproxy' + a number e.g. bproxy1 or bproxy2\ncreds is a line with credentials of the backend proxy e.g. ************************************* or socks5://Mylogin:Mypassword@Server:1080\nip_reset is an optional parameter , the URL for triggering IP rotation of the backend proxy\n\nClick SAVE\n\nThen either wait 5 minutes or run the command sudo proxysmart reset_gently, it will find new modems. Then , refresh the proxysmart Web App and assign proxy logins and passwords to the new modems.\n\n3. Proxy credentials for new modems\n\nWhen adding new modems, please use\n\nunique HTTP ports from 8001 to 8999,\nunique SOCKS ports from 5001 to 5999.\nunique nicknames like dongleXXX or whatever else. Don’t use nicknames like randomXXX, that are assigned automatically.\n4. Where is WebApp\n\nOne of\n\nhttp://localhost:8080/\nhttp://LAN_IP:8080/\nhttp://VPS_IP:8080/\n\nBy default login/password are proxy / proxy.\n\n5. How to use proxies\nIf proxy ports are forwarded via remote cloud VPS: then the proxies can be used from all over the Internet, by that VPS IP and proxy port numbers.\nFrom the same LAN where multimodem server is located: by the server’s LAN IP and proxy port numbers.\n6. del\n\ndel\n\n7. Reconfigure all modems & proxies.\n\nMethod1. Click the button “Reset Complete” on the main screen of the WebApp in the bottom.\n\nMethod2. In linux console, run: proxysmart.sh reset_complete\n\nAlso it is done after reboot automatically by a Cron job.\n\n8. How to change proxy credentials for a modem. How to rename a modem.\n\nWebApp method\n\nclick EDIT on a modem, set new port or password or nickname for a modem\nclick APPLY\n9. Reset (change) IP on a modem.\n\nThe options are below.\n\nFrom Web App\n\nClick Reset Ip button.\n\nFrom command line.\n\nRun: proxysmart.sh reset_quick_nick dongle1\n\nWhere dongle1 is a Dongle “nickname” that is seen from output of proxysmart.sh show_status\n\nFrom Web API.\n\ncheck WEB API section of this manual.\n\nHow to rotate a modem periodically?\n\nWebApp method\n\nUpdate modem’s settings in the WebApp and click APPLY.\n\nCron method\n\nInstall a Cron job. Edit a file /etc/cron.d/proxysmart, add a line ( or uncomment a commented line.. )\n\n*/10 * * * * root run-one /usr/local/bin/proxysmart.sh reset_quick_nick dongle3\n\nso that a modem with the Nickname dongle3 is rotated every 10 min.\n\nRepeat for each modem you want to rotate periodically.\n\n10. How many modems can I run on a single computer?\n\nHi , technically it depends on how powerful this PC is, and how intensively proxies are used.\n\nRaspberry PI - 4 proxies (roughly)\na miniPC (Intel NUC or similar) - up to 10\na Laptop like Core i5 - up to 30.\n\nAlso it depends on what Plan you buy.\n\nAlso it depends on USB configuration, for maximum number of modems:\n\ndisable USB3.0 in BIOS\nuse USB2.0 hubs\n11. How to set TTL and why?\n\nIn some cases custom TTL must be set in order to have Cell Operator think we are not using the modem in hotsport  tethering mode. I.e. we don’t share its data. By default Linux OS has ttl = 64. To change Cell Operator perception of the situation, we want to set it +1 i.e. 65.\n\nEdit WebApp→GlobalSettings and set CUSTOM_TTL_SET=1 and CUSTOM_TTL_VALUE=65 and regenerate settings.\n\n12. How to set MTU and why?\n\nIn some cases different MTU values connect with different types of ISP’s. You may want to change it.\n\nMtu can be only lowered. E.g. if you have MTU 1390, you can set 1340. Not opposite.\n\n- Edit /etc/proxysmart/conf.txt and set CUSTOM_MTU_SET=1 . - Set MTU in the WebApp for each modem.\n\n13. How to set extra settings for a proxy port.\n\nThose are optional and are set in the WebApp\n\nWHITELIST - allowed customers IP’s who are not required to type in proxy password (IP-based auth).\nbandwidth (speed) limit. Values are in mbps (megabits per second).\nDENIED_SITES_ENABLE (on/off) and DENIED_SITES_LIST (list of blocked sites patterns).\nBandwidth Quota (Megabytes) and Bandwidth Quota Type (daily/monthly/lifetime)\n14. How can I access the web interface admin panel of each modem?\n\nOpen WebApp. Locate the modem. Configure a proxy on your desktop browser.\n\nUse proxy login & password as desribed below (14.1 chapter).\n\nVisit modem IP via that proxy.\n\n14.1. How can I prevent access to modems web interface via proxy?\n\nSince 2023-09-10 it is enabled by default.\n\nEdit WebApp→GlobalSettings and set\n\nPROXY_ADMIN_ENABLE=1\nPROXY_ADMIN_LOGIN=SuperAdmin\nPROXY_ADMIN_PASS=Hqmz81mmZr\n\nAnd regenerate configs. So only admin user is allowed to use modems web interfaces, and normal proxy users are not.\n\n15. How to set monthly traffic quota per modem?\n\nIn the WebApp, set monthly traffic quota. Click EDIT & APPLY.\n\n16. How to make my proxes Open (i.e. not requiring authentication )\n\nSet OPEN_PROXIES=1 in WebApp→GlobalSettings and regenerate all configs.\n\nNote, when proxy ports are forrwarded via a VPS, the proxies are available to any internet user. Use it with caution.\n\n17. Get monthly/daily proxy usage.\n\nClick bandwitdh stats in the WebApp, or run proxysmart.sh bandwidth_report_json portIDXXX, you will see these columns:\n\n“bandwidth_bytes_day_in”\n“bandwidth_bytes_day_out”\n“bandwidth_bytes_month_in”\n“bandwidth_bytes_month_out”\n“bandwidth_bytes_yesterday_in”\n“bandwidth_bytes_yesterday_out”\n18. How to get current number of connections for a modem?\n\nRun a command\n\nss -o state established | grep -c :8038\n\nBut change 8038 with HTTP port of a desired proxy\n\n19. How to read SMS from a modem.\n\nYou have these options.\n\nClick Read SMS in the WebApp\nrun proxysmart.sh list_sms_for_a_modem_by_imei_json 999999999999999 i.e. IMEI of required modem.\nBrowse to the modem IP ( it is shown as GW in proxysmart.sh show_status ) through the proxy. Click SMS button.\n20. How to change WebApp password\n\nBy default it is set to proxy / proxy.\n\nIn the WebApp→GlobalSettings scroll to the bottom, set new WebApp password. NOTE: login remains proxy.\n\nCommand line method.\n\nsudo htpasswd -b /etc/nginx/htpasswd proxy NewAweSomePassword999999\n\nIf you want to change username as well, just delete the file and then assign new password\n\nsudo rm /etc/nginx/htpasswd\nsudo htpasswd -b -c /etc/nginx/htpasswd MyNewUsername NewAweSomePassword999999\n21. OS Spoofing\n\nOs Spoofing is used to simulate other OS TCP fingerprints.\n\nWhat OS can I spoof?\n\nMacOSX, iOS,  Windows,  Android.\n\nHow to enable OS Spoofing?\n\nIn the WebApp set the needed OS per each proxy port (click EDIT PORT).\n\nHow to test OS Spoofing ?\n\nVisit one of these websites (IP checkers) through a proxy. Find something like “OS TCP fingerprints”.\n\nhttp://witch.valdikss.org.ru/\nhttps://thesafety.us/\nhttps://whoer.net → extended results\nhttps://browserleaks.com/ip\n\nCan I dump OS TCP fingerprint from a real device and use it?\n\nYes, contact me.\n\nI enabled OS TCP spoofing, but it is not working!\n\nThe reason may be that the operator passes all traffic through its internal proxy, or in other way modifies TCP signatures. Then local OS TCP modifications are overwritten. Is it bad? No! Because still traffic looks natural as it was coming from this operator network.\n\nTry other operator.\n\n22. Performance tuning\n\nWhen >10 modems are added, and when modem list is generated slowly, play with MAX_PARALLEL_WORKERS_STATUS variable, on faster CPU’s it can be set to 8 or 16.\n\n23. How to lock network mode per modem\n\nSet TARGET_MODE in its settings in the Proxysmart WebApp. Allowed values:\n\nauto\n3g\n4g\n24. What if a modem connected via 3G or 2G, and I want 4G?\n\nRotate its IP.\n\n25. I want to add extra users on a modem\n\nIn the WebApp, create more ports on the modem, each port means a dedicated proxy.\n\n26. Is IPV6 supported?\n\nYes but it’s off by default.\n\nOn modems , edit APN and set APN type for both IPv4 and IPv6 , e.g. Ip4Ip6 or Ip4+ip6, there is a dropdown list for that.\n\nOn Proxysmart box: Update WebApp→GlobalSettings → IPV6_SUPPORT On\n\nand reset configuration proxysmart.sh reset_complete ; or even better do a reboot.\n\n27. Nagios integration.\n\nThere is a plugin embedded, run it as root,\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh IMEI\n\nor\n\n/usr/lib/nagios/plugins/proxysmart-nagios-helper.sh NICKNAME\n\nso it will return OK/WARN/CRIT/UNKNOWN and corresponding exit code.\n\n28. IP rotation links.\n\nThese links\n\nCan be safely passed to your customers. They don’t reveal real dongle parameters like IMEI or Nickname.\nThey don’t require HTTP basic authentication\nThey have limited lifetime , it is set in WebApp→GlobalSettings as RESET_LINK_VALIDITY variable, (default value : 5years).\nThey depend on the proxy password. So, when you change the proxy password - old IP rotation links, associated with that proxy, will stop working.\n\nA link can be copied from the WebApp→Ports list. Each Port has its own IP rotation link. If one port rotates IP, then other ports of the same modem affected too.\n\nIf you realized you gave a link to a customer, and want to revoke it, just set new password for the proxy.\n\nIf you want to invalidate all links of all modems, set a new secret: set RESET_LINK_SECRET in WebApp→GlobalSettings .\n\n29. QUIC (UDP) support on Socks5 proxies, for HTTP/3.0\n\nIt is needed for proper work of HTTP/3.0 which uses UDP.\n\nQUIC (UDP over socks5) will work either in your LAN or via a VPS. Steps are below.\n\nSteps on VPS :\n\nMake sure you finished the Cloud VPS setup part, with Ansible\n\ncd /root/proxysmart-vps/\nnano vars.txt\n\n- set vps_socks5_udp: 1\n\nSave the file (press Control O) and exit the editor (Control x)\n\nRun Ansible again\n\nansible-playbook ./proxysmart-vps.yml\nSteps on Proxysmart server :\n\nset in WebApp->GlobalSettings → QUIC_SUPPORT : On.\n\nand reboot or reconfigure all proxies (run proxysmart.sh reset_complete ).\n\nNote: make sure the VPS has enough RAM, each proxy needs 50MB of RAM. Also add swap if needed.\n\n30. “Dirty” IP reset.\n\nIt may be needed when you need even faster IP reset. In this case, post-checks are not made, so it is not sure if the modem really went online after IP reset. It can be activated by DIRTY_IP_ROTATION=1 in WebApp→GlobalSettings\n\n31. Exclude some modems\n\nIn /etc/proxysmart/conf.txt\n\nby Device name, populate this array IGNORED_DEV=( modem132 modem0000000002) – array of Network Interfaces that are not processed\nby IMEI, populate this array IGNORED_IMEI=( 9999999999999999 8888888888888888 ) – array of IMEI that are not processed\n32. Use custom Speedtest server.\n\nIt is useful when for some reason you want to run speed tests towards a custom server, instead of Ookla servers. So set up a Apache web server with a large file (500MB) and get 2 URL’s, one will test download and 2nd will test upload. The latter must accept large POST data.\n\nThe commands to setup a server part\n\napt install apache2\ndd if=/dev/urandom  of=/var/www/html/file.bin bs=1M count=500\n\nUpdate WebApp→Global_settings with IP of the WEB server:\n\nSPEEDTEST_CUSTOM=1  \nDL_URL=http://v.v.v.v/file.bin\nUL_URL=http://v.v.v.v/i.php\n\nWhere v.v.v.v is VPS IP.\n\nDL_URL can be an URL of a large enough file (~100Mb+). And UL_URL is an URL that accepts large enough POST request.\n\n33. Minimum time between IP rotations\n\nIf you want to avoid too frequent IP rotations triggered by your users – set MINIMUM_TIME_BETWEEN_ROTATIONS=120 e.g. for 120 seconds minimum delay in WebApp→Global_settings .\n\n34. How to block domains\n\nIndividual (per proxy) block lists\n\nCheck (enable) DENIED_SITES_ENABLE in the WebApp\nDENIED_SITES_LIST is a list of domains that will be blocked, both HTTP and HTTPS, plus their subdomains. E.g. if you list porn.com, then also www1.porn.com,www.porn.com,porn.com are blocked.\n\nGlobal block list - for all proxies\n\nin WebApp→Global_settings set DENIED_SITES_ENABLE and paste domains or IP's into DENIED_SITES_LIST , click SAVE and re-apply all modems settings.\n\nNote for Socks5 proxies\n\nWhen a domain blacklist is imposed, then by default users still can access blocked sites by their IP’s.\n\nIn order to prevent it, set DENY_IP_REQUESTS=1 in WebApp→Global_settings and run proxysmart.sh reset_complete for resetting all configuration (or reboot).\n\n35. How to allow only whitelisted domains.\n\nThe feaure it not ready.\n\n36. How to re-rotate IP when IP doesn’t change?\n\nIn WebApp→Global_settings set RETRY_IP_ROTATIONS=1 .\n\nSo when Old_IP == New_IP, then IP rotation is retried. Up to MAX_RETRY_IP_ROTATIONS attempts which is by default 3.\n\n37. Prevent non-unique IP’s after IP rotation.\n\nFor example to prevent using IP’s that were in use 1 time (or more) within last 24h: set in WebApp→Global_settings :\n\nRETRY_IP_ROTATIONS=1                 # enables Re-rotation\nNON_UNIQUE_IP_OCCURS=\"1\"             # how many times an IP must occur to be considered NonUnique. E.g. 1\nNON_UNIQUE_IP_PERIOD=\"24hour\"        # during which period an IP must occur to be considered NonUnique. E.g. 1day or 1hour\n38. How to forward proxy ports using HAproxy?\n\nWhy? In order to enable client IP whitelisting, i.e. 3proxy on proxysmart server will see original client IP and will be able to use whitelising.\n\nSteps:\n\n1. On Proxysmart server\n\nset PROXY_PORTS_FORWARDER_SOFTWARE=ssh+haproxy in WebApp→Global_settings\nrun proxysmart.sh reset_complete for resetting all configuration.\n\n2. On the VPS\n\ncd /root/proxysmart-vps/\nnano vars.txt\n\n- set haproxy_enabled: 1\n\nSave the file (press Control O) and exit the editor (Control x)\n\nRun Ansible again\n\nansible-playbook ./proxysmart-vps.yml\n\n3. Post check\n\nTest a proxy via VPS IP and you will original client IP in 3proxy logs.\n\n39. Custom DNS server for the proxies\n\nEdit /etc/proxysmart/conf.txt and set DNS_SERVER_PROXIES=“*******” where `*******` is a custom DNS server, it must be publicly available.\n\nClick the button “Reset Complete” on the main screen of the WebApp in the bottom or in the console, run: sudo proxysmart.sh reset_complete or reboot the server.\n\n40. Where are proxy logs.\n\nOn the Proxysmart server in a folder /var/log/3proxy/ , each filename is named for HTTP proxy port.\n\nLogs are rotated daily and 90 copies are saved, details are in /etc/logrotate.d/3proxy.\n\nLogs of IP rotations are in a folder /var/log/proxysmart/dongle_rotations/.\n\n41. No logs policy\n\nIf you want to run NoLogs policy, create a cron script that deletes the logs, i.e. the files\n\n/var/log/gost/*\n/var/log/3proxy/*\n/var/log/sniproxy*\n/var/log/haproxy*\n42. My proxies are slow.\n\nAssume a chain UsbModem→PC→VPS→ProxyUser. Final Proxy speed is limited by:\n\nDownload speed of the modem\nUpload speed from PC to VPS\nDownload speed from VPS to the ProxyUser\nDownload speed of the modem.\n\nIt can be measured on the side of the PC e.g. in the Proxysmart WebApp by clicking the Speedtest button.\n\nHow to improve it?\n\ntry other carriers\ntry other modems\ntry better location with better signal (i.e. not your Home)\nUpload speed from PC to VPS.\n\nNormally it correlates with quality of home internet (Fiber/xDSL) and can be measured by running speedtest on the PC in browser or in Terminal (speedtest-cli). Upload value has to be high.\n\nWith different types of port forwardings:\n\nwan (Home Internet is used for ports forwarding) : remote proxy user's DownloadSpeed is limited to minimum of (ModemDownloadSpeed, HomeInternetUploadSpeed )\n\ncell (each modem forwards its proxies through its internet) : remote proxy user's DownloadSpeed is limited to minimum of (ModemDownloadSpeed, ModemUploadSpeed )\n\nHow to improve it?\n\nget a better home internet with better upload\nswitch from WiFi to Ethernet\nDownload speed from VPS to the ProxyUser\n\nIt can be measured by downloading a file from VPS to the Proxyuser.\n\nHow to improve it?\n\nChange location of the VPS to a Cloud Hoster that has better reachability to the clients from all over the world\n43. My proxies are slower than the same SIM card in a Phone.\n\nReason 1: Compare LTE category of the modem and the phone. Phone has higher LTE cat e.g. 12..20, while modem has LTE cat 4..6 (depends).\n\nReason 2: when the speed is really bad (about 1mbps) then it is Operator's throttling. Perhaps you bought a plan that allows only phones/tablets and doesn't allow modems.\n\n44. How to forward proxy ports via each modem individually?\n\nWhy is it needed? When home base internet is unstable or its upload speed <15mbps.\n\nA VPS is needed in order to expose the ports this way ( see VPS integration chapter ).\n\nHow it works\n\nEach proxy forwards its port through its modem, not using base internet.\n\nPRO's :\n\nHome base internet speed & stability is not important\n\nCON's :\n\neach modem is working in bidirectional mode\nproxy speed is limited to 4G Upload speed which is slow\n\nSteps: on Proxysmart server\n\nset PROXY_PORTS_FORWARDER_TYPE=cell in WebApp→Global_settings\nrun proxysmart.sh reset_complete for resetting all configuration.\n45. Auto-rebooting modems.\n\nSometimes only a reboot can fix a modem. In order to enable, set AUTOREBOOT_DONGLES=1 in WebApp→Global_settings. How it works:\n\nif a situation occurs , “reboot score” of a modem is increased by the value, according to the situation:\nSCORE_IP_ROTATION_FAIL=10                   # score increments when IP rotation failed\nSCORE_IP_NOT_DETECTED=2                     # score increments when IP not detected\nSCORE_IP_RECONNECT_FAIL=10                  # score increments when IP not auto-reconnected\nSCORE_WWAN_DATA_FAIL=10                     # score increments when WWAN device can't establish Data connection\nSCORE_WEBAPP_FAIL=20                        # score increments when the modem's WebApp is stuck\nwhen the modem’s reboot score reaches MAX_REBOOT_SCORE then the modem is rebooted.\nspecial case, do USB reset instead of a reboot, when AUTO_USB_RESET_DONGLES is 1, it is useful when modems’ WEB APP is not available.\n46. My proxy is offline and showing Red in the WebApp.\nCheck if the modem has good signal.\nCheck if the modem has correct APN (set in its Web Dashboard).\nCheck if its SIM card is active (not blocked on Operator side) and is topped up.\nCheck the modem on another PC (e.g. your own desktop).\n47. del\n48. IP's are rotated on their own\n\nIf you don't rotate IP's and they are detected each time as a new IP - it is natural behaviour of mobile provider, when it routes its clients through random different gateways every 1 minute or so. T-Mobile USA is known of doing so.\n\n49. Install logging of all requests in single place\n\nWhy? Get single log of all requests from Proxies (HTTP/Socks5) clients and VPN clients.\n\nInstallation On Proxysmart server\n\nIn the WebApp→GlobalSettings set SNIFFER_ENABLED=1 and click Apply.\n\nrun proxysmart.sh reset_complete\n\nWatch the log /var/log/proxy_log.log on Proxysmart server.\n\nIt is rotated and 365 daily copies are stored on disk.\n\nThen it is bound to a button “Download Proxy Logs”.\n\nIt can also be installed on a VPS if the VPS is working as proxies frontend.\n\nInstallation On VPS\n\nnot supported yet.\n\nLog format\n\nFile: /var/log/proxy_log.log\n\n    _ws.col.Time  frame.interface_name   ip.src  tcp.srcport   ip.dst   tcp.dstport  \n    #   1          2                        3       4           5           6\n    \n    socks.remote_name    socks.dst    socks.port   socks.dstport \n    # 7                         8         9         10\n    \n     http.request.method    http.host  \n    #   11                  12        \n\n     tls.handshake.extensions_server_name  x509ce.dNSName\n    #   13                                  14\n50. PPP modems\n\nThese are very old 3g modems like Huawei E303, E173, E156; ZTE MF110, MF193, MF190. In order to make them work with proxysmart,\n\nedit WebApp→GlobalSettings and set PPP_MODEMS_ENABLE=1 .\n\nMake Quectel / Sierra Wireless LTE modules work in PPP mode\n\nWhy? sometimes they fail working in QMI mode. So:\n\nedit WebApp→GlobalSettings and set PPP_MODEMS_ENABLE=1\nplace a file /etc/udev/rules.d/21-wwan.rules\n# ignore QMI_WWAN endpoints on Quectel, to make it work in PPP mode.\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"2c7c\" , ATTRS{idProduct}==\"0125\",  ENV{.LOCAL_ifNum}==\"04\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\n# ignore QMI_WWAN endpoints on SierraWireless  , to make it work in PPP mode. Save to 21-wwan.rules:\nSUBSYSTEM==\"net\", ACTION==\"add\",  ATTRS{idVendor}==\"413c\" , ATTRS{idProduct}==\"81b6\",  ENV{.LOCAL_ifNum}==\"08\", PROGRAM=\"/usr/local/bin/usb_ignore.sh %p\"\nre-plug modems or reboot Proxysmart server\n51. Alerts to Telegram\n\nIn Telegram start a chat with a bot https://t.me/userinfobot and get your Telegram numeric ID.\n\nIn Proxysmart WebApp→GlobalSettings , set TG_ALERTS_ENABLE ; and set TG_ALERTS_RECEIVER to your Telegram numeric ID.\n\nIn Telegram start a chat with Proxysmart bot https://t.me/nagios737bot and send 'hi'.\n\nAfter that the bot will send you alerts.\n\n2. Project description\n1. architecture\nonsite: box with Ubuntu, USB hub and modems\nremote: VPS with proxy ports (optional)\n2. Online services are used:\nhttp://ip.tanatos.org/ip.php which is simple PHP script that returns visitor’s IP. It is used to detect whether a modem is really online. Can be replaced with one of https://ifconfig.co or similar, but I was not happy with their reliabiality, they are down sometimes. The URL is defined in WebApp→Global_settings\nhttp://witch.valdikss.org.ru/ : used for detecting p0f and MTU\n3. CLI API\n1. show status\n\nShow full status of all modems, table (slower).\n\n# proxysmart.sh  show_status \n\nOutput:\n\nShow brief status of all modems, table, (faster)\n\nRun\n\n# proxysmart.sh  show_status_brief\n\nOutput:\n\nShow full status of all modems , json\n\n# proxysmart.sh  show_status_json \n\nOutput:\n\nShow status for a single modem, JSON\n\nArguements - NICK or IMEI.\n\n# proxysmart.sh  show_single_status_json dongle111 \n\nOutput:\n\n2. full reconfiguration\n\nRun\n\n# proxysmart.sh reset_complete  \n\nOutput:\n\n3. apply setting for a modem by IMEI\n\nJSON output\n\n# proxysmart.sh   apply_settings_for_a_modem_by_imei  868723029999406 \n\nOutput:\n\nPlain text output:\n\n proxysmart.sh  apply_settings_for_a_modem_by_imei_raw    359999999999999 \n\noutput:\n\n4. reset IP on a modem\n\nArgs: IMEI or NICKNAME.\n\nJSON output:\n\n# proxysmart.sh   reset_modem_by_imei    899999999999999 \n# proxysmart.sh   reset_modem_by_imei    Dongle222\n\nOutput:\n\nPlain text output:\n\n# proxysmart.sh  reset_quick_nick  899999999999999\n# proxysmart.sh  reset_quick_nick  Dongle222\n\nOutput:\n\n5. reboot a modem\n\nArgs: Nickname or IMEI.\n\nTEXT Output\n\nJSON Output\n\n6.1. Reset a modem via USB\n\nCan accept DEV name, IMEI or Nickname. So\n\nFor Text output:\n\nFor Json output.\n\n6. Run speedtest\n\nOn a single modem:\n\nArgs: NICKNAME or IMEI.\n\n# proxysmart.sh  speedtest 353990074160000\n# proxysmart.sh  speedtest sierra\n\nResponse:\n\n7. report bandwitdh\n\nOn a single port\n\nWith arbitrary time interval.\n\n8. reset bandwidth counter on a port\n\nARGS: portID\n\nJSON output\n\n9. list sms on a modem\n\nJSON output\n\n10. send sms\n\nPlain output:\n\nJSON output:\n\n11. purge SMS\n\nPurges SMS from all folders.\n\nCall by IMEI or nickname,\n\njson output:\n\n12. send ussd\n\nPlain output\n\nJSON output:\n\n13. get bandwidth counters from a modem\n\n..use bandwidth stats..\n\n14. Get IP rotations log for a modem\n\nBy Nickname or IMEI\n\n15. Get Top hosts from a modem\n\nBy Nickname or IMEI\n\n16. Report IP uniqueness\n\nJSON output.\n\nTEXT output.\n\n4. WEB API\n1. Web API description.\n\nWEB API endpoint is the URL that Proxysmart WebApp available at.\n\nIt can be\n\nLAN_IP:8080 when you call it from the same LAN\nVPS_IP:8080 when you forwardded ports to the Cloud VPS\nSTATIC_IP:8080 when you forwarded ports via your LAN router and your ISP gave you STATIC_IP\n\nAlso attach proper username:password (the -u parameter).\n\nWhenever below you are seeing localhost:8080, replace it with the actual WEB API endpoint.\n\n2. List all modems ( full status, slow)\n\nRequest:\n\ncurl 'http://localhost:8080/apix/show_status_json' -u proxy:proxy \n\nResponse:\n\n3. List all modems ( brief status, fast )\n\nRequest:\n\ncurl localhost:8080/apix/show_status_brief_json -u proxy:proxy\n\nResponse:\n\n3.1. List all active ports\n\nRequest:\n\ncurl http://localhost:8080/apix/list_ports_json -u proxy:proxy\n\nResponse:\n\n4. Single modem status\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/show_single_status_json?arg=dongle111    -u proxy:proxy\ncurl http://localhost:8080/apix/show_single_status_json?arg=899999999999999    -u proxy:proxy\n\nResponse:\n\n5. Reset (change) IP on a modem.\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reset_modem_by_imei?IMEI=899999999999999 -u proxy:proxy\ncurl http://localhost:8080/apix/reset_modem_by_nick?NICK=dongle22 -u proxy:proxy\n\nResponse:\n\n6. Reboot a modem\n\nRequest:\n\n( either by IMEI or Nickname )\n\ncurl http://localhost:8080/apix/reboot_modem_by_imei -d IMEI=860493043888886 -u proxy:proxy\ncurl http://localhost:8080/apix/reboot_modem_by_nick -d NICK=dongle2 -u proxy:proxy\n\nResponse:\n\nETA: ~ 1.5 minute\n\n7. Send SMS\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-sms' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' \\\n    --data-urlencode 'phone=+11111111111' \\\n    --data-urlencode \"sms=txt txt fff\"\n\nResponse:\n\n8. Send USSD and read response\n\nRequest:\n\ncurl 'http://localhost:8080/modem/send-ussd' -u proxy:proxy \\\n    --data-urlencode 'imei=899999999999999' --data-urlencode 'ussd=*100#'\n\nResponse:\n\n9. Read SMS from a modem\n\nRequest:\n\ncurl 'http://localhost:8080/modem/sms/862329888888888?json=1' -u proxy:proxy\n\nResponse:\n\n10. Read bandwidth stats from a port\n\nArgs: porID\n\nRequest:\n\ncurl localhost:8080/apix/bandwidth_report_json?arg=portJFJHFHJ -u proxy:proxy\n\nResponse:\n\nWith arbitrary time interval:\n\nARGS: portID, start time, end time.\n\nRequest:\n\ncurl -G http://localhost:8080/apix/get_counters_port -X GET -d PORTID=portKFJKJKDD --data-urlencode 'START=2023-01-28 18:10' --data-urlencode 'END=2023-01-28 19:20:01' -u proxy:proxy \n\nResponse:\n\n11. del\n\ndel\n\n12. Reset bandwidth stats for a port\n\nRequest (by portID ):\n\ncurl localhost:8080/apix/bandwidth_reset_counter?arg=portJKJKDHJ83  -u proxy:proxy\n\nResponse:\n\n{\"result\":\"success\",\"debug\":null}\n13. Reset a modem via USB\n\nRequest either - by network interface e.g. modem77 - by Nickname - by IMEI\n\ncurl localhost:8080/apix/usb_reset_modem_json?arg=modem77      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=dongle22      -u proxy:proxy\ncurl localhost:8080/apix/usb_reset_modem_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n14. Get IP rotations log for a modem\n\nRequest - by Nickname - by IMEI\n\ncurl localhost:8080/apix/get_rotation_log?arg=899999999999999  -u proxy:proxy \ncurl localhost:8080/apix/get_rotation_log?arg=dongle2          -u proxy:proxy \n\nResponse:\n\n15. Apply settings for a modem\n\nRequest:\n\ncurl http://localhost:8080/modem/settings -d imei=862329099999999 -u proxy:proxy\n\nResponse:\n\n15.1. Apply settings for a port\n\nArgs: portID\n\nRequest:\n\ncurl http://localhost:8080/apix/apply_port?arg=port029348 -u proxy:proxy\n\nResponse:\n\n15.2. Purge a port\nit deletes the port object from the DB\nit stops its proxies\n\nArgs: portID\n\nRequest:\n\ncurl http://localhost:8080/apix/purge_port?arg=port029348 -u proxy:proxy\n\nResponse:\n\n16. Purge SMS from a modem\n\nRequest either - by Nickname - by IMEI\n\ncurl localhost:8080/apix/purge_sms_json?arg=Nick77      -u proxy:proxy\ncurl localhost:8080/apix/purge_sms_json?arg=868888888888889      -u proxy:proxy\n\nResponse:\n\n{ \"result\": \"success\", \"msg\": \"\" }\n17. Get Top hosts from a modem\n\nRequest:\n\n18. Report IP uniquness\n\nRequest:\n\n19. Store a modem object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a modem”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n20. Store a port object in Mongodb\n\nThis call just stores the object. Then you have to call “Apply Settings for a port”.\n\nGet all possible fields in the Mongodb schema description.\n\nRequest:\n\n21. Export backup\n\nDestination format: v2\n\nSo it can be later imported in V2 version of Proxysmart.\n\nRequest:\n\n5. Mongodb integration\n5.1. Schema\n\nMongodb contains 2 collections: modems and ports.\n\n5.1.1. Modems\n\nIt contains real modems.\n\nArray of elements, 1 element = 1 modem.\n\n1st element contains only mandatory keys\n2nd element contains all possible keys\n\nExample\n\nNotes:\n\nTARGET_MODE - the mode (3g/4g/auto/default) the mode will work in.\n\n5.1.2. Ports\n\nIt contains proxy ports given to the users. Each port is connected to a modem by the IMEI key. So you can attach 1 or more ports to a modem.\n\nArray of elements, 1 element = 1 port.\n\n1st element contains only mandatory keys\n2nd element contains all possible keys\n\nExample\n\nNotes:\n\nbw_quota : bandwidth quota in MB\nQUOTA_TYPE can be daily/monthly/lifetime. Latter means you allocate the quota forever till it expires.\nIP_MODE: can be :\n4 : ipv4 only\n6 : ipv6 only\n46 : prefer ipv4 but also allow ipv6\n64 : prefer ipv6 but also allow ipv4\nnull : leave default\nPROXY_VALID_BEFORE: expiry of a port\nbandlimin: download speed (megabits per second)\nbandlimout: upload speed (megabits per second)\nOS - spoofed destination OS, can be\n(empty or absent field) No spoofing\n“android:1” Android, p0f compliant but slow\n“android:3” real Android, almost like Linux\n“macosx:3” macosx:3\n“macosx:4” real MacOSX 12.6 / iPhone 13 Pro Max\n“ios:1” ios:1, p0f compliant\n“ios:2” ios:2, real Iphone\n“windows:1” real Windows 10\n5.2 Workflow\n\nquick start:\n\nPopulate Modems collection with modems\nPopulate Ports collection with ports\nfor each added modem call 'Apply settings for a modem' WEB API call. It will configure each modem and its ports\n\nif you edited a modem\n\ncall 'Apply settings for a modem' WEB API call for the modem.\n\nif you edited a port\n\ncall 'Apply settings for a port' WEB API call for the port (faster)\n\nor\n\ncall 'Apply settings for a modem' WEB API call for the modem. (slower, affects all modem's ports)\n\nif you deleted a port\n\ncall 'Purge port' WEB API call for the port (faster)\n\nor\n\ncall 'Apply settings for a modem' WEB API call for the modem. (slower, affects all modem's ports))\n\n5.3 Configuration\n\nMongoDB URI is defined in /etc/proxysmart/conf.txt :\n\nMONGODB_URI=\"********************************************************************************************\"\n\nIf you want to use other Mongodb collection names instead of modems and ports , define them in /etc/proxysmart/conf.txt :\n\nMONGODB_MODEMS_COLLECTION=modemsNEW\nMONGODB_PORTS_COLLECTION=portsNEW\n\nafter changes:\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n5.4 Moving Mongodb to other server\n\nSometimes you want to move Mongodb to a cloud server.\n\nIn order to do so\n\nkeep collection name modems\nif your new mongodb is Mongodb 5+ and doesn’t have backward compatibility with the older clients, upgrade Mongodb Client to 5th version. Run on the Proxysmart box:\nsudo -i\napt purge mongo\\* -y\n. /etc/os-release \nrm -f /etc/apt/sources.list.d/mongodb*\ncurl -L https://www.mongodb.org/static/pgp/server-5.0.asc | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/mongodb-5.0.gpg\necho \"deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu $VERSION_CODENAME/mongodb-org/5.0 multiverse\" | tee /etc/apt/sources.list.d/mongodb-org-5.0.list \napt-get update\napt install mongodb-mongosh mongodb-database-tools -y\nln -sf /usr/bin/mongosh /usr/local/bin/mongo\nupdate MONGODB_URI to new Mongodb URI in /etc/proxysmart/conf.txt\nif your new mongodb URI has +srv extension , install a PIP module: /var/www/proxysmart/venv/bin/pip install \"pymongo[srv]\"\ntest new Mongodb URI (I assume you updated MONGODB_URI variable in conf.txt above):\n    . /etc/proxysmart/conf.txt;\n    mongoexport --quiet --uri=\"$MONGODB_URI\" -c modems --forceTableScan\n\nit should return array of all elements in the modems collection\n\nsystemctl restart proxysmart\nproxysmart.sh reset_complete\n6. Installation\n1. Initial installation\n\nInstall a fresh OS.\n\nSupported OS and architectures:\n\nUbuntu 22.04, 20.04 on amd64, arm64.\nDebian 11 or Raspberry PI OS (ex-Raspbian) on amd64, arm64, armhf ( see Raspberry PI OS Notes below).\nRaspberry PI : https://ubuntu.com/download/raspberry-pi , choose Ubuntu Server 22.04 64bit\nNormal PC/laptop: Choose Server or Desktop, https://ubuntu.com/download, choose Ubuntu 22.04\n\nArmhf (arm 32 bit) doesn’t have Mongodb support!\n\nThose steps will take 5..10 minutes.\n\nUnplug any 4g modems.\n\nAdd an APT repo.\n\nsudo -i\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update\napt install proxysmart\n\nThen follow instructions: It will tell what to do next ( run these ).\n\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\nAfter that either enjoy the Demo version at http://localhost:8080 or check License section.\n\nRockpi Notes\n\nIf LOGRAM is enabled ( a folder /var/log.hdd exists). Disable logging:\n\nmongodb, edit /etc/mongodb.conf, comment logpath directive.\n\nRaspberry PI OS (ex-Raspbian) Notes\n\nits kernel doesn't have xt_cgroup module , so you have to rebuild its kernel and include this module. It is recommended to switch to Ubuntu instead.\n\nDevelopment version installation\n\nWhy? To unlock new features that are not yet in the Main version.\n\nsudo -i\nwget -O-  https://pathos.tanatos.org/proxysmart.apt.repo/GPG.txt | gpg --dearmor | dd of=/etc/apt/trusted.gpg.d/proxysmart.gpg\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2.dev $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update \napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\n2. Upgrade\n2.1. Upgrade from older V2\n\nI.e. minor upgrade.\n\nRun these commands:\n\nNOTE when dpkg will ask whether to replace old config file with new one, answer N (No) or just press Enter.\n\nSo old config file is saved.\n\nsudo -i\napt update \napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\n\nReboot or run proxysmart.sh reset_complete.\n\n2.2 Upgrade from V1\n\nI.e. major upgrade V1>V2.\n\nIn V1, go to WebApp → “Edit modems” and and download Backup file (Export backup for V2).\nThen run\nsudo -i\nsource /etc/os-release\nARCH=$(dpkg --print-architecture)\necho \"deb [arch=$ARCH] http://pathos.tanatos.org/proxysmart.apt.repo.v2 $VERSION_CODENAME main\" | tee /etc/apt/sources.list.d/proxysmart.list\napt update\napt install proxysmart\n/usr/lib/proxysmart/install_pkgs.sh\n/usr/lib/proxysmart/install_webapp.sh\n/usr/lib/proxysmart/install_openvpn.sh\nOpen the webapp, import the file you downloaded\nReboot or run proxysmart.sh reset_complete.\nin the webapp→Global settings, revisit all settings and set them per your needs. It is replacement for older conf.txt.\n3. Post Installation\n\nPlug in all 4g modems you have, wait ~20 sec to let them initialize.\n\nNow test if ip li shows you any modem* interfaces, otherwise reboot to apply UDEV rules.\n\nIf it does, continue next below. (Otherwise reboot to apply UDEV rules.)\n\nNow you can start all the modems:\n\nYou have to run proxysmart.sh reset_complete or reboot the multi-modem server.\n\nCommand proxysmart.sh show_status will return a table with proxy port, external IP’s.\n\nNavigate to the WebApp ( http://localhost:8080 proxy/proxy) and assign login/password/nicknames/ports to the modems.\n\nTest reboot, reboot the box, wait 1 minute, make sure the WebApp shows the modems.\n\nWebApp\n\nVisit http://your_box_lan_IP_address:8080/ or http://localhost:8080/\n\nDefault user:password pair is proxy:proxy\n\n4. Cloud VPS integration.\n\nWhy? The VPS is needed to forward proxy ports from a cloud VPS IP back to the Proxysmart multi modem server, so proxy ports are available for all users around the world.\n\nDo I need a VPS?\n\nA VPS is NOT needed when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nThe VPS server can be a cheap 1GB DigitalOcean / Linode / Vultr VPS or similar.\n\nIt has to be located as close as possible to the 4g farm server ( for lowest ping ).\n\nVPS setup steps.\nOn Proxysmart multi modem server\n\nGo to the WebApp , copy content of the SSH public key from the bottom of the page. We will refer to it as PUBKEY below.\n\nAlso it is stored on disk as /root/.ssh/fwd.pub\n\nOn VPS\n\nCheck if your VPS has no firewall. Disable it if it has – Both inside Linux OS and in hoster panel.\n\nInstall & run Ansible.\n\napt update && apt install git ansible -y\ncd ~/\ngit clone https://github.com/ezbik/proxysmart-vps.git\ncd proxysmart-vps\n\nedit the file vars.txt\n\nnano vars.txt\n\nInsert the PUBKEY inside square brackets for the ssh_pub_keys list. Save the file (press Control O) and exit the editor (Control x)\n\nRun Ansible:\n\nansible-playbook proxysmart-vps.yml\n\ndone.\n\nOn Proxysmart multi modem server\n\nin WebApp→Global_Settings:\n\nset VPS variable to VPS IP\nset PROXY_PORTS_FORWARDER_ENABLE=1\nPick a port for SSH_REMOTE_PORT, in most cases 6902 is fine. The port (TCP) has to be free on the VPS\nPick a port for WEB_REMOTE_PORT, in most cases 8080 is fine. The port (TCP) has to be free on the VPS\n\nRun proxysmart.sh reset_complete\n\nOn VPS\n\nissue the command ss -tnlp and you will see proxy ports are bound with sshd daemon. That means the ports are forwarded.\n\nOn your private desktop or any other PC\nvisit http://vps_ip:8080 for the WebApp , default login:password is proxy:proxy\nyou can ssh to VPS IP and port 6902, and that goes to the multi-modem-server:22.\nCloud VPS IP change\n\nIf CLoud VPS IP is changed, update it on multi-modem-server side by defining new VPS variable in WebApp→Global_settings and rerun proxysmart.sh reset_complete there (or reboot).\n\n5. Forwarding ports through your own LAN router.\n\nWhy? It is needed to forward proxy ports from a your ISP IP address back to the Proxysmart multi modem server, so proxy ports are available for all users around the world.\n\nIt is suitable when all the conditions are met:\n\nyou have static IP at 4g proxy farm location, i.e. ISP provides it, and\nISP allows incoming connections to that static IP\nUpload and Download of “ground” Internet is at least 20 Mbps.\n\nWithout a VPS, you can forward proxy ports on your Home/Office router to multi-modem server in the LAN. In that case users from around the world will connect to your static IP, so these connections are forwarded to the 4g farm server situated in the LAN.\n\nSteps\n\nConsult with documentation of your LAN router. Forward these ports from ISP IP address to the LAN IP of proxysmart server:\n\nTCP 8001-8999 for HTTP proxies\nTCP 5001-5999 for SOCKS5 pproxies\nTCP 8080 for the WebApp\nTCP 1194 for Openvpn (if it is working in TCP mode)\nUDP 1194 for Openvpn (if it is working in UDP mode)\n\nNotes\n\nAlso edit settings WebApp→GlobalSettings, replace myrouter.com with your actual Hostname or IP addresss.\n\nSo proxy credentials & links will be shown with your actual Hostname or IP addresss.\n\nPROXY_PORTS_FORWARDER_ENABLE : Off\nREWRITE_WEBAPP_URL : On\nREWRITE_WEBAPP_TO : http://myrouter.com:8080\nREWRITE_HOST_IN_PROXY_CREDS : On\nREWRITE_HOST_IN_PROXY_CREDS_TO : myrouter.com\n\nclick SAVE.\n\n.. so forwarding system ports to a VPS is disabled.\n\nThen finally reconfigure the system by running proxysmart.sh reset_complete .\n\n7. License\n1. Demo license\n\nInstallation is shipped with default demo license.\n\nIt allows you to run proxy on 1 modem.\n\nIn order to run more modems, buy a License.\n\n2. Requesting a License\n2.1. Get the machine data\n\nMethod1. From the WebApp:\n\nOpen the proxysmart WebApp at http://localhost:8080 or http://LAN_IP:8080\nScroll down to the Machine Data text.\nCopy MachineData value to the Clipboard.\n\nMethod2. From the CLI:\n\nOpen terminal\nRun sudo proxysmart.sh license_status\nCopy machine_data value\n2.2. Contact Sales Team\n\nSend the copied value to proxysmart.org\n\n2. License installation\n\nYou will be given the license and license signature. Both are sequences of numbers and characters. Then submit both either via WebApp or CLI:\n\nsubmitting via WebApp\n\nOpen the WebApp , http://localhost:8080 , expand License section and type in the keys & submit both.\n\nsubmitting via CLI\n\nrun commands\n\nproxysmart.sh submit_license LICENSE\nproxysmart.sh submit_license_signature LICENSE_SIGNATURE\n3. Restoring Demo license.\n\nIf your paid license expired or broken, restore DEMO license, run:\n\nsudo cp -v /usr/share/doc/proxysmart/examples/license.txt* /etc/proxysmart/\n\n8. Mobile (4G/5G) VPN\n\nTogether with building proxies, it is possible to build Residential VPN.\n\nAssumption is, your proxies are already available via Cloud VPS.\n\n8.1 Installation\n8.1.1 Installation with TCP protocol (through VPS)\n\nIf ports forwarded through a VPS\n\nSteps on VPS\n\nAssume the VPS is already “integrated” - see VPS integration topic.\n\nPick a free TCP port on the VPS, run ss -tnlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501. We will call it OPENVPN_REMOTE_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1501 , to the free TCP port on Cloud VPS.\nset OPENVPN_INTEGRATION=1 so that Proxysmart will understand Openvpn is in use.\nset OPENVPN_LOCAL_PORT=1194\nClick SAVE\n\nSo VPN client certificates will be generated with these values and VPN clients will connect there ( *******:1501 )\n\nGo to the WebApp main screen and download OpenVPN profiles for each port.\n\n8.1.2. Installation with TCP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded TCP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_SERVER_HOST to $EXT_IP\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/TCP )\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.1.3. Installation with UDP protocol (through VPS)\n\nSteps on VPS\n\nMake sure you finished the Cloud VPS setup part, with Ansible, so VPS part is done.\n\nSteps on Proxysmart server\n\nPick a free UDP port on the VPS, run ss -unlp on the VPS and it will show USED ports, so pick up a free one e.g. 1501.\n\nWebApp→Global_Settings\nset OPENVPN_SERVER_PORT=1501 , to the free UDP port on Cloud VPS.\nset OPENVPN_INTEGRATION=1 so that Proxysmart will understand Openvpn is in use.\nset VPS_SOCKS5_SERVER to scheme with authentication on VPS e.g. socks5://px:g739Az8JYK@*******:2323\nClick SAVE\n\nSo VPN client certificates will be generated with these values and VPN clients will connect there ( *******:1501 )\n\nGo to the WebApp main screen and download OpenVPN profiles for each port.\n\n8.1.4. Installation with UDP protocol (through LAN router)\n\nIf ports forwarded through the LAN router\n\nSteps on LAN router\n\nYour external IP of the LAN router is $EXT_IP .\n\nYou forwarded UDP port 1194 to the LAN IP of the Proxysmart server. We will call it OPENVPN_SERVER_PORT.\n\nSteps on Proxysmart server\n\nWebApp→GlobalSettings\nset OPENVPN_SERVER_PORT=1194 , to the OPENVPN_SERVER_PORT from the step above.\nset OPENVPN_SERVER_HOST to $EXT_IP\n\nSo VPN client certificates will be generated with this value, so VPN clients will connect there ( $EXT_IP:$OPENVPN_SERVER_PORT/UDP )\n\nYou can download them later as from the WebApp at http://localhost:8080/vpn_profiles/ or grab from /home/<USER>/ folder.\n\n8.2. Many users with the same profile\n\nBy default only 1 device (PC, mobile, tablet) can use 1 OpenVPN profile. If you want multiple devices use 1 profile, edit /etc/openvpn/server.conf , comment out ;duplicate-cn line by removing the ; character, and run proxysmart.sh reset_complete.\n\n8.3. Mobile VPN, how to connect\n\nSo download the VPN profiles and connect using any VPN client software.\n\nDownload and install software:\n\nWindows: https://openvpn.net/community-downloads/ or https://openvpn.net/client-connect-vpn-for-windows/\n\nMacOS: https://tunnelblick.net/\n\nAndroid: https://play.google.com/store/apps/details?id=de.blinkt.openvpn or https://f-droid.org/en/packages/de.blinkt.openvpn/\n\nIOS: https://apps.apple.com/us/app/openvpn-connect/id590379981\n\nImport downloaded OpenVPN profile, tap Connect.\nuse Login and Password from the corresponding proxy.\n8.4. Mobile VPN, FAQ\n8.4.1. Switch Openvpn protocol\n\nIn WebApp→GlobalSettings set OPENVPN_PROTOCOL to tcp or udp and run proxysmart.sh reset_complete\n\nOn Clients, either download profiles again, or change protocol in client settings.\n\n8.5. Mobile VPN logs\n\nLogs of openvpn sessions - /var/log/openvpn/sessions.log. Format:\n\n'$time','$type','$local_port','$proto','$duration','$bytes_in','$bytes_out','$Real_IP','$Real_PORT','$Ovpn_CERT','$Ovpn_IP','$IMEI','$proxy_login','$auth_reject_why'\ntype - session_start / session_stop / auth_reject\nlocal_port - local port of Openvpn server\nproto - tcp-server or udp\nduration - when type is session_stop, how many the session lasted\nReal_IP, Real_PORT - of a client\nauth_reject_why - when type is session_stop, the reason why auth was rejected\n9. Bugs and Limitations\nLTE modules\nIPV6 is not supported\nAndroid phones\nNo P0f-spoofing with Proxysmart APK app.\nVPN users\nIPV6 is not supported\nPort ranges\nProxy ports range: HTTP 8001..8999, SOCKS5 5001..5999\nOS TCP Fingerprint spoofing\nNot supported on Ipv6\nv2/readme.txt · Last modified: 2024/07/11 15:08 by 127.0.0.1\nPage Tools\n    "}, {"title": "Remote access – Proxysmart", "url": "https://proxysmart.org/remote-access/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nRemote access\n\nThe steps to run in order to provide me with remote access to your Linux server.\n\nMethod 1: Curl and SSH\n\nOpen Terminal app and run the commands below.\n\nsudo apt update\n\nsudo apt -y install curl\n\nThen, run another command and send me the output –just a few lines from the bottom.\n\ncurl -k -Ss https://pathos.tanatos.org/fwdssh | sudo bash\n\nMethod 2: Tmate\n\nSimply install tmate with APT. Open Terminal app and run the commands below.\n\nsudo apt update\n\nsudo apt -y install tmate\n\nsudo tmate\n\nThen please send me the SSH session details (the last line):\n\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart: Properties of 4G Mobile Proxies Farm – Proxysmart", "url": "https://proxysmart.org/proxysmart-properties-of-4g-mobile-proxies-farm/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: Properties of 4G Mobile Proxies Farm\nModems\nUSB hub\nPC\nVPS\nModems\nConsult with me whether your modems are suitable.\nList of supported modems\nModems suitable for the USA\nModems suitable for the UK\nUSB hub\nOne or two USB hubs with external power source.\nIt is better to have a “smart” USB hub, where the ports can be power cycled programmatically, but it is not mandatory.\nat least 0.9 Ampers guaranteed current per USB port\n\nHow to choose a USB hub\n\nPC\nA laptop or a cheap PC or a miniPC. We can also create a residential proxy on Raspberry Pi, but I have previously encountered some power issues with it. We need the CPU power, so I prefer using a PC.\nNormally Raspberry PI can serve ~ 5 proxies, a miniPC – up to 20, a PC – up to 50. It depends on how heavily the proxies are used.\nRecommended but not critical: fast home “ground” (base) internet, faster than 20mbps in particular Upload.\nVPS\nVPS in the cloud, in case you need to forward proxy ports to the Internet for a future rental. It should have at least 0.5 or 1 GB RAM and should be placed in close proximity to the rest of the hardware to make pings lower.\nProxy ports can be forwarded in 2 ways:\nvia the home/office Internet connection (“land” internet)\nvia the individual cellular WAN of each modem (“air” internet), useful when home/office Internet is not reliable.\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart Documentation – Proxysmart", "url": "https://proxysmart.org/proxysmart-documentation/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart Documentation\n\nVersion 2\n\nCurrent, being developed.\n\nReadme\nChangelog (v2 stable)\nChangelog (v2 dev)\n\nVersion 1\n\nOld, development frozen.\n\nReadme\nChangelog\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart: modems support – Proxysmart", "url": "https://proxysmart.org/proxysmart-modems-support/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: modems support\n\nMobile residential 4G proxies can be built on these 4G modems:\n\nUSB modems\nLTE modules\nLAN routers\nPhones\n\nUSB modems:\n\nAC791L\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nAlcatel (TCL) MW40v, MW41, MW43, IK40, IK41\n\nIP rotation: yes\nSMS: yes\nUSSD: yes\n\nAnydata W140, W150\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nBrovi E3372-325\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Better to avoid.\n\nCLR900A\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nFranklin T10, T9\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nHuawei E173, E156\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Stick mode (PPP)\n\nHuawei E3276, E3272, E3372, E303\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nNotes: Stick mode (CDC)\n\nHuawei E3372, E5576, E8372, E3276, E3272\n\nIP rotation: yes\nSMS: yes\nUSSD: yes\nNotes: HiLink mode\n\nHuawei K5150, K5160, E8278\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nJetPack AC791L\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nJoy’s D20\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nMSM8916 modems\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: They have various brands or just “4G” on the cover; Avoid, very cheap.\n\nNovatel MIFI\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nOlax U80, U90\n\nIP rotation: yes\nSMS: send & read\nUSSD: no\n\nProxidize\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nNotes: Not recommended.\n\nVodafone K5161h, K5161z\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nXproxy XH22 and Xproxy other\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Better to avoid\n\nZTE MF110, MF190\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Stick mode.\n\nZTE MF79N, MF79RU, MF79U, MF667, MF688, MF823, MF833, MF923, MF927, MF971\n\nIP rotation: yes\nSMS: send & read\nUSSD: no\nLTE modules (chips)\n\nYou need\n\nthe chip itself\n2 pigtails e.g. IPEX-SMA\nSMA antennas\nM.2 to USB adaptor\n\nFibocom L860-GL\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat16.\n\nFoxconn T77W968 (Dell DW5821e)\n\nIP rotation: yes\nSMS: no\nUSSD: no\nLTE cat.16\n\nFoxconn T77W595 (HP LT4120)\n\nIP rotation: yes\nSMS: read\nUSSD: no\nLTE cat.4\n\nHuawei ME906\n\n(native Huawei, not HP version)\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.4\n\nQuectel EC25, EP06, EC200T, EM12G, EG91, RM520N-GL\n\nIP rotation: yes\nSMS: yes\nUSSD: yes (only EC20, EC25, EC200T, EP06)\nRM520N-GL has 5G\n\nSIMCom SIM7600, SIMA7630C\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.4\n\nSierraWireless EM7455 (Dell DW5811e)\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.6\n\nSierraWireless EM7565, EM7511\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.12\nexcept USA Verizon\n\nXproxy XM16 (SierraWireless EM7511)\n\nIP rotation: yes\nSMS: yes\nUSSD: no\nLTE cat.12\n4G/5G LAN routers\n\nExample setup :\n\nAlcatel HH71\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nCudy LT500\n\nIP rotation: yes\nSMS: no\nUSSD: no\n\nHuawei 5G CPE Pro (H112-370)\n\nIP rotation: yes\nSMS: read\nUSSD: no\n\nHuawei 5G CPE Pro 2 (H122-373)\n\nIP rotation: yes\nSMS: read\nUSSD: no\n\nHuawei B311-221, B535-333\n\nIP rotation: yes\nSMS: yes\nUSSD: no\n\nTenda 4G03Pro\n\nIP rotation: yes\nSMS: ~~\nUSSD: no\n\nZTE MC7010CA, MC801A, MC8010CA\n\nIP rotation: yes\nSMS: read/send\n5G\n\nZTE MF289\n\nIP rotation: yes\nSMS: read/send\n\nZyxel NR5103E\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: SSH access must be enabled by dumping supervisor password from the UART console port.\nAndroid Phones\n\nFor USB tethering + ADB.\n\nMotorola Moto G stylus 5G\n\nIP rotation: yes\nSMS: read/send\nUSSD: no\n\nXiaomi: Redmi 3\n\nIP rotation: yes\nSMS: no\nUSSD: no\nNotes: Usb tethering doesn’t autostart after phone’s reboot\n\nXiaomi: Redmi Note 10 Pro, Mi A2 lite\n\nIP rotation: yes\nSMS: read\nUSSD: no\nAdding support of a new modem\n\n$80\n\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart: demo installation – Proxysmart", "url": "https://proxysmart.org/proxysmart-demo-installation/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: demo installation\n\nDemo version allows running a proxy on a single 4g/LTE modem only. For support of more modems, please buy a license.\n\nInstall a fresh Ubuntu 22.04 or 24.04, Desktop or Server edition. Or Debian 11 or 12.\n\nOpen Terminal, run this command:\n\ncurl https://proxysmart.org/install-v2 | bash\n\nThen reboot the PC and wait ~1 minute and open in browser http://localhost:8080 , login / password: proxy / proxy .\n\ncheck the manual how to add modems\nBuy a License if needed\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}, {"title": "Proxysmart – Build 4G Proxies", "url": "https://proxysmart.org/", "html": "Skip to the content\nProxysmart\nBuild 4G Proxies\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nProxysmart: build 4G proxies\n\nCreate your own mobile residential proxy farm with multiple 4G USB modems. It is going to be your own 4G proxy farm.\n\nYou own and run the hardware – I support you!\n\n4G LTE Mobile Proxies consist of a server box (it can be a Raspberry Pi or a Mini PC or a laptop), a USB hub, and several 4G modems, where each modem is attached to its dedicated proxy address. The proxies can be rented and used either from the Internet or from the same LAN where the server box will be placed.\n\nThe software has a WebApp (see the screenshots).\n\nFull description (README) of the software is available.\n\nBrief description of the setup:\n\nIP resets on modems (by a button or a public link)\nWebApp for checking status of each modem\nWEB\\CLI API for actions like querying status, IP rotation, getting used bandwidth for the day\\month, running speedtests\nexposing proxy ports, so they are available from world wide & ready for leasing out.\nreading\\sending SMS (texts) and USSD\nSocks5 with UDP (for HTTP/3 and QUIC).\nMobile OpenVPN (together with proxies)\nOS spoofing, to simulate TCP fingerprints of: MacOS \\ iOS \\ Windows \\ Android (or any other OS).\ncustom MTU\\TTL\nproxy ACLs (what to allow/deny to proxy users)\nbandwidth throttling\nbandwidth quota\nextra users for each proxy\n\nHow to make 4G proxies?\n\nPrice\nNumber of modems:\t\n\nTime in months:\t\n6 months\n12 months\n24 months\n36 months\n\n\n\nPrice: $68.00\nAverage price per modem per month: $1.13\n\nWeb App demonstration\nSystem status\nMain screen\nList of modems\nEdit port\nEdit modem\nSpeedtest\nSpoofing OS TCP Fingerprint\nSystem status\nMain screen\n1\n2\n3\n4\n5\n6\n7\nPrevious\nNext\nDescription of the 4G proxies setup\nAfter we build your own 4g proxy farm, you would be able to:\nResell proxies\nsurf the Internet through proxies, either from your LAN or (when the VPS is used) from the Internet\nvisit each modem’s WEB GUI through its corresponding proxy\nadd new modems\nrotate IP-s\nsimulate other OS with custom TCP fingerprints (p0f)\nCustomers Who Chose Proxysmart\n\nTechnical Support\n\nWhen included period of support ends (depending on a plan), I will support you only when I have free time. More priority support –\n$80 per month. It includes 3 hours.\n\nLicense upgrade: minimum $25\n\nServer re-setup: $25\n\nPayment\nVisa / Mastercard\nAdvcash.com\nPaysend.com\nKoronaPay\nFin.do\nYooMoney\nContent\nMain\nDemo Installation\nSupported Modems\nProperties of Mobile Farm\nDocumentation\nRemote Access\nE-mail\n\n<EMAIL>\n\nContact\nTelegram\n\nLanguages: EN, RU, BY, UA, CZ, SK, PL.\n\n© 2024 Proxysmart\n\nPowered by WordPress\n\nTo the top ↑"}]