# **Integrating the Novatel USB551L Modem for SMS Messaging with Python**

**1\. Introduction:**

The Novatel USB551L modem, also marketed as the Ovation MC551, is a Verizon 4G LTE USB modem designed to provide mobile broadband connectivity.1 This document addresses the need for a detailed technical guide on integrating this particular modem with a Python application to utilize its Short Message Service (SMS) capabilities. The integration aims to enable users to send, receive, and forward SMS messages programmatically. The objectives of this report are to serve as a comprehensive resource, detailing the modem's SMS features, the essential AT commands required for operation, the necessary driver installation procedures across various operating systems, the implementation of SMS functionality using the Python library PySerial, techniques for continuous monitoring of incoming messages, and methods for automated SMS forwarding to other destinations. This guide is intended for developers and engineers who require a practical and in-depth understanding of how to incorporate the Novatel USB551L modem into their Python-based projects to achieve SMS functionality.

The Novatel USB551L, while considered an older model in the realm of 4G LTE modems, remains a viable option for projects specifically requiring SMS functionality over a cellular network. Its potential availability at a more economical price point compared to newer, more feature-rich modems 4 could make it a preferred selection for applications where budget constraints are a significant factor. Furthermore, the strong association of the modem with the Verizon network and the frequent mention of Verizon branding and the VZAccess Manager software 2 in the research material suggests a close operational relationship with the Verizon ecosystem. This might imply that certain configurations or limitations could arise when attempting to use the modem with alternative carriers, necessitating careful investigation into cross-carrier compatibility for projects intending to operate outside the Verizon network.

**2\. Novatel USB551L Modem: Technical Overview and SMS Capabilities:**

The Novatel USB551L modem supports connectivity across several network technologies, including 4G LTE on the 700 MHz Band 13 and 3G CDMA on the 800/1900 MHz bands.2 In terms of data transmission speeds, the modem is capable of achieving LTE download speeds of up to 12 Mbps and upload speeds of up to 5 Mbps. When operating on the 3G CDMA network (EV-DO Rev.A), it supports download speeds of up to 1.4 Mbps and upload speeds of up to 800 kbps.3 It is important to note that these speeds represent the theoretical maximum and actual data rates experienced by users can fluctuate depending on various factors such as network load and signal strength. The modem is built around the Qualcomm MDM9600 chipset 2 and offers broad operating system compatibility, including support for Windows (XP, Vista, 7), Linux, and Mac OS.2 It interfaces with host devices via a standard USB Type A 2.0 connector.2 For optimal performance, the modem incorporates an internal advanced proprietary antenna design and also includes an external antenna port that utilizes a TS9 connector.3 The physical design of the Novatel USB551L is characterized by its slim, durable, and compact form factor 2, making it easily portable.

Critically for the user's request, the Novatel USB551L modem supports two-way text messaging, confirming its capability to both send and receive SMS messages.2 However, it is important to note certain functional limitations of the device. Specifically, the modem does not support GPS or dial-up connections.2 Furthermore, the modem requires a 4G SIM card to operate.2

The modem's support for both 4G LTE and 3G networks provides a degree of operational versatility, allowing it to connect in areas with differing levels of network infrastructure. However, the primary LTE band supported, Band 13 (700 MHz), is predominantly used by Verizon in the United States.2 This concentration on a specific band might restrict the modem's immediate usability in other geographic regions or with different carriers without a thorough investigation into the compatibility of frequency bands and network protocols. The absence of integrated GPS functionality 2 is a significant constraint for applications that require location awareness. If the intended project necessitates tracking or location-based services, developers will need to consider incorporating a separate GPS module to fulfill these requirements, as the Novatel USB551L itself does not provide this capability.

**3\. Understanding AT Commands for SMS Control:**

AT commands, short for "Attention" commands, form a standardized communication protocol used to interact with modems and other communication devices over a serial interface.10 These commands are essential for configuring the modem, initiating actions such as sending SMS messages, and retrieving information like received messages. AT commands are broadly categorized into basic commands, which typically do not begin with a "+", and extended commands, which do.10 For SMS operations, we primarily utilize extended AT commands.

The following table outlines the essential AT commands relevant to controlling SMS functionality with the Novatel USB551L modem:

**Table 1: Essential AT Commands for SMS Operations with Novatel USB551L**

| Command | Description | Syntax | Example |
| :---- | :---- | :---- | :---- |
| AT+CMGF=\<mode\> | Sets the SMS message format (0 for PDU, 1 for text mode). | AT+CMGF=1 | Sets the modem to text mode. |
| AT+CMGS=\<da\>\[,\<toda\>\]\<CR\>\<text\>\<ctrl-Z\> | Sends an SMS message to the specified destination address. | AT+CMGS="+1234567890"\<CR\>Hello\!\<ctrl-Z\> | Sends "Hello\!" to \+1234567890. |
| AT+CMGR=\<index\> | Reads an SMS message from the specified index in storage. | AT+CMGR=1 | Reads the message at index 1\. |
| AT+CMGL=\<status\> | Lists SMS messages based on their status (e.g., "REC UNREAD", "ALL"). | AT+CMGL="REC UNREAD" | Lists all unread received messages. |
| AT+CMGD=\<index\>\[,\<delflag\>\] | Deletes SMS messages from storage based on index and optional delete flag. | AT+CMGD=2 | Deletes the message at index 2\. |
| AT+CSCA=\<sca\>,\<tosca\> | Sets the SMS service center address and its type of address. | AT+CSCA="+19037029920",145 | Sets the SMSC address to \+19037029920. |
| AT+CPMS=\<mem1\>\[,\<mem2\>\[,\<mem3\>\]\] | Selects preferred message storage (e.g., "SM" for SIM, "ME" for modem). | AT+CPMS="SM","SM","SM" | Sets the preferred storage to the SIM card for all operations. |
| AT+CNMI=\<mode\>\[,\<mt\>\[,\<bm\>\[,\<ds\>\[,\<bfr\>\]\]\]\] | Configures new message indications to the terminal equipment. | AT+CNMI=2,2,0,0,0 | Enables indications of new received SMS messages. |

The syntax for these commands generally involves sending the command string followed by a carriage return character (\\r). For the AT+CMGS command, after sending the command with the recipient's phone number, the modem will typically respond with a prompt (e.g., "\>"), after which the message text can be sent, terminated by a Ctrl+Z character (ASCII 26). Reading responses from the modem is crucial for confirming the successful execution of commands and for retrieving message content.

In addition to these standard GSM AT commands, the Verizon U620L AT Command Reference Guide 14 lists several other AT commands, some of which are specific to Verizon and begin with AT$. While this documentation is for a different Verizon modem (U620L), it is likely that some of the SMS-related commands or similar functionalities exist for the USB551L, given that both are Verizon-branded modems. For instance, the U620L guide details commands like AT$NWSMSIMSFORMAT for managing SMS format over IMS, as well as the standard AT+CMGD, AT+CMGF, AT+CMGL, AT+CMGR, AT+CMGS, AT+CMGW, AT+CMSS, AT+CSCA, AT+CSCS, AT+CSMP, and AT+CSMS.14 These commands cover a wide range of SMS operations, including sending, receiving, deleting, and managing message settings. While the basic SMS tasks can likely be accomplished using the standard GSM AT commands, exploring the Verizon-specific commands for the USB551L, if documentation is available, could potentially unlock more advanced features or provide more granular control over the modem's behavior within the Verizon network. Configuring the SMS service center address (AT+CSCA) is a network-dependent setting 16, meaning the correct address for the Verizon network (or any other network the modem is used on) must be configured for successful SMS sending. Incorrectly setting the SMSC can result in the failure of messages to be delivered.

**4\. Driver Installation and Operating System Compatibility:**

To ensure the Novatel USB551L modem functions correctly with a computer system, appropriate drivers must be installed, and the installation process can vary depending on the operating system in use.

For **Windows** operating systems, specific USB drivers are required. These drivers are often included within the VZAccess Manager software, which is typically provided by Verizon.4 Users have noted that locating and installing the correct version of VZAccess Manager is critical, as different versions may contain the necessary drivers for the USB551L.4 Novatel also offers Windows USB drivers directly on their support website.17 The process of finding and installing these drivers usually involves downloading the software package and following the on-screen installation instructions. After installation, the modem will typically be recognized as a serial communication port (COM port) in the Windows Device Manager.

On **Linux** systems, the Novatel USB551L might be supported by built-in kernel modules such as cdc\_ether and usb\_serial\_option.20 These modules often enable the system to recognize the modem as both a network interface and a series of serial ports (typically under /dev/ttyUSB\* or /dev/ttyACM\*). Some Linux distributions might require the installation of additional packages, such as python-serial, which is a dependency for the vzwctl.py script mentioned in the research material for connecting to CDMA networks.20 The option driver is also frequently associated with USB modems on Linux.21 Users can typically identify the modem's device files by using commands like lsusb to list USB devices and ls /dev/tty\* to list serial ports.

For **Mac OS**, the necessary drivers are generally included as part of the VZAccess Manager software for Mac.22 Similar to Windows, installing VZAccess Manager on macOS usually handles the driver installation process, allowing the operating system to communicate with the modem.

VZAccess Manager plays a significant role in managing the connection to the cellular network and often installs the required drivers across different operating systems.2 In some instances, it might also be necessary to use VZAccess Manager for the initial provisioning or activation of the modem on the network.20 However, it's worth noting that some users have reported encountering issues with the stability and functionality of VZAccess Manager.25 Once the drivers are installed, identifying the correct serial port for the modem is crucial for establishing communication using PySerial. On Windows, this will be a COM port number (e.g., COM3, COM4). On Linux, it will typically be a device file path under the /dev directory, such as /dev/ttyUSB0 or /dev/ttyACM0.12 The specific port number or device file name can vary depending on the system configuration and the order in which USB devices are connected.

The reliance on VZAccess Manager for driver installation, especially on Windows and macOS, can present challenges if the appropriate version of the software is not readily available or if it exhibits compatibility problems with the specific operating system version in use.4 This situation underscores the potential need for developers to seek alternative driver sources or to understand manual driver installation procedures to overcome issues with VZAccess Manager. For Linux users, the existence of specific kernel modules like cdc\_ether, usb\_serial\_option, and option 20 indicates a different driver management paradigm compared to Windows and macOS. On Linux, the modem might be recognized as both a network interface and a serial device, potentially offering more flexibility in how the device can be utilized. Furthermore, for newer Linux kernels (version 3.4 or later), tools like libqmi and ModemManager provide an alternative to traditional AT commands by using the native QMI protocol for communication.20 This approach could offer more robust and efficient communication for Linux-based applications.

**Table 2: Driver Information for Novatel USB551L Modem**

| Operating System | Recommended Driver Source | Key Software/Utilities | Notes/Considerations |
| :---- | :---- | :---- | :---- |
| Windows | VZAccess Manager (Verizon), Novatel Support Website | VZAccess Manager | Finding the correct version of VZAccess Manager is crucial. Novatel drivers might need manual installation. Modem appears as a COM port. |
| Linux | Built-in kernel modules (cdc\_ether, usb\_serial\_option) | lsusb, ls /dev/tty\* | Often recognized automatically. Might require python-serial for PySerial. Modem appears as /dev/ttyUSB\* or /dev/ttyACM\*. Consider libqmi and ModemManager for newer kernels. |
| Mac OS | VZAccess Manager (Verizon) | VZAccess Manager | Drivers are typically bundled with VZAccess Manager for Mac. Modem appears as a serial port. |

**5\. Integrating with Python using PySerial:**

PySerial is a widely used Python library that facilitates serial port communication, making it an essential tool for interacting with hardware devices like the Novatel USB551L modem.12 To begin using PySerial, it must first be installed. This can be easily done using pip, the Python package installer, with the command pip install pyserial.34

Once PySerial is installed, establishing a serial connection to the Novatel USB551L modem involves several steps within a Python script. First, the serial library needs to be imported. Next, the correct serial port for the modem must be identified based on the operating system (e.g., 'COM3' on Windows or '/dev/ttyUSB0' on Linux). A serial.Serial object is then created, which takes the port name as its primary argument, along with other parameters such as the baud rate. Common baud rates used for communicating with modems include 9600, 115200, and sometimes higher rates like 460800\.12 The appropriate baud rate for the Novatel USB551L might need to be determined through the modem's documentation or by trial and error. It is also important to set a timeout value for serial operations to prevent the script from hanging indefinitely if the modem does not respond as expected. After creating the Serial object, the serial port must be explicitly opened using the ser.open() method. It is crucial to ensure that the port is properly closed using ser.close() when the communication is complete to release the resources.

Interacting with the modem involves sending AT commands and reading the responses. AT commands are sent to the modem as byte strings, typically encoded in ASCII and terminated with a carriage return character (\\r). This can be achieved using the ser.write(command.encode() \+ b'\\r') method. After sending a command, the application needs to read the modem's response. PySerial provides several methods for reading data from the serial port, including ser.readline(), which reads a line terminated by a newline character (\\n), ser.read(n), which reads up to n bytes, and ser.read\_until(expected=b'\\n'), which reads until a specific byte sequence is encountered.12 The choice of method depends on the expected format of the modem's response. It's important to be aware of potential variations in line endings (\\n or \\r\\n) and to account for possible delays in the modem's responses.12 Setting an appropriate timeout when creating the Serial object is essential to prevent the Python application from becoming unresponsive while waiting for data from the modem.12

Given the range of baud rates observed in the research material (9600, 115200, 460800\) 12, the precise baud rate required for communication with the Novatel USB551L might necessitate some experimentation or a review of the modem's specific technical documentation. Using an incorrect baud rate will likely result in unintelligible data being exchanged between the computer and the modem. Furthermore, when working with the serial port, it is crucial to handle data as bytes. AT commands and SMS message content, although text-based, need to be encoded into bytes before being sent over the serial connection and decoded back into strings upon reception (e.g., using methods like .encode('ascii') for commands and .decode('utf-8') for message content).12 Selecting the correct encoding is vital, particularly for SMS messages that might contain characters beyond the standard ASCII character set.

**6\. Implementing SMS Functionality in Python:**

Implementing SMS functionality with the Novatel USB551L modem using Python and PySerial involves sending specific AT commands in the correct sequence. To send an SMS message, the modem first needs to be set to text mode using the command ser.write(b'AT+CMGF=1\\r'). Once in text mode, the command to send an SMS is ser.write(b'AT+CMGS="' \+ recipient\_number.encode() \+ b'"\\r'), where recipient\_number is the phone number of the message recipient. After this command is sent, the modem typically returns a prompt ("\>"), indicating it is ready for the message content. The message content is then sent using ser.write(message\_content.encode() \+ b'\\r'), where message\_content is the text of the SMS. Finally, the end of the message is indicated by sending the Ctrl+Z character (ASCII 26\) using ser.write(bytes()). After sending the message, the Python application should wait for the modem's response, which usually includes "OK" if the message was sent successfully or an error message if there was an issue.

Receiving SMS messages involves a similar initial step of setting the modem to text mode: ser.write(b'AT+CMGF=1\\r'). To check for new, unread messages, the command ser.write(b'AT+CMGL="REC UNREAD"\\r') is sent. The modem's response to this command will contain a list of any unread messages, including information such as the sender's phone number, the date and time the message was received, and a brief indication of the message content. The Python application needs to read and parse this response to extract the relevant details. Regular expressions can be a useful tool for parsing the modem's output.35 To read the full content of a specific message, the AT+CMGR=\<index\> command is used, where \<index\> is the index of the message as reported by the AT+CMGL command. The modem will then respond with the full text of the message.

When working with SMS, there are two primary modes: text mode and PDU (Protocol Data Unit) mode. Text mode, as described above, is generally simpler to use for basic SMS sending and receiving, as the message content is handled as plain text. However, text mode has limitations in terms of the features it supports. PDU mode, on the other hand, is more complex as it requires encoding messages into a specific PDU format before sending and decoding received PDUs back into readable text. PDU mode offers more advanced features, such as the ability to handle different character encodings and to receive delivery reports. The choice between text mode and PDU mode depends on the specific requirements of the application. For most basic SMS tasks, text mode is often sufficient and easier to implement. The consistent use of text mode commands in the research material's examples for sending and receiving SMS 12 suggests that this is a standard and generally effective approach for interacting with GSM modems for SMS. It is important to always check the modem's response after sending each AT command to ensure that the command was executed successfully and to handle any potential errors gracefully.

**7\. Continuous Monitoring for Incoming SMS Messages:**

For applications that require real-time processing of incoming SMS messages, continuous monitoring of the serial port connected to the Novatel USB551L modem is necessary. Several techniques can be employed to achieve this.

One approach is **polling**, where the Python application periodically sends the AT+CMGL="REC UNREAD" command to the modem to check if any new, unread messages have been received. While this method is relatively straightforward to implement, it can be inefficient as it consumes system resources and might introduce a delay between the time a message is received by the modem and the time it is detected by the application.

A more efficient method is to use an **interrupt-driven approach** by configuring the modem to send an unsolicited notification when a new SMS message arrives.11 The AT+CNMI command is used for this purpose. By setting the appropriate parameters for AT+CNMI, the modem can be instructed to send a notification, such as \+CMTI: \<mem\>,\<index\>, upon the reception of a new SMS. The Python application can then continuously listen for these notifications on the serial port and, upon receiving one, can immediately read the new message using the AT+CMGR=\<index\> command. This method is generally preferred for applications that need to react to incoming messages with minimal delay.

Another common technique for continuous monitoring involves using **threading**. A separate thread can be created within the Python application that runs a continuous loop to read data from the serial port using methods like ser.readline().54 This thread can then parse any incoming data to check for unsolicited notifications (like \+CMTI) or, if using a polling approach, can periodically send the AT+CMGL command. When a new message is detected, the monitoring thread can then signal the main application thread or store the message data in a queue for processing. Using a separate thread ensures that the main application remains responsive and is not blocked while waiting for incoming messages from the serial port. Proper synchronization mechanisms, such as queues or locks, might be required to safely share data between the monitoring thread and the main application.

For more complex applications, **asynchronous programming** using libraries like asyncio and aioserial can provide a non-blocking way to handle serial communication.63 Asynchronous I/O allows the application to perform other tasks while waiting for data from the serial port, which can be particularly beneficial for applications with graphical user interfaces or those that need to handle multiple concurrent operations.

When configuring the modem to send unsolicited notifications using the AT+CNMI command, it is the most efficient way to achieve real-time monitoring compared to constantly querying the modem.31 This approach reduces the latency in detecting new messages and minimizes unnecessary communication overhead. When employing threads for monitoring the serial port, it is essential to carefully manage thread safety and the sharing of data between different threads.54 Utilizing queues is a common and effective strategy for passing received SMS messages from the monitoring thread to the main application thread for further processing, ensuring that data is handled securely and without the risk of race conditions.

**8\. Automated SMS Forwarding:**

Once the Python application is capable of receiving SMS messages from the Novatel USB551L modem, it can be configured to automatically forward these messages to another destination. Two primary methods for automated forwarding are to send the message content to an email address or to forward it to another phone number.

Forwarding received SMS messages to an **email address** can be implemented using Python's built-in smtplib library.36 This involves extracting the sender's phone number and the content of the SMS from the received message. Then, an email is created using the email.mime.text module, with the SMS content as the body, the sender's number potentially in the subject or body, and the recipient email address. The smtplib library is used to establish a connection to an SMTP (Simple Mail Transfer Protocol) server, authenticate if necessary, and send the email. This requires configuring the SMTP server details, such as the host address, port number, username, and password. For users with Gmail accounts, it might be necessary to generate and use an App Password instead of the regular Gmail password if two-factor authentication is enabled.67

Forwarding an SMS message to **another phone number** can be achieved either by using the same Novatel USB551L modem to send a new SMS or by utilizing an external SMS gateway service.75 To use the same modem, the Python application would need to construct and send an SMS message using the AT+CMGS command, as described in Section 6, with the recipient number being the target forwarding number and the message content being the content of the originally received SMS. Alternatively, numerous SMS gateway services are available that provide APIs (typically RESTful) which can be accessed from Python to send SMS messages to any valid phone number. These services often handle the underlying complexities of SMS delivery and can offer additional features like delivery reports. However, using an SMS gateway service might incur costs depending on the service provider and the volume of messages sent.

Forwarding SMS messages to an email address via Python's smtplib offers a relatively simple and widely accessible method for archiving and accessing SMS messages.36 This approach requires access to an SMTP server, but many email providers, including Gmail, offer SMTP access. When considering forwarding SMS to another phone number programmatically, utilizing an SMS gateway service might be a more robust and scalable solution, especially for applications that require high reliability or need to send a large volume of forwarded messages.75 While these services typically involve costs, they often provide better infrastructure and features compared to relying solely on the same modem for forwarding.

**9\. Troubleshooting and Best Practices:**

Integrating the Novatel USB551L modem for SMS messaging with a Python application can sometimes present challenges. Common issues encountered during the integration process include incorrect identification of the serial port assigned to the modem, using the wrong baud rate or other serial port communication parameters, difficulties in installing the necessary drivers for the operating system, the modem not responding to AT commands sent by the application, problems related to the SIM card (such as it not being inserted correctly, being PIN-locked, or not having an active service plan), an incorrectly configured SMS service center address, issues with encoding or decoding the message content, timeouts occurring during serial communication, and problems with the VZAccess Manager software, such as it hanging or failing to establish a connection.25

To ensure reliable SMS communication, several best practices should be followed. First, it is crucial to verify the correct serial port that the modem is using and to configure the baud rate and other serial port parameters (like parity, stop bits, and data bits) in the Python script to match the modem's settings. Ensuring that the correct drivers for the Novatel USB551L are installed for the specific operating system is also essential for proper communication. Before attempting to send or receive SMS messages, it is advisable to test basic communication with the modem by sending simple AT commands, such as AT to check if the modem responds with "OK", or ATI to retrieve modem identification information. The status of the SIM card should be checked to ensure it is properly inserted, unlocked if necessary, and has an active service plan with the carrier. The SMS service center address might need to be configured based on the carrier being used. It is important to handle the modem's responses to AT commands and any potential error messages within the Python code to ensure robust operation. Implementing appropriate timeouts for serial read operations can prevent the application from getting stuck while waiting for a response from the modem. Using a logging mechanism to record the communication between the Python application and the modem can be invaluable for debugging purposes. Linux users with newer kernels should consider exploring the use of libqmi and ModemManager as potentially more reliable alternatives to AT commands for communicating with the modem.20 Updating the modem's firmware to the latest available version, if possible, can often resolve known bugs and improve the overall performance and stability of the device.4 Finally, ensuring that the modem is operating in the correct mode, such as LAN mode instead of Windows Mobile Broadband mode, as sometimes required by certain applications, is an important configuration step.24

The reported instability and issues with VZAccess Manager 25 suggest that while it is a primary tool for initial setup and driver installation, developers should be prepared to troubleshoot problems associated with this software or to consider alternative methods for direct interaction with the modem, particularly for automated or long-running applications where the reliability of VZAccess Manager might be a concern. The recommendation for Linux users to explore libqmi and ModemManager 20 for newer kernels indicates a potential shift towards more native and possibly more stable communication protocols on that platform, offering an alternative to the more universal but sometimes less efficient AT command interface.

**10\. Conclusion:**

In summary, the Novatel USB551L modem can be successfully integrated with a Python application to enable SMS messaging capabilities through the use of AT commands and the PySerial library. Despite being an older model, it provides the necessary features for sending, receiving, and forwarding SMS messages. Key aspects for successful integration include careful driver installation tailored to the specific operating system, accurate configuration of the serial port parameters, and proper handling of the communication flow with the modem, including sending commands and reading responses.

Potential applications for this integration are diverse and include the development of SMS-based notification systems for various events, remote monitoring and control solutions where SMS can serve as a communication channel, and integration with Internet of Things (IoT) devices that require cellular-based SMS functionality.

Future work could involve exploring the use of libqmi and ModemManager on Linux systems for potentially more advanced and reliable modem control. Investigating the implementation of PDU mode for SMS communication could unlock enhanced features beyond basic text messaging. Additionally, developing a robust error handling and logging mechanism within the Python application is crucial for ensuring the stability and reliability of the integrated system, especially when considering deployment in production environments.

#### **Works cited**

1. linux/drivers/usb/serial/option.c at master \- GitHub, accessed May 1, 2025, [https://github.com/torvalds/linux/blob/master/drivers/usb/serial/option.c](https://github.com/torvalds/linux/blob/master/drivers/usb/serial/option.c)  
2. Novatel Verizon 551L 4G/LTE/3G Cat 4 USB Dongle Modem \- Embedded Works, accessed May 1, 2025, [https://embeddedworks.net/product/wwan531/](https://embeddedworks.net/product/wwan531/)  
3. Verizon Wireless 4G LTE Modem Novatel USB551L, accessed May 1, 2025, [https://www.4gltemall.com/verizon-wireless-4g-lte-modem-novatel-usb551l.html](https://www.4gltemall.com/verizon-wireless-4g-lte-modem-novatel-usb551l.html)  
4. Getting a Novatel USB551L working in Linux by using Windows | CynicalSignals, accessed May 1, 2025, [https://www.cynicalsignals.com/getting-a-novatel-usb551l-working-in-linux-by-using-windows/](https://www.cynicalsignals.com/getting-a-novatel-usb551l-working-in-linux-by-using-windows/)  
5. High-Speed Novatel 551L USB Modem \- Affordable 4G LTE on Excel Wireless, accessed May 1, 2025, [https://excel-wireless.com/product/novatel-551l-usb-modem-verizon-4g-lte/](https://excel-wireless.com/product/novatel-551l-usb-modem-verizon-4g-lte/)  
6. The Verizon USB551L 4G LTE Modem Lands on the Verizon ..., accessed May 1, 2025, [https://inseego.com/company/press-releases/verizon-usb551l-4g-lte-modem-lands-verizon-wireless-network/](https://inseego.com/company/press-releases/verizon-usb551l-4g-lte-modem-lands-verizon-wireless-network/)  
7. Novatel USB551L 4G USB Modem for Verizon Review \- YouTube, accessed May 1, 2025, [https://www.youtube.com/watch?v=fxDoZNdPm3o](https://www.youtube.com/watch?v=fxDoZNdPm3o)  
8. Novatel USB551L 4G USB Modem for Verizon Review \- PhoneArena, accessed May 1, 2025, [https://www.phonearena.com/reviews/Novatel-USB551L-4G-USB-Modem-for-Verizon-Review\_id2719](https://www.phonearena.com/reviews/Novatel-USB551L-4G-USB-Modem-for-Verizon-Review_id2719)  
9. Novatel Wireless USB551L \- Verizon 4G LTE: Two Datacards and a WiFi Hotspot Massively Reviewed \- AnandTech, accessed May 1, 2025, [https://www.anandtech.com/show/4289/verizon-4g-lte-two-datacards-wifi-hotspot-massively-reviewed/5](https://www.anandtech.com/show/4289/verizon-4g-lte-two-datacards-wifi-hotspot-massively-reviewed/5)  
10. AT Commands \- Blue Security Blog \- WordPress.com, accessed May 1, 2025, [https://bluesecblog.wordpress.com/2016/11/11/at-commands/](https://bluesecblog.wordpress.com/2016/11/11/at-commands/)  
11. AT Commands, GSM AT command set \- Engineers Garage, accessed May 1, 2025, [https://www.engineersgarage.com/at-commands-gsm-at-command-set/](https://www.engineersgarage.com/at-commands-gsm-at-command-set/)  
12. Sending SMS with a USB GSM modem (and Python) \- cylab.be, accessed May 1, 2025, [https://cylab.be/blog/337/sending-sms-with-a-usb-gsm-modem-and-python](https://cylab.be/blog/337/sending-sms-with-a-usb-gsm-modem-and-python)  
13. What are AT Commands? AT Commands 2024 Guide \- Cavli Wireless, accessed May 1, 2025, [https://www.cavliwireless.com/blog/nerdiest-of-things/an-introduction-to-cellular-at-commands.html](https://www.cavliwireless.com/blog/nerdiest-of-things/an-introduction-to-cellular-at-commands.html)  
14. www.verizon.com, accessed May 1, 2025, [https://www.verizon.com/content/dam/support/pdf/user\_guide/u620-at-command-reference-guide-7-17-15.pdf](https://www.verizon.com/content/dam/support/pdf/user_guide/u620-at-command-reference-guide-7-17-15.pdf)  
15. MiFi 4G LTE Global USB Modem U620L AT Command Reference Guide \- Verizon, accessed May 1, 2025, [https://espanol.verizon.com/content/dam/support/pdf/user\_guide/u620-at-command-reference-guide-7-17-15.pdf](https://espanol.verizon.com/content/dam/support/pdf/user_guide/u620-at-command-reference-guide-7-17-15.pdf)  
16. 25.7. Example Demonstrating How to Use the \+CMGS AT Command to Send SMS Text Messages in SMS PDU Mode, accessed May 1, 2025, [https://www.developershome.com/sms/cmgsCommand6.asp](https://www.developershome.com/sms/cmgsCommand6.asp)  
17. Download USB Drivers | NovAtel, accessed May 1, 2025, [https://novatel.com/support/support-materials/usb-drivers](https://novatel.com/support/support-materials/usb-drivers)  
18. USB Drivers | NovAtel, accessed May 1, 2025, [https://novatel.com/products/firmware-options-pc-software/usb-drivers](https://novatel.com/products/firmware-options-pc-software/usb-drivers)  
19. Software Downloads | NovAtel, accessed May 1, 2025, [https://novatel.com/support/support-materials/software-downloads](https://novatel.com/support/support-materials/software-downloads)  
20. Script for making our Novatel 551L connect to Verizon \- GitHub Gist, accessed May 1, 2025, [https://gist.github.com/b5e9fd54a1c0845aef43](https://gist.github.com/b5e9fd54a1c0845aef43)  
21. Verizon USB551L 4G Stick on Linux \- Super User, accessed May 1, 2025, [https://superuser.com/questions/516400/verizon-usb551l-4g-stick-on-linux](https://superuser.com/questions/516400/verizon-usb551l-4g-stick-on-linux)  
22. Data Card Software: VZAccess Manager \- Verizon 4G LTE: Two Datacards and a WiFi Hotspot Massively Reviewed \- AnandTech, accessed May 1, 2025, [https://www.anandtech.com/show/4289/verizon-4g-lte-two-datacards-wifi-hotspot-massively-reviewed/6](https://www.anandtech.com/show/4289/verizon-4g-lte-two-datacards-wifi-hotspot-massively-reviewed/6)  
23. USER MANUAL \- Inseego, accessed May 1, 2025, [https://inseego.com/download/userguide-ovation-mc551-usb-modem.pdf](https://inseego.com/download/userguide-ovation-mc551-usb-modem.pdf)  
24. PocketPORT Modem Guide: Novatel Wireless USB551L (Verizon LTE/4G) \- Proxicast, accessed May 1, 2025, [https://win.proxicast.com/support/PocketPORT\_Modem\_Guide\_CSCart.asp?TID=16](https://win.proxicast.com/support/PocketPORT_Modem_Guide_CSCart.asp?TID=16)  
25. Frustrating connection problem with Novatel USB551L 4G USB Modem, accessed May 1, 2025, [https://community.verizon.com/t5/Broadband-Netbook-Archive/Frustrating-connection-problem-with-Novatel-USB551L-4G-USB-Modem/m-p/310765](https://community.verizon.com/t5/Broadband-Netbook-Archive/Frustrating-connection-problem-with-Novatel-USB551L-4G-USB-Modem/m-p/310765)  
26. USB 551L keeps disconnecting me constantly, very annoying, Please Help\!, accessed May 1, 2025, [https://community.verizon.com/t5/Broadband-Netbook-Archive/USB-551L-keeps-disconnecting-me-constantly-very-annoying-Please/td-p/356345](https://community.verizon.com/t5/Broadband-Netbook-Archive/USB-551L-keeps-disconnecting-me-constantly-very-annoying-Please/td-p/356345)  
27. USB551l firmware update \- Verizon Community Forums, accessed May 1, 2025, [https://community.verizon.com/t5/4G-LTE-LTE-Advanced/USB551l-firmware-update/m-p/262237](https://community.verizon.com/t5/4G-LTE-LTE-Advanced/USB551l-firmware-update/m-p/262237)  
28. docs/docs/mini/3g4g.md at master · domino-team/docs \- GitHub, accessed May 1, 2025, [https://github.com/domino-team/docs/blob/master/docs/mini/3g4g.md](https://github.com/domino-team/docs/blob/master/docs/mini/3g4g.md)  
29. Sending a SMS with Python and pyserial via an USB 3G Modem on pfSense/FreeBSD, accessed May 1, 2025, [https://gist.github.com/mrf345/13781a4103f3da356597d11e73db8f1f](https://gist.github.com/mrf345/13781a4103f3da356597d11e73db8f1f)  
30. python \- How to Send/Receive SMS using AT commands? \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/2161197/how-to-send-receive-sms-using-at-commands](https://stackoverflow.com/questions/2161197/how-to-send-receive-sms-using-at-commands)  
31. Send and Receive SMS Messages Using Raspberry Pi and Python – My Blog, accessed May 1, 2025, [https://hristoborisov.com/index.php/projects/turning-the-raspberry-pi-into-a-sms-center-using-python/](https://hristoborisov.com/index.php/projects/turning-the-raspberry-pi-into-a-sms-center-using-python/)  
32. Send SMS via a USB Dongle/Python \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/18823594/send-sms-via-a-usb-dongle-python](https://stackoverflow.com/questions/18823594/send-sms-via-a-usb-dongle-python)  
33. MC7700 unable to send SMS \- MC/EM Series \- Sierra Wireless Forum, accessed May 1, 2025, [https://forum.sierrawireless.com/t/mc7700-unable-to-send-sms/29116](https://forum.sierrawireless.com/t/mc7700-unable-to-send-sms/29116)  
34. python-gsmmodem-new \- PyPI, accessed May 1, 2025, [https://pypi.org/project/python-gsmmodem-new/](https://pypi.org/project/python-gsmmodem-new/)  
35. python-serial-sms/sms.py at master \- GitHub, accessed May 1, 2025, [https://github.com/GorioB/python-serial-sms/blob/master/sms.py](https://github.com/GorioB/python-serial-sms/blob/master/sms.py)  
36. GSM, SMS, Email \- Python Exemplarisch, accessed May 1, 2025, [https://www.python-exemplarisch.ch/index\_en.php?inhalt\_links=navigation\_en.inc.php\&inhalt\_mitte=raspi/en/gsm.inc.php](https://www.python-exemplarisch.ch/index_en.php?inhalt_links=navigation_en.inc.php&inhalt_mitte=raspi/en/gsm.inc.php)  
37. communication with modem using pyserial \- python \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/12615963/communication-with-modem-using-pyserial](https://stackoverflow.com/questions/12615963/communication-with-modem-using-pyserial)  
38. Communicating with GSM modems using PySerial in python \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/30950701/communicating-with-gsm-modems-using-pyserial-in-python](https://stackoverflow.com/questions/30950701/communicating-with-gsm-modems-using-pyserial-in-python)  
39. How to send and receive serial in python, accessed May 1, 2025, [https://discuss.python.org/t/how-to-send-and-receive-serial-in-python/10394](https://discuss.python.org/t/how-to-send-and-receive-serial-in-python/10394)  
40. How can I retrieve the text sms from a modem device with Python? \- Digi forum, accessed May 1, 2025, [https://forums.digi.com/t/how-can-i-retrieve-the-text-sms-from-a-modem-device-with-python/11932](https://forums.digi.com/t/how-can-i-retrieve-the-text-sms-from-a-modem-device-with-python/11932)  
41. Send SMS msg via Serial port to GSM Modem \- Ignition \- Inductive Automation Forum, accessed May 1, 2025, [https://forum.inductiveautomation.com/t/send-sms-msg-via-serial-port-to-gsm-modem/53686](https://forum.inductiveautomation.com/t/send-sms-msg-via-serial-port-to-gsm-modem/53686)  
42. MortadhaDAHMANI/Py-SIM800L-SMS: SMS sender with AT Commands \- PySerial \- python environment \- GitHub, accessed May 1, 2025, [https://github.com/MortadhaDAHMANI/Py-SIM800L-SMS](https://github.com/MortadhaDAHMANI/Py-SIM800L-SMS)  
43. How to use PySerial to send data to my Arduino Uno with GSM Shield attached, so that an SMS can be sent out? \- Reddit, accessed May 1, 2025, [https://www.reddit.com/r/arduino/comments/7sd77x/how\_to\_use\_pyserial\_to\_send\_data\_to\_my\_arduino/](https://www.reddit.com/r/arduino/comments/7sd77x/how_to_use_pyserial_to_send_data_to_my_arduino/)  
44. retreive sms via python, serial connection and AT commands \- GitHub, accessed May 1, 2025, [https://gist.github.com/underdoeg/1262332](https://gist.github.com/underdoeg/1262332)  
45. AT commands with pyserial not working with receiving sms \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/19067647/at-commands-with-pyserial-not-working-with-receiving-sms](https://stackoverflow.com/questions/19067647/at-commands-with-pyserial-not-working-with-receiving-sms)  
46. AT Commands with the raspberry \[solved\], accessed May 1, 2025, [https://forums.raspberrypi.com/viewtopic.php?t=183796](https://forums.raspberrypi.com/viewtopic.php?t=183796)  
47. Spitz AX \- Issues with Sending AT Commands \- 4G LTE, 5G NR, USB modems \- GL.iNet, accessed May 1, 2025, [https://forum.gl-inet.com/t/spitz-ax-issues-with-sending-at-commands/40915](https://forum.gl-inet.com/t/spitz-ax-issues-with-sending-at-commands/40915)  
48. colins44/gsm-sms: A small python script that allows you to send, receive and delete SMS's using a USB GSM dongle modem \- GitHub, accessed May 1, 2025, [https://github.com/colins44/gsm-sms](https://github.com/colins44/gsm-sms)  
49. Use pyGSM to manage SMS \- Acme Systems, accessed May 1, 2025, [https://www.acmesystems.it/pygsm](https://www.acmesystems.it/pygsm)  
50. How to send and receive SMS from python using usb modem? \[closed\] \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/22975211/how-to-send-and-receive-sms-from-python-using-usb-modem](https://stackoverflow.com/questions/22975211/how-to-send-and-receive-sms-from-python-using-usb-modem)  
51. Python Serial Port Communication Between PC and Arduino Using PySerial Library, accessed May 1, 2025, [https://www.instructables.com/Python-Serial-Port-Communication-Between-PC-and-Ar/](https://www.instructables.com/Python-Serial-Port-Communication-Between-PC-and-Ar/)  
52. serial input/output \- Python Forum, accessed May 1, 2025, [https://python-forum.io/thread-35891.html](https://python-forum.io/thread-35891.html)  
53. Serial read with python, accessed May 1, 2025, [https://discuss.python.org/t/serial-read-with-python/16925](https://discuss.python.org/t/serial-read-with-python/16925)  
54. python monitoring over serial port \- pyserial \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/911089/python-monitoring-over-serial-port](https://stackoverflow.com/questions/911089/python-monitoring-over-serial-port)  
55. pySerial API, accessed May 1, 2025, [https://pyserial.readthedocs.io/en/latest/pyserial\_api.html](https://pyserial.readthedocs.io/en/latest/pyserial_api.html)  
56. python and serial. how to send a message and receive an answer \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/16701401/python-and-serial-how-to-send-a-message-and-receive-an-answer](https://stackoverflow.com/questions/16701401/python-and-serial-how-to-send-a-message-and-receive-an-answer)  
57. pygsm/lib/pygsm/gsmmodem.py at master · rapidsms/pygsm \- GitHub, accessed May 1, 2025, [https://github.com/rapidsms/pygsm/blob/master/lib/pygsm/gsmmodem.py](https://github.com/rapidsms/pygsm/blob/master/lib/pygsm/gsmmodem.py)  
58. Need help using Pyserial \- Python Forum, accessed May 1, 2025, [https://python-forum.io/thread-35914.html](https://python-forum.io/thread-35914.html)  
59. Python sending sms using serial port from RaspberryPi, accessed May 1, 2025, [https://forums.raspberrypi.com/viewtopic.php?t=110748](https://forums.raspberrypi.com/viewtopic.php?t=110748)  
60. pyserial/examples/at\_protocol.py at master \- GitHub, accessed May 1, 2025, [https://github.com/pyserial/pyserial/blob/master/examples/at\_protocol.py](https://github.com/pyserial/pyserial/blob/master/examples/at_protocol.py)  
61. Python Serial port event \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/62836625/python-serial-port-event](https://stackoverflow.com/questions/62836625/python-serial-port-event)  
62. Using a serial read to trigger further events \- Raspberry Pi Forums, accessed May 1, 2025, [https://forums.raspberrypi.com/viewtopic.php?t=196300](https://forums.raspberrypi.com/viewtopic.php?t=196300)  
63. Receive serial data without polling using Python \! \- Raspberry Pi Forums, accessed May 1, 2025, [https://forums.raspberrypi.com/viewtopic.php?t=285986](https://forums.raspberrypi.com/viewtopic.php?t=285986)  
64. Verizon Wireless Activation Procedure, accessed May 1, 2025, [https://cdn.logic-control.com/docs/win-911/Win911%20Manual/Activation%20Guide.pdf](https://cdn.logic-control.com/docs/win-911/Win911%20Manual/Activation%20Guide.pdf)  
65. Function to Continuasly monitor serial port and control other functions \- Python Forum, accessed May 1, 2025, [https://python-forum.io/thread-8687.html](https://python-forum.io/thread-8687.html)  
66. How to implement serial event detection for request /response operation with Tkinter, accessed May 1, 2025, [https://raspberrypi.stackexchange.com/questions/117810/how-to-implement-serial-event-detection-for-request-response-operation-with-tki](https://raspberrypi.stackexchange.com/questions/117810/how-to-implement-serial-event-detection-for-request-response-operation-with-tki)  
67. Get Started with Forward SMS to Email using Python | Plivo Docs, accessed May 1, 2025, [https://www.plivo.com/docs/messaging/use-cases/forward-sms-to-email/python/](https://www.plivo.com/docs/messaging/use-cases/forward-sms-to-email/python/)  
68. Forwarding Texts to Email | SignalWire Developer Portal, accessed May 1, 2025, [https://developer.signalwire.com/compatibility-api/guides/messaging/python/text-to-email/](https://developer.signalwire.com/compatibility-api/guides/messaging/python/text-to-email/)  
69. Sending and Forwarding Messages \- Aspose Documentation, accessed May 1, 2025, [https://docs.aspose.com/email/python-net/sending-and-forwarding-messages/](https://docs.aspose.com/email/python-net/sending-and-forwarding-messages/)  
70. Simple Python daemon for forwarding SMS to email, tested on Samsung and Nokia phones, accessed May 1, 2025, [https://gist.github.com/5852092](https://gist.github.com/5852092)  
71. How to forward email using Python \- smtp \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/8542021/how-to-forward-email-using-python](https://stackoverflow.com/questions/8542021/how-to-forward-email-using-python)  
72. Forwarding an email with python smtplib \- Stack Overflow, accessed May 1, 2025, [https://stackoverflow.com/questions/2717196/forwarding-an-email-with-python-smtplib](https://stackoverflow.com/questions/2717196/forwarding-an-email-with-python-smtplib)  
73. How to send text messages for free using Python \- Alfredo Sequeida, accessed May 1, 2025, [https://www.alfredosequeida.com/blog/how-to-send-text-messages-for-free-using-python-use-python-to-send-text-messages-via-email/](https://www.alfredosequeida.com/blog/how-to-send-text-messages-for-free-using-python-use-python-to-send-text-messages-via-email/)  
74. Send SMS Messages For FREE Using Email To SMS Gateway In Python \- YouTube, accessed May 1, 2025, [https://www.youtube.com/watch?v=3247tuklMEk](https://www.youtube.com/watch?v=3247tuklMEk)  
75. A Python client library for sending and managing SMS messages via the SMS Gateway for Android™ API \- GitHub, accessed May 1, 2025, [https://github.com/android-sms-gateway/client-py](https://github.com/android-sms-gateway/client-py)  
76. Send SMS and WhatsApp with Python – Mobile as SMS Gateway \- SMSMobileAPI, accessed May 1, 2025, [https://smsmobileapi.com/python/](https://smsmobileapi.com/python/)  
77. How to Send SMS Messages Using Python SMS API? | SMSAPI Blog, accessed May 1, 2025, [https://www.smsapi.com/blog/send-sms-python-api/](https://www.smsapi.com/blog/send-sms-python-api/)  
78. SMSgate is an open source Python-based server for sending and especially receiving SMS using multiple GSM modems and SIM cards. \- GitHub, accessed May 1, 2025, [https://github.com/pentagridsec/smsgate](https://github.com/pentagridsec/smsgate)  
79. smsframework \- PyPI, accessed May 1, 2025, [https://pypi.org/project/smsframework/](https://pypi.org/project/smsframework/)  
80. Send SMS Using Python | Textmagic SMS API, accessed May 1, 2025, [https://www.textmagic.com/docs/api/python/](https://www.textmagic.com/docs/api/python/)  
81. Sending SMS Messages with Python: A Step-by-Step Guide \- GatewayAPI, accessed May 1, 2025, [https://gatewayapi.com/blog/sending-sms-messages-with-python-a-step-by-step-guide/](https://gatewayapi.com/blog/sending-sms-messages-with-python-a-step-by-step-guide/)  
82. No-cost way to use Python to send text messages? (Not for marketing/spam, I promise), accessed May 1, 2025, [https://www.reddit.com/r/learnpython/comments/1crx2yv/nocost\_way\_to\_use\_python\_to\_send\_text\_messages/](https://www.reddit.com/r/learnpython/comments/1crx2yv/nocost_way_to_use_python_to_send_text_messages/)